import { create } from 'zustand';

interface GlobalState {
  isLoadedMap: boolean;
}

export interface GlobalStore extends GlobalState {
  setIsLoadedMap: (args: GlobalState['isLoadedMap']) => void;
}

const initialState: Pick<GlobalStore, keyof GlobalState> = {
  isLoadedMap: false,
};

const useGlobalStore = create<GlobalStore>()(set => ({
  ...initialState,
  setIsLoadedMap: isLoadedMap => {
    set(() => ({ isLoadedMap }));
  },
}));

export default useGlobalStore;
