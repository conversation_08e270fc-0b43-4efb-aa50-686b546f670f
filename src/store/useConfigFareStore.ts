import { create } from 'zustand';

interface FareState {
  isShowErrorStore: boolean;
  isDisableUpdateStore: boolean;
}

export interface FareStore extends FareState {
  setIsShowErrorStore: (args: FareState['isShowErrorStore']) => void;
  setDisableUpdateStore: (args: FareState['isDisableUpdateStore']) => void;
}

const initialState: Pick<FareStore, keyof FareState> = {
  isDisableUpdateStore: false,
  isShowErrorStore: false,
};

const useConfigFareStore = create<FareStore>()(set => ({
  ...initialState,

  setDisableUpdateStore: isDisableUpdateStore => {
    set(() => ({ isDisableUpdateStore }));
  },
  setIsShowErrorStore: isShowErrorStore => {
    set(() => ({ isShowErrorStore }));
  },
}));

export default useConfigFareStore;
