import { Navigate } from 'react-router-dom';
import BookingHistory from '#/features/booking-history/pages/BookingHistory';
import BookingHistoryDetailByCustomer from '#/features/booking-history/pages/BookingHistoryDetailPage';
import BookingHistoryDetailOfCustomer from '#/features/booking-history/pages/BookingHistoryDetailOfCustomer';
import Coupons from '#/features/coupon/pages/Coupons';
import CouponDetail from '#/features/coupon/pages/CouponDetail';
import { CustomerBusinessDetail } from '#/features/customer/pages/CustomerBusinessDetail';
import Customers from '#/features/customer/pages/Customers';
import DashboardPage from '#/features/dashboard/overview/DashboardPage';
import { DepartmentDetail } from '#/features/department/pages/DepartmentDetail';
import { NavbarKey } from '#/shared/constants';
import PrivateLayout from '#/shared/layouts/PrivateLayout';
import BookingHistoryDetailOfDepartment from '#/features/booking-history/pages/BookingHistoryDetailOfDepartment';
import { DepartmentDetailTab } from '#/features/department/constant';
import { EmployeeDetail } from '#/features/employee/pages/EmployeeDetail';
import BookingHistoryDetailOfEmployee from '#/features/booking-history/pages/BookingHistoryDetailOfEmployee';
import LoyalPoints from '#/features/loyal-point/pages/LoyalPoints';
import Subscription from '#/features/subscription/pages/Subscription';
import SubscriptionDetail from '#/features/subscription/pages/SubscriptionDetail';
import Notification from '#/features/notification/pages/Notification';
import Location from '#/features/configuration/location/pages/Location';
import Banner from '#/features/configuration/banner/pages/Banner';
import BannerDetail from '#/features/configuration/banner/pages/BannerDetail';
import SupportTicket from '#/features/support-ticket/pages/SupportTicket';
import Task from '#/features/task/pages/Task';
import ServiceFeePage from '#/features/fee/pages/ServiceFee';
import ServiceFeeDetail from '#/features/fee/pages/ServiceFeeDetail';
import RateDetail from '#/features/fee/pages/RateDetail';
import DepartmentManagementPage from '#/features/department-management/pages/DepartmentManagementPage';
import DepartmentManagementDetail from '#/features/department-management/pages/DepartmentManagementDetail';
import ZoneManagement from '#/features/zone/pages/ZoneManagement';
import ZoneManagementDetail from '#/features/zone/pages/ZoneManagementDetail';
import RatingAdmin from '#/features/rating-history/pages/RatingAdmin';
import NotificationDetail from '#/features/notification/pages/NotificationDetail';
import { CouponInSubscriptionDetail } from '#/features/subscription/pages/CouponDetail';
import LocationDetail from '#/features/configuration/location/pages/LocationDetail';
import EmployeeInternal from '#/features/employee-management/pages/employee-internal/EmployeeInternal';
import DriverExternalDetail from '#/features/employee-management/pages/driver-external/DriverExternalDetail';
import EmployeeInternalDetail from '#/features/employee-management/pages/employee-internal/EmployeeInternalDetail';
import DriverExternal from '#/features/employee-management/pages/driver-external/DriverExternal';
import BookingSource from '#/features/dashboard/bookingsource/pages/BookingSource';
import HeatMap from '#/features/dashboard/heatmap/pages/Heatmap';
import Permission from '#/features/permission/pages/Permission';
import PermissionDetail from '#/features/permission/pages/PermissionDetail';
import PageNotFound from '#/features/page-not-found/PageNotFound';
import AccessDeniedPage from '#/features/access-denied/AccessDenied';
import FleetPage from '#/features/fleet/pages/FleetPage';
import FleetDetail from '#/features/fleet/pages/FleetDetail';
import NewsPage from '#/features/news/pages/NewsPage';
import NewsPageDetail from '#/features/news/pages/NewsPageDetail';
import FixedFareDetail from '#/features/fee/pages/FixedFareDetail';

const dashboardRouter = [
  {
    element: (
      <Navigate replace to={`/${NavbarKey.Dashboard}/${NavbarKey.Overview}`} />
    ),
    path: `/${NavbarKey.Dashboard}`,
  },
  {
    element: <DashboardPage />,
    path: `/${NavbarKey.Dashboard}/${NavbarKey.Overview}`,
  },
  {
    element: <BookingSource />,
    path: `/${NavbarKey.Dashboard}/${NavbarKey.BookingSource}`,
  },
  {
    element: <HeatMap />,
    path: `/${NavbarKey.Dashboard}/${NavbarKey.Heatmap}`,
  },
];

const customerRouter = [
  {
    element: <Customers />,
    path: `/${NavbarKey.Customer}`,
  },
  {
    element: <Customers />,
    path: `/${NavbarKey.Customer}/:tab`,
  },
  {
    element: <CustomerBusinessDetail />,
    path: `/${NavbarKey.Customer}/:tab/:id`,
  },
  {
    element: <CustomerBusinessDetail />,
    path: `/${NavbarKey.Customer}/:tab/:id/:detailTab`,
  },
  {
    element: <BookingHistoryDetailOfCustomer />,
    path: `/${NavbarKey.Customer}/:tab/:id/:detailTab/booking-detail/:bookingId`,
  },
  //TODO: check this
  {
    element: <DepartmentDetail />,
    path: `/${NavbarKey.Customer}/:tab/:id/:detailTab/department-detail/:departmentId`,
  },
  {
    element: <DepartmentDetail />,
    path: `/${NavbarKey.Customer}/:tab/:id/:detailTab/department-detail/:departmentId/:departmentDetailTab`,
  },

  {
    element: <BookingHistoryDetailOfDepartment />,
    path: `/${NavbarKey.Customer}/:tab/:id/:detailTab/department-detail/:departmentId/${DepartmentDetailTab.AllBooking}/:bookingDetailId`,
  },

  {
    element: <BookingHistoryDetailOfDepartment />,
    path: `/${NavbarKey.Customer}/:tab/:id/:detailTab/department-detail/:departmentId/${DepartmentDetailTab.DepartmentEmployee}/:employeeId`,
  },

  {
    element: <EmployeeDetail />,
    path: `/${NavbarKey.Customer}/:tab/:id/employees/:employeeId`,
  },

  {
    element: <BookingHistoryDetailOfEmployee />,
    path: `/${NavbarKey.Customer}/:tab/:id/employees/:employeeId/booking-detail/:bookingId`,
  },

  {
    element: <BookingHistoryDetailOfCustomer />,
    path: `/${NavbarKey.Customer}/:tab/:id/:detailTab/employee-detail/:employeeId`,
  },
];

const bookingHistoryRouter = [
  {
    element: <BookingHistory />,
    path: `/${NavbarKey.BookingHistory}`,
  },
  {
    element: <BookingHistory />,
    path: `/${NavbarKey.BookingHistory}/:tab`,
  },
  {
    element: <BookingHistoryDetailByCustomer />,
    path: `/${NavbarKey.BookingHistory}/:tab/:id`,
  },
];

const couponRouter = [
  {
    element: <Coupons />,
    path: `/${NavbarKey.Coupon}`,
  },
  {
    element: <CouponDetail />,
    path: `/${NavbarKey.Coupon}/:id`,
  },
];

const loyalPointsRouter = [
  {
    element: <LoyalPoints />,
    path: `/${NavbarKey.LoyalPoints}`,
  },
];

const subscriptionRouter = [
  {
    element: <Subscription />,
    path: `/${NavbarKey.Subscription}`,
  },
  {
    element: <SubscriptionDetail />,
    path: `/${NavbarKey.Subscription}/:id`,
  },
  {
    element: <CouponInSubscriptionDetail />,
    path: `/${NavbarKey.Subscription}/:id/${NavbarKey.Coupon}/:couponId`,
  },
  {
    element: <SubscriptionDetail />,
    path: `/${NavbarKey.Subscription}/:id/:tab`,
  },
];

const configurationRouter = [
  {
    element: <Location />,
    path: `/${NavbarKey.Configuration}/${NavbarKey.Location}`,
  },
  {
    element: <LocationDetail />,
    path: `/${NavbarKey.Configuration}/${NavbarKey.Location}/:id`,
  },
  {
    element: <Banner />,
    path: `/${NavbarKey.Configuration}/${NavbarKey.Banner}`,
  },
  {
    element: <BannerDetail />,
    path: `/${NavbarKey.Configuration}/${NavbarKey.Banner}/:id`,
  },
];

const feeRouter = [
  {
    element: <ServiceFeePage />,
    path: `/${NavbarKey.Service}/:tab`,
  },
  {
    element: <ServiceFeeDetail />,
    path: `/${NavbarKey.Service}/${NavbarKey.Fee}/:id`,
  },
  {
    element: <RateDetail />,
    path: `/${NavbarKey.Service}/${NavbarKey.Rate}/:id`,
  },
  {
    element: <FixedFareDetail />,
    path: `/${NavbarKey.Service}/${NavbarKey.FixedFare}/:id`,
  },
];

const departmentRoute = [
  {
    element: <DepartmentManagementPage />,
    path: `/${NavbarKey.DepartmentManagement}`,
  },
  {
    element: <DepartmentManagementDetail />,
    path: `/${NavbarKey.DepartmentManagement}/:id`,
  },
];

const zoneRoute = [
  {
    element: <ZoneManagement />,
    path: `/${NavbarKey.Zone}`,
  },
  {
    element: <ZoneManagementDetail />,
    path: `/${NavbarKey.Zone}/:id`,
  },
];

const ratingRouter = [
  {
    element: <RatingAdmin />,
    path: `/${NavbarKey.Rating}`,
  },
];

const notificationRouter = [
  {
    element: <Notification />,
    path: `/${NavbarKey.Notification}`,
  },
  {
    element: <NotificationDetail />,
    path: `/${NavbarKey.Notification}/:id`,
  },
];

const employeeManagementRoute = [
  {
    element: <EmployeeInternal />,
    path: `/${NavbarKey.EmployeeManagement}/${NavbarKey.Employee}`,
  },
  {
    element: <EmployeeInternalDetail />,
    path: `/${NavbarKey.EmployeeManagement}/${NavbarKey.Employee}/:id/:tab`,
  },
  {
    element: <DriverExternal />,
    path: `/${NavbarKey.EmployeeManagement}/${NavbarKey.Driver}`,
  },
  {
    element: <DriverExternalDetail />,
    path: `/${NavbarKey.EmployeeManagement}/${NavbarKey.Driver}/:id/:tab`,
  },
];
const permissionRoute = [
  {
    element: <Permission />,
    path: `/${NavbarKey.Permission}`,
  },
  {
    element: <PermissionDetail />,
    path: `/${NavbarKey.Permission}/:id`,
  },
];

const taskRouter = [
  {
    element: <Task />,
    path: `/${NavbarKey.Task}`,
  },
];

const supportTicketRouter = [
  {
    element: <SupportTicket />,
    path: `/${NavbarKey.Support}`,
  },
];

const permissionDeniedRouter = [
  {
    element: <AccessDeniedPage />,
    path: `/${NavbarKey.PermissionDenied}`,
  },
];

const fleetRouter = [
  {
    element: <FleetPage />,
    path: `/${NavbarKey.Fleet}`,
  },
  {
    element: <FleetDetail />,
    path: `/${NavbarKey.Fleet}/:id/:tab`,
  },
];

const newsRouter = [
  {
    element: <NewsPage />,
    path: `/${NavbarKey.News}`,
  },
  {
    element: <NewsPageDetail />,
    path: `/${NavbarKey.News}/:id`,
  },
];
export const privateRouter = [
  {
    children: [
      ...dashboardRouter,
      ...customerRouter,
      ...bookingHistoryRouter,
      ...couponRouter,
      ...loyalPointsRouter,
      ...subscriptionRouter,
      ...configurationRouter,
      ...feeRouter,
      ...ratingRouter,
      ...notificationRouter,
      ...employeeManagementRoute,
      ...supportTicketRouter,
      ...taskRouter,
      ...departmentRoute,
      ...zoneRoute,
      ...permissionRoute,
      ...permissionDeniedRouter,
      ...fleetRouter,
      ...newsRouter,
    ],
    element: <PrivateLayout />,
    errorElement: <PageNotFound />,
    path: '/',
  },
];
