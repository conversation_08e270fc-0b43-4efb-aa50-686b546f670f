// import { useUsersServiceUserControllerGetUser } from './api/queries';
import { useJsApiLoader } from '@react-google-maps/api';
import Routes from './routers';
import useGlobalStore from './store/useGlobalStore';
import { useEffect } from 'react';

function App() {
  const { setIsLoadedMap } = useGlobalStore();
  const { isLoaded } = useJsApiLoader({
    googleMapsApiKey: import.meta.env.VITE_GOOGLE_MAP_API_KEY,
    libraries: ['visualization'],
  });

  useEffect(() => {
    setIsLoadedMap(isLoaded);
  }, [isLoaded]);

  return (
    <div className="h-screen max-h-screen w-screen max-w-[100vw] flex justify-center overflow-hidden">
      <Routes />
    </div>
  );
}

export default App;
