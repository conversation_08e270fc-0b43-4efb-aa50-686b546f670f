{"bookingHistory": {"title": "Historique des réservations", "businessBookings": "Business bookings", "business": "Business", "individualBookings": "Individual bookings", "onlyIncludesCompletedBookings": "Only includes completed bookings", "search": "Search by booking ID or employee name", "searchCustomer": "Search", "table": {"bookingId": "Booking ID", "name": "Name", "driverName": "Driver", "service": "Service", "totalAmount": "Total Amount", "distance": "Distance", "bookingDate&Time": "Date & Time", "customerName": "Customer", "employeeName": "Employee", "cancelledAfter5Mins": "Cancelled after 5 mins", "amount": "Amount", "role": "Role", "bookingStatus": "Booking Status", "carId": "Car ID", "refundTag": "Refund Tag", "refundAmount": "<PERSON><PERSON>", "amountCharged": "Amount charges", "filter": {"search": "Search by booking ID or employee name", "startDate": "Start Date", "endDate": "End Date", "paymentMethod": "Payment method", "zone": "All zones"}, "from": "From", "to": "To", "coupon": "Coupon", "cancelReason": "<PERSON><PERSON><PERSON> d’annulation", "amountForDriver": "Pay for driver", "rating": "Rating", "ratingDetail": "Rating Detail", "zoneName": "Zone Name", "zone": "Zone"}, "detail": {"title": "Détail de la réservation", "refundNote": "This booking is {{type}} refunded ({{value}}).", "customerInformation": {"title": "Informations sur le client", "viewDetail": "Voir les détails", "customerId": "ID client"}, "generalInformation": {"email": "Email", "phoneNumber": "Téléphone", "address": "Address", "codeNameAndPin": "Code Name | PIN"}, "departmentInformation": {"title": "Department information", "business": "Business", "department": "Department", "group": "Group"}, "driverInformation": {"title": "Informations sur le chauffeur", "driverId": "ID chauffeur", "taxInformation": "Infos fiscales", "tps": "TPS", "tvq": "TVQ"}, "carInformation": {"title": "Informations sur le véhicule", "licensePlates": "Plaque d<PERSON>immat", "carType": "Type"}, "bookingInformation": {"title": "Informations de la réservation", "service": "Service", "dateTime": "Date & time", "from": "De", "to": "À", "price": "Origin price", "discount": "Discount", "federalTax": "Federal tax", "provincialTax": "Provincial tax", "amount": "Amount", "cancelReason": "<PERSON><PERSON><PERSON> d'annulation", "OTHER": "<PERSON><PERSON>", "mappingRoute": "Itinéraire", "viewOnTheMap": "Voir la carte", "paymentID": "Payment ID", "scheduledTime": "Heure planifiée", "start": "D<PERSON>but", "end": "Fin", "loyalPoints": "Points de fidélité", "distance": "Distance", "paymentMethod": "Mode de paiement", "coupon": "Coupon", "totalAmount": "Total amount", "charge": "Charge", "netPrice": "Net price", "freeCancellation": "Annulation gratuite", "autoCancellation": "(Auto cancellation)", "noteToDriver": "Note au chauffeur", "rating": "Évaluation", "ratingDetail": "Détail de l’évaluation", "tipPaymentMethod": "Tip payment method"}, "receipt": {"title": "<PERSON><PERSON><PERSON>", "tps": "T.P.S (5%)", "tvq": "T.V.Q (9.975%)", "amountPaid": "Amount (paid)", "amountForDriver": "Paiement au chauffeur", "tip": "Tip", "stripeFee": "<PERSON><PERSON>e (Réservation)", "tipStripeFee": "Frais Stripe (Pourboire)", "bookingPaymentId": "Paiement de la réservation", "tipPaymentId": "Paiement du pourboire", "amountCharged": "<PERSON><PERSON> fact<PERSON>", "subTotal": "Sous-total", "total": "Total", "tipAmount": "<PERSON><PERSON> pourboire", "penaltyFee": "Stripe fee (Penalty fee)", "tripFare": "<PERSON><PERSON><PERSON> du <PERSON>", "promotionCode": "Code promo", "taxiLoyalSplit": "Partage Taxi Loyal"}, "refund": {"title": "<PERSON><PERSON><PERSON><PERSON>", "cancelRefund": "Annuler le remboursement", "newSupportTicket": "{{quantity}} tickets de support", "fully": "fully", "partially": "partially", "fullyRefunded": "Ce trajet a été entièrement remboursé", "partiallyRefunded": "Cette réservation a été entièrement remboursée", "tripCost": "Le coût initial du trajet était de <span style='font-weight:bold'>{{value}}</span>.", "tipAmount": "The tip amount was <span style='font-weight:bold'>{{value}}</span>."}, "refundPayment": {"title": "Remboursement", "amountPlaceholder": "Champ de saisie", "reason": "<PERSON><PERSON><PERSON> du re<PERSON>", "reasonPlaceholder": "Sélectionner un motif", "refundNote": "A<PERSON><PERSON>z une raison pour ce remboursement", "refundNoteRequired": "A note is required when a provided reason isn’t selected.", "note": "<PERSON><PERSON><PERSON>", "partiallyRefunded": "Un remboursement partiel a déjà été effectué ({{value}} restants).", "fullyRefunded": "Payment has been fully refunded.", "invalidRefundError": "Le remboursement ne peut pas dépasser {{value}}.", "refundAmountLessThanZero": "Le montant du remboursement doit être supérieur à 0.", "notification": {"description": "Le remboursement a été créé avec succès.", "message": "Créer un remboursement"}}, "supportTicket": {"title": "Tickets de support", "supportNote": "Note du support", "solvedNote": "Solved note", "markAsSolved": "Marquer comme résolu", "solveTicketConfirm": "Do you want to solve this ticket?", "note": "<PERSON><PERSON><PERSON>", "notePlaceholder": "Saisissez votre note", "solve": "<PERSON><PERSON><PERSON><PERSON>", "solved": "R<PERSON>ol<PERSON>", "partially": "partially", "fully": "fully", "confirm": {"title": "Confirmer", "description": "Voulez-vous résoudre ce ticket?"}, "resolve": {"title": "Ce ticket a été résolu avec succès.", "description": "R<PERSON>ol<PERSON>"}}, "bookingProgress": {"title": "Progression de la réservation", "bookingCreated": "Réservation créée", "lookingForADriver": "Recherche d’un chauffeur", "bookingCanceled": "Réservation annulée", "driverIsOnTheWay": "Le chauffeur est en route", "driverJustArrived": "Le chauffeur vient d’arriver", "youAreOnTheRoad": "En route", "you'veArrivedAtYourDestination": "Arrivé à destination", "bookingCompleted": "Réservation terminée"}}}, "cancel": {"DRIVER_ARRIVED_EARLY": "Driver arrived early", "DRIVER_ASKED_CANCEL": "Driver asked me to cancel", "DRIVER_NOT_GETTING_CLOSER": "Driver not getting closer", "COULD_NOT_FIND_DRIVER": "á", "WAIT_TIME_TOO_LONG": "Wait time was too long", "DRIVER_DELAYED": "The driver is not moving", "DRIVER_NOT_MOVING": "Delay of the driver", "INCORRECT_ADDRESS": "Incorrect pickup or drop-off address", "CANCELLED_BY_CENTER": "Cancel by Dispatch Center", "AUTO_CANCELLED": "bế", "OTHER": "Other"}, "customColumns": {"fixedColumns": "Fixed columns", "activeColumns": "Active columns", "availableColumns": "Available columns"}, "serviceType": {"ALL": "ALL", "TYPICAL_CAR": "Voiture typique-4", "ELECTRIC_CAR": "Voiture électrique", "VAN": "Fourgonlette", "ACCESSIBLE_VAN": "Fourgonnette accessible"}}