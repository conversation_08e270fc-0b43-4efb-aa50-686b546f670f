{"support": {"ticket": "Ticket", "supportTicket": "Support Ticket", "newRequest": "Nouvelle demande", "successfullyRefunded": "Remboursé avec succès", "refundCanceled": "Remboursement annulé", "refundPending": "Remboursement en attente", "solved": "R<PERSON>ol<PERSON>", "refundFailed": "Refund failed", "reason": "<PERSON>son", "type": "<PERSON><PERSON><PERSON>", "supportType": {"PAYMENT_ISSUE": "Problème de paiement", "LOST_ITEM": "Objet perdu", "OTHER": "<PERSON><PERSON>"}, "supportProgress": "Suivi de l’assistance", "note": "<PERSON><PERSON><PERSON>", "noSupportTicket": "Aucun ticket d’assistance disponible", "table": {"ticketId": "Ticket ID", "bookingId": "Booking ID", "supportNote": "Support note", "tag": "Tag", "receivedDate": "Received date", "status": "Status", "resolvedBy": "Resolved by", "resolvedDate": "Resolved date"}, "requestModal": {"title": "Request", "customerInformation": "Customer information", "paidAmount": "Paid amount"}, "reasonType": {"DUPLICATE": "Doublon", "FRAUDULENT": "<PERSON><PERSON><PERSON><PERSON>", "REQUESTED_BY_CUSTOMER": "Demandé par le client", "OTHER": "<PERSON><PERSON>"}, "by": "par"}}