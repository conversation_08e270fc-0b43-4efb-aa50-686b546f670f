{"bookingHistory": {"title": "Booking History", "businessBookings": "Business bookings", "business": "Business", "individualBookings": "Individual bookings", "onlyIncludesCompletedBookings": "Only includes completed bookings", "search": "Search by booking ID or employee name", "searchCustomer": "Search", "table": {"bookingId": "Booking ID", "name": "Name", "driverName": "Driver", "service": "Service", "totalAmount": "Total Amount", "distance": "Distance", "bookingDate&Time": "Date & Time", "customerName": "Customer", "employeeName": "Employee", "cancelledAfter5Mins": "Cancelled after 5 mins", "amount": "Amount", "role": "Role", "bookingStatus": "Booking Status", "carId": "Car ID", "refundTag": "Refund Tag", "refundAmount": "Refund amount", "amountCharged": "Amount charges", "filter": {"search": "Search by booking ID or employee name", "startDate": "Start Date", "endDate": "End Date", "paymentMethod": "Payment method", "zone": "All zones"}, "from": "From", "to": "To", "coupon": "Coupon", "cancelReason": "Cancel reason", "amountForDriver": "Pay for driver", "rating": "Rating", "ratingDetail": "Rating Detail", "zoneName": "Zone Name", "zone": "Zone"}, "detail": {"title": "Booking Detail", "refundNote": "This booking is {{type}} refunded ({{value}}).", "customerInformation": {"title": "Customer information", "viewDetail": "View detail", "customerId": "Customer ID"}, "generalInformation": {"email": "Email", "phoneNumber": "Phone number", "address": "Address", "codeNameAndPin": "Code Name | PIN"}, "departmentInformation": {"title": "Department information", "business": "Business", "department": "Department", "group": "Group"}, "driverInformation": {"title": "Driver information", "driverId": "Driver ID", "taxInformation": "Tax information", "tps": "TPS", "tvq": "TVQ"}, "carInformation": {"title": "Car information", "licensePlates": "License plates", "carType": "Car type"}, "bookingInformation": {"title": "Booking information", "service": "Service", "dateTime": "Date & time", "from": "From", "to": "To", "price": "Origin price", "discount": "Discount", "federalTax": "Federal tax", "provincialTax": "Provincial tax", "amount": "Amount", "cancelReason": "Cancel reason", "mappingRoute": "Mapping Route", "viewOnTheMap": "View on the map", "paymentID": "Payment ID", "scheduledTime": "Scheduled Time", "start": "Start", "end": "End", "loyalPoints": "Loyal Points", "distance": "Distance", "paymentMethod": "Payment method", "coupon": "Coupon", "totalAmount": "Total amount", "charge": "Charge", "netPrice": "Net price", "freeCancellation": "Free cancellation", "autoCancellation": "(Auto cancellation)", "noteToDriver": "Note to driver", "rating": "Rating", "ratingDetail": "Rating detail", "tipPaymentMethod": "Tip payment method"}, "receipt": {"title": "Receipt", "tps": "T.P.S (5%)", "tvq": "T.V.Q (9.975%)", "amountPaid": "Amount (paid)", "amountForDriver": "Pay for driver", "tip": "Tip", "stripeFee": "Stripe fee (Booking)", "tipStripeFee": "Stripe fee (Tip)", "bookingPaymentId": "Booking Payment", "tipPaymentId": "Tip Payment", "amountCharged": "Amount charged", "subTotal": "Subtotal", "total": "Total", "tipAmount": "Tip amount", "penaltyFee": "Stripe fee (Penalty fee)", "promotionCode": "Promo code", "taxiLoyalSplit": "Taxi Loyal Split"}, "refund": {"title": "Refund", "cancelRefund": "Cancel refund", "newSupportTicket": "{{quantity}} support ticket", "fully": "fully", "partially": "partially", "fullyRefunded": "This booking has been fully refunded", "partiallyRefunded": "This booking has been partial refunded", "tripCost": "The original trip cost was <span style='font-weight:bold'>{{value}}</span>.", "tipAmount": "The tip amount was <span style='font-weight:bold'>{{value}}</span>."}, "refundPayment": {"title": "Refund payment", "amountPlaceholder": "Input amount", "reason": "Reason", "reasonPlaceholder": "Select refund reason", "refundNote": "Add a reason for this refund", "refundNoteRequired": "A note is required when a provided reason isn’t selected.", "note": "Note", "partiallyRefunded": "Payment has been partially refunded ({{value}} remaining).", "fullyRefunded": "Payment has been fully refunded.", "invalidRefundError": "Refund cannot be more than {{value}}.", "refundAmountLessThanZero": "Refund amount must be greater than 0.", "notification": {"description": "The refund has been created successfully.", "message": "Create refund"}}, "supportTicket": {"title": "Support ticket", "supportNote": "Support note", "solvedNote": "Solved note", "markAsSolved": "<PERSON>", "solveTicketConfirm": "Do you want to solve this ticket?", "note": "Note", "notePlaceholder": "Enter your note", "solve": "Solve", "solved": "Solved", "partially": "partially", "fully": "fully", "confirm": {"title": "Confirm", "description": "Do you want to solve this ticket?"}, "resolve": {"title": "This ticket has been resolved successfully.", "description": "Resolve"}}, "bookingProgress": {"title": "Booking progress", "bookingCreated": "Booking created", "lookingForADriver": "Looking for a driver", "bookingCanceled": "Booking canceled", "driverIsOnTheWay": "Driver is on the way", "driverJustArrived": "The driver just arrived", "youAreOnTheRoad": "You're on the road", "you'veArrivedAtYourDestination": "You've arrived at your destination", "bookingCompleted": "Booking completed"}}}, "cancel": {"DRIVER_ARRIVED_EARLY": "Driver arrived early", "DRIVER_ASKED_CANCEL": "Driver asked me to cancel", "DRIVER_NOT_GETTING_CLOSER": "Driver not getting closer", "COULD_NOT_FIND_DRIVER": "Could not find driver", "WAIT_TIME_TOO_LONG": "Wait time was too long", "DRIVER_DELAYED": "The driver is not moving", "DRIVER_NOT_MOVING": "Delay of the driver", "INCORRECT_ADDRESS": "Incorrect pickup or drop-off address", "CANCELLED_BY_CENTER": "Cancel by Dispatch Center", "AUTO_CANCELLED": "Auto cancellation", "OTHER": "Other"}, "customColumns": {"fixedColumns": "Fixed columns", "activeColumns": "Active columns", "availableColumns": "Available columns"}, "serviceType": {"ALL": "ALL", "TYPICAL_CAR": "Typical car-4", "ELECTRIC_CAR": "Electric car", "VAN": "<PERSON>", "ACCESSIBLE_VAN": "Accessible van"}}