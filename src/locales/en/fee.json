{"fee": {"serviceFee": "Service fee", "rate": "Rate", "tipPosition": "Tip position", "title": "Fee Configuration", "newFee": "New Fee", "search": "Search by name or amount", "type": "All services", "name": "Name", "amount": "Amount", "vehicleType": "Service", "basedOn": "Per ride/km", "applyFrom": "Apply from", "status": "Status", "createdBy": "Created by", "information": "Fee information", "feeDetail": "Fee detail", "applySpecificArea": {"title": "Apply specific area", "description": "The fee will be applied if the chosen location is within these specific areas"}, "perType": {"placeholder": "All type"}, "create": {"new": "Create new fee", "name": "Name (EN)", "nameFr": "Name (FR)", "namePlaceholder": "Enter fee name", "amountPlaceholder": "Enter amount", "vehicleTypePlaceholder": "Select services", "applyFrom": {"title": "Apply from", "all": "All", "specificDate": "Specific date", "startFrom": "Start from"}, "applyFromPlaceholder": "Start from", "basedOn": {"placeholder": "Select per ride/km", "ride": "Ride", "km": "Kilometer"}, "notification": {"description": "The fee has been created successfully.", "message": "Create fee"}}, "update": {"title": "Edit fee", "notification": {"description": "The fee has been updated successfully.", "message": "Update fee"}}, "delete": {"title": "Delete fee", "desc": "Are you sure to delete this fee?", "notification": {"description": "The fee has been deleted successfully.", "message": "Delete fee"}}}, "rate": {"title": "Rate Configuration", "newRate": "New Rate", "search": "Search by name or Km rate", "applyDate": "Apply date", "time": "Time", "rateDetail": "Rate detail", "information": "Rate information", "create": {"new": "Create new rate", "kmRate": "KM Rate", "namePlaceholder": "Enter rate name", "kmRatePlaceholder": "Enter km rate", "applyTime": {"title": "Apply time", "all": "All", "specificDate": "Specific date", "days": "Days", "startTime": "Start time", "endTime": "End time", "startDate": "Start date", "endDate": "End date"}, "applyFromPlaceholder": "Start from", "notification": {"message": "Create rate", "description": "The rate has been created successfully."}}, "update": {"title": "Edit rate", "notification": {"description": "The rate has been updated successfully.", "message": "Update rate"}}, "delete": {"title": "Delete rate", "desc": "Are you sure to delete this rate?", "notification": {"description": "The rate has been deleted successfully.", "message": "Delete rate"}}, "duplicate": {"title": "Duplicate Configuration", "desc": "The system will take the higher rate. Do you want to continue this action?"}, "currentRateInformation": "Current rate information", "pricingRuleNote": "Pricing Rule Notice: If duplicate prices exist, the system will automatically apply the higher rate.", "pricingRuleNoteOfDays": "Pricing Rule Notice: If duplicate prices exist, the system will automatically apply the higher rate. In addition, the selected time range must not exceed 24 hours."}, "tipPosition": {"activities": "Activities", "setting": "Tip position setting", "content": "Configure when tip options appear on the mobile app to match your service flow.", "beforeBooking": "Before booking", "beforeBookingContent": "Set your service tip option before booking is confirmed.", "afterBooking": "After booking", "afterBookingContent": "Set your service tip option after booking is completed.", "table": {"actionBy": "Action by", "atTime": "At time", "selectOption": "Select option"}}, "fixedFare": {"title": "Fixed Fare", "zoneSelects": "Select zone", "vehicleTypes": "Services", "newFare": "New Fare", "createNewFixedFare": "Create new fixed fare", "editFixedFare": "Edit fixed fare", "roundTrip": "Round trip", "roundTripTooltip": "Enable this option if the fare should cover both the outgoing and return journeys between the selected zones.", "couponsAllowed": "Coupons allowed", "fixedFarePlaceholder": "Enter amount", "detail": "Fixed fare detail", "information": "Fixed fare information", "delete": {"title": "Delete fixed fare", "desc": "Are you sure to delete this fixed fare?", "notification": {"description": "The fixed fare has been deleted successfully.", "message": "Delete fixed fare"}}, "update": {"title": "Edit fixed fare", "notification": {"description": "The fixed fare has been updated successfully.", "message": "Update fixed fare"}}, "create": {"title": "Edit fixed fare", "notification": {"description": "The fixed fare has been created successfully.", "message": "Create fixed fare"}}, "viewOnMap": "View on map", "startTimeEqualsEndTime": "Start time cannot be the same as end time", "amountChargedTooltip": "This is the total amount charged per trip, including T.P.S (5%) and T.V.Q (9.975%)"}}