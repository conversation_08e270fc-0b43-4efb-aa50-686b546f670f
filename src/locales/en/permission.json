{"permission": {"title": "Permission", "permissionManagement": "Permission management", "newRole": "New role", "table": {"search": "Search by role", "status": "Status", "role": "Role", "assignee": "Assignee", "createdDate": "Created date"}, "create": {"title": "Create new permission"}, "update": {"title": "Update permission"}, "roleName": {"title": "Role name", "placeholder": "Enter name"}, "dataViewPermission": "Data view permission", "isolated": "Isolated (Assigned data only)", "department": "Department (All data of department only)", "cross": "Cross (All data)", "availableRight": "Available rights", "selectedRight": "Selected rights", "deletePermission": {"title": "Delete permission", "desc": "Are you sure you want to delete this permission?", "notification": {"description": "The permission has been deleted successfully.", "message": "Delete permission"}}, "createRole": {"notification": {"description": "The role has been created successfully.", "message": "Create role"}}, "updateRole": {"notification": {"description": "The role has been updated successfully.", "message": "Update role"}}, "detail": {"title": "Permission detail", "permissionInformation": "Permission information", "name": "Name", "dataView": "Data view", "selectedRight": "Selected rights", "employeeList": "Employee list", "addEmployee": "Add Employee to Role", "table": {"search": "Search by name, or ID", "status": "Status", "employeeId": "Employee ID", "name": "Name", "employee": "Employee", "phoneNo": "Phone no", "assignedDate": "Assigned date"}, "removeEmployee": {"title": "Confirmation", "desc": "Do you want to remove this employee?", "notification": {"description": "The employee has been removed successfully.", "message": "Remove employee"}}, "addEmployeeNotification": {"notification": {"description": "The employee has been added successfully.", "message": "Add employee"}}}, "key": {"dashboard": {"title": "Dashboard", "viewBookingSource": "View Booking Source", "heatmaps": "Heatmaps"}, "bookingHistory": {"personal": "Booking History", "refund": "Refund (Personal)", "viewPersonal": "View/Export booking history/detail (Personal)", "viewBusiness": "View/Export booking history/detail (Business)"}, "support": {"title": "Support", "view": "View support ticket list/detail", "solve": "Solve ticket"}, "customer": {"title": "Customer", "viewPersonal": "View/Export customer list/detail (Personal)", "editPersonal": "Edit customer (Personal)", "viewBusiness": "View/Export customer list/detail (Business)", "editBusiness": "Create/Edit/Delete customer (Business)"}, "task": {"title": "Task", "viewAssigned": "View/Create/Edit/Delete task list (Assigned data)", "viewDepartment": "View/Create/Edit/Delete task list (Department-related data)", "viewAll": "View/Create/Edit/Delete task list (All data)", "config": "Status/Priority Configuration"}, "fleet": {"title": "Fleet", "view": "View fleet list/detail"}, "customerReview": {"title": "Customer reviews", "viewCustomer": "View customer reviews list/Export list", "viewAverage": "View average rating/Export list"}, "department": {"title": "Department", "view": "View department list/detail", "action": "Create/Edit/Delete department"}, "zone": {"title": "Zone", "view": "View/Export zone list/detail"}, "permission": {"title": "Permissions", "view": "View role list/detail", "action": "Create/Edit/Delete role"}, "coupon": {"title": "Coupon", "view": "View coupon list/detail", "action": "Create/Edit/Delete coupon"}, "location": {"title": "Location", "view": "View sub-location list/detail", "action": "Create/Edit/Delete sub-location"}, "loyalPoints": {"title": "Loyal Points", "view": "View Loyal Points Configuration", "edit": "Edit Loyal Points Configuration"}, "notificationSender": {"title": "Notification Sender", "view": "View notification list/detail", "action": "Create/Edit/Delete notification"}, "banner": {"title": "Banner", "view": "View banner list/detail", "action": "Create/Edit/Delete banner"}, "feeConfiguration": {"title": "Fee Configuration", "fee": {"view": "View fee list/detail", "action": "Create/Edit/Delete fee"}, "rate": {"view": "View rate list/detail", "action": "Create/Edit/Delete rate"}, "tip": {"view": "View tip position config", "action": "Config tip position"}, "fixedFare": {"view": "View fixed fare list/detail", "action": "Create/Edit/Delete fixed fare"}}, "loyalOne": {"title": "Loyal One", "viewList": "View package list/detail", "viewRevenue": "View package revenue", "action": "Create/Edit/Delete package"}, "employee": {"title": "Employee", "viewDepartment": "View/Export employee list/detail (Department-related data)", "viewEmployee": "View/Export employee list/detail (All data)", "actionEmployee": "Create/Edit/Delete employee", "viewDriver": "View/Export driver list/detail", "actionDriver": "Active/Deactive driver"}, "news": {"title": "News", "view": "View news list/detail", "action": "Create/Edit/Delete news"}}}}