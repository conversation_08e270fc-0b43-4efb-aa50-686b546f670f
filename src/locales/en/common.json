{"common": {"validate": {"email": {"invalid": "Invalid email", "required": "Email is required"}, "name": {"required": "Name is required"}, "phoneNumber": {"required": "Phone number is required"}, "url": {"invalid": "Invalid Url", "required": "Url is required"}, "password": {"invalid": "Invalid password", "required": "Password is required"}, "requiredField": "This is a required field", "requiredFieldBasedOnName": "{{fieldName}} is a required field", "mustContainDigitsOnly": "Field must contain digits only", "maxLength": "Max length is {{length}}", "requiredLength": "Required length is {{length}}", "requiredMin": "The minimum amount is ${{min}}. Please enter an amount greater than or equal to ${{min}}", "requiredMax": "This value must be less than or equal to {{max}}", "pleaseEnterAmount": "Please enter amount", "wrongUrlFormat": "Wrong URL format", "duplicateField": "This field cannot be duplicated.", "fileSize": "Image must be smaller than {{maximumSize}} MB!"}, "logout": "Logout", "button": {"send": "Send", "edit": "Edit", "delete": "Delete", "view": "View", "pay": "Pay", "cancel": "Cancel", "create": "Create", "export": "Export", "confirm": "Confirm", "update": "Update", "reset": "Reset", "saveChanges": "Save Changes", "next": "Next", "save": "Save", "clearFilter": "Clear Filter", "back": "Back", "detail": "Detail", "previousStep": "Previous step", "schedule": "Schedule", "apply": "Apply", "viewMore": "View more", "refund": "Refund", "maskAsSolve": "<PERSON> as solve", "close": "Close", "add": "Add", "remove": "Remove", "gotIt": "Got it", "more": "More", "collapse": "Collapse", "upload": "Upload"}, "table": {"result": "Result"}, "status": {"active": "Active", "inactive": "Inactive", "expired": "Expired", "removed": "Removed", "cancelled": "Cancelled", "completed": "Completed", "scheduled": "Scheduled", "closed": "Closed", "open": "Open", "title": "Status"}, "time": {"today": "Today", "thisWeek": "This Week", "thisMonth": "This Month", "thisYear": "This Year"}, "error": {"opp": "Oops!", "titleError": "Error!", "titleSuccess": "Success!"}, "success": "Success", "copiedToClipboard": "Copied to clipboard", "search": "Search", "saveChanges": "Save changes?", "areYouSureToSaveChanges": "Are you sure to save changes?", "paymentMethod": {"cash": "Pay in car", "card": "Card"}, "generalInformation": "General information", "upload": {"chooseFile": "Choose file", "noFileChosen": "No file chosen", "validate": {"fileSize": "Image must smaller than {{maximumSize}} MB!", "fileType": "File type must be {{type}}", "errorMessage": "Please download uploaded file to review the issue"}, "processing": "Processing"}, "excelOnly": "One file only, allowed XLSX, XLS only.", "downloadSample": "Download sample", "email": "Email", "phoneNumber": "Phone number", "statusLabel": "Status", "from": "From", "to": "To", "distance": "Distance", "service": "Service", "paymentMethodLabel": "Payment method", "paidAmount": "Paid amount", "pleaseSelect": "Please select", "pleaseSelectAMember": "Please select a member", "typeHere": "Type here", "comment": "Comment", "showLess": "Show less", "showMore": "Show more", "yes": "Yes", "no": "No", "export": {"title": "Exporting", "desc": "You will receive the export file via email shortly after the process is finished"}, "pageNotFound": "Page Not Found", "pageNotFoundMessage": "Oops! The page you’re looking for doesn’t exist or has been moved. <br/> Please contact the administrator if you believe this is an error.", "accessDenied": "Access Denied", "accessDeniedMessage": "Oops! Looks like you do not have the required permission to view this page. <br/> Please contact the administrator if you believe this is an error.", "information": "Information", "new": "New", "failed": "Failed", "cancelRenewal": "Cancel renewal"}}