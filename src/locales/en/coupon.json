{"coupon": {"title": "Coupon Management", "table": {"filter": {"search": "Search by promo code"}, "promotionCode": "Promo Code", "discountType": "Discount Type", "discountValue": "Discount Value", "usageLimit": "Usage Limit Per Account", "totalUserUsageLimit": "Usage", "expiredDate": "Expired Date", "status": "Status", "title": "Title", "redeemMethod": "Redeem Method", "createdDate": "Created Date", "validFrom": "Available From", "validTo": "Available To", "search": "Search by promo code or title"}, "create": {"title": "Create new coupon", "new": "New coupon", "general": "General", "targetAudience": "Target audience", "selectFromList": "Select from list", "uploadFile": "Upload file", "couponTitle": {"title": "Title (EN)", "titleFr": "Title (FR)", "placeholder": "Enter coupon title"}, "promotionCode": {"title": "Promo code", "placeholder": "Enter promotion code"}, "availableTime": {"title": "Available times", "placeholder": "Select available times"}, "vehicleTypes": {"title": "Vehicle types", "placeholder": "Select vehicle types", "all": "All", "typical": "Typical Car-4", "electric": "Electric Car", "van": "<PERSON>", "accessible": "Accessible Van", "anyCar": "Any Car"}, "discountType": {"title": "Discount type", "placeholder": "Select discount type"}, "discountValue": {"title": "Discount value", "placeholder": "Enter discount value"}, "percentage": {"title": "Percentage (%)", "placeholder": "Enter percentage"}, "amount": {"title": "Amount ({{unit}})", "placeholder": "Enter amount ({{unit}})"}, "dropRate": {"title": "Drop-rate", "placeholder": "Enter drop-rate"}, "checkMaxDiscountLimit": {"title": "Check maximum discount limit ($CAD)", "placeholder": "Enter amount"}, "checkMinSpendForAdoption": {"title": "Check minimum spend for adoption ($CAD)", "placeholder": "Enter amount"}, "redeemMethod": {"title": "Redeem method", "placeholder": "Select redeem method", "free": "Free", "manual": "Manual input", "useLoyal": "Redeem by Point"}, "redeemPoint": {"title": "Redeem points", "placeholder": "Enter redeem points"}, "usageLimit": {"title": "Usage limit / account", "placeholder": "Enter usage limit / account"}, "totalUserUsageLimit": {"title": "Total user usage limit", "placeholder": "Enter total user usage limit"}, "paymentMethod": "Payment method", "taxiLoyalSplit": {"title": "Taxi Loyal Split", "placeholder": "Enter topup"}, "description": {"title": "Description (EN)", "titleFr": "Description (FR)", "placeholder": "Enter description"}, "userTypes": {"title": "User types", "placeholder": "Select user types", "all": "All users", "paidUser": "Old users (booked at least one ride)", "newUser": "New users (haven't booked a ride yet)", "specificUser": "Specific users (choose users from list)"}, "userProfile": {"title": "User profile", "placeholder": "Select user profile", "all": "All", "business": "Business", "individual": "Individual"}, "specificUser": {"title": "Specific users list", "placeholder": "Search by name or email"}, "advanced": {"title": "Advanced", "specificTime": "Specific Time", "configSpecificTime": "Config Specific Time", "specificArea": "Specific Area", "configPickUpArea": "Specific pick up area", "configDropOffArea": "Specific drop off area", "specificAreaTable": {"name": {"title": "Name", "placeholder": "Enter pickup name"}, "location": {"title": "Location", "placeholder": "Enter pickup coordinates"}, "radius": {"title": "<PERSON><PERSON>", "placeholder": "Radius (km)"}}, "specificDropOffArea": {"title": "Specific drop off area", "placeholder": "Enter coordinates (drop-off area)"}, "radius": {"title": "<PERSON><PERSON>", "placeholder": "Radius (km)"}, "getCoordinatesTooltip": "<span style='font-weight:bold'>How to get coordinates?</span> </br> 1. Google Maps </br> <ul style='list-style:inside; margin-left: 10px'><li>Open the Google Maps.</li> <li>Click right mouse button on the location  you want to find the coordinates.</li> <li>Copy coordinates (eg:16.06036831815255, 108.19740263596206), then paste in Taxi Loyal.</li></ul> 2. Apple Maps. </br> <ul style='list-style:inside; margin-left: 10px'><li>Open the Maps app.</li> <li>Tap and hold on the location.</li> <li>The coordinates will appear in a pop-up box or under the location's name.</li> <li>Copy coordinates, then paste in Taxi Loyal.</li></ul>", "validateCoordinatesAndRadius": "Please enter both coordinates and radius", "validateSpecificArea": "Please choose at least 1 option"}, "repeatTime": "Repeat time (weekday)", "notification": {"description": "The coupon has been created successfully.", "message": "Create coupon"}, "codeAlreadyExists": "This code already exists"}, "update": {"title": "Edit coupon", "notification": {"description": "The coupon has been updated successfully.", "message": "Update coupon"}}, "delete": {"title": "Delete Coupon", "desc": "Are you sure to delete this coupon?", "notification": {"description": "The coupon has been deleted successfully.", "message": "Delete coupon"}}, "detail": {"title": "Coupon detail", "totalDiscount": "Total Discount", "totalTransactionValue": "Total Transaction Value", "totalUsedRemain": "Total Used / Remain", "totalUsers": "Total Users", "activityHistory": "Activity history", "maximumDiscountLimit": "Maximum discount limit ($CAD)", "minimumSpendForAdoption": "Minimum spend for adoption ($CAD)", "discountValue": "Discount value", "totalUsed": "Total Used"}, "logHistory": {"title": "Log history", "bookingId": "Booking ID", "customer": "Customer", "redeemDate": "Redeem date", "dateOfUse": "Date of use", "transactionValue": "Transaction value", "discountValue": "Discount value", "search": "Search by booking ID"}, "activityHistory": {"title": "Activity history", "actionBy": "Action by", "activity": "Activity", "atTime": "At time"}, "selectFilterType": "Select filter type", "atLeast1PickUpPoint": "Please add at least 1 pick up point", "atLeast1DropOffPoint": "Please add at least 1 drop off point"}}