/* eslint-disable max-lines */
import { IconKey } from '#/shared/constants/iconKey';
import { addIcon } from '@iconify/react';

addIcon(IconKey.ShortLogo, {
  body: ` 
  <svg width="28" height="26" viewBox="0 0 28 26" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path d="M1.50523 1.24034C1.26725 0.784198 1.59817 0.238281 2.11267 0.238281H27.0176V8.11736H7.16984C5.89354 8.11736 4.72306 7.40783 4.13268 6.27628L1.50523 1.24034Z" fill="#ED1C24"/>
  <path d="M5.95836 10.1471C5.72037 9.69094 6.0513 9.14502 6.56579 9.14502H27.0174V17.0241H11.623C10.3467 17.0241 9.17618 16.3146 8.5858 15.183L5.95836 10.1471Z" fill="#ED1C24"/>
  <path d="M10.5834 19.0538C10.3454 18.5977 10.6763 18.0518 11.1908 18.0518H27.0177V25.9308H16.248C14.9717 25.9308 13.8012 25.2213 13.2108 24.0898L10.5834 19.0538Z" fill="#ED1C24"/>
  </svg>  
        `,
  height: 26,
  width: 28,
});

addIcon(IconKey.Receipt, {
  body: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
  <path d="M7 2H6C3 2 2 3.79 2 6V7V21C2 21.83 2.94 22.3 3.6 21.8L5.31 20.52C5.71 20.22 6.27 20.26 6.63 20.62L8.29 22.29C8.68 22.68 9.32 22.68 9.71 22.29L11.39 20.61C11.74 20.26 12.3 20.22 12.69 20.52L14.4 21.8C15.06 22.29 16 21.82 16 21V4C16 2.9 16.9 2 18 2H7ZM5.97 14.01C5.42 14.01 4.97 13.56 4.97 13.01C4.97 12.46 5.42 12.01 5.97 12.01C6.52 12.01 6.97 12.46 6.97 13.01C6.97 13.56 6.52 14.01 5.97 14.01ZM5.97 10.01C5.42 10.01 4.97 9.56 4.97 9.01C4.97 8.46 5.42 8.01 5.97 8.01C6.52 8.01 6.97 8.46 6.97 9.01C6.97 9.56 6.52 10.01 5.97 10.01ZM12 13.76H9C8.59 13.76 8.25 13.42 8.25 13.01C8.25 12.6 8.59 12.26 9 12.26H12C12.41 12.26 12.75 12.6 12.75 13.01C12.75 13.42 12.41 13.76 12 13.76ZM12 9.76H9C8.59 9.76 8.25 9.42 8.25 9.01C8.25 8.6 8.59 8.26 9 8.26H12C12.41 8.26 12.75 8.6 12.75 9.01C12.75 9.42 12.41 9.76 12 9.76Z" fill="#007AFF"/>
  <path d="M18.01 2V3.5C18.67 3.5 19.3 3.77 19.76 4.22C20.24 4.71 20.5 5.34 20.5 6V8.42C20.5 9.16 20.17 9.5 19.42 9.5H17.5V4.01C17.5 3.73 17.73 3.5 18.01 3.5V2ZM18.01 2C16.9 2 16 2.9 16 4.01V11H19.42C21 11 22 10 22 8.42V6C22 4.9 21.55 3.9 20.83 3.17C20.1 2.45 19.11 2.01 18.01 2C18.02 2 18.01 2 18.01 2Z" fill="#007AFF"/>
</svg>`,
  height: 24,
  width: 24,
});

addIcon(IconKey.Email, {
  body: `<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M14.2632 17.5H5.73611C2.62371 17.5 0.833008 15.7973 0.833008 12.8378V7.16216C0.833008 4.2027 2.62371 2.5 5.73611 2.5H14.2632C17.3756 2.5 19.1663 4.2027 19.1663 7.16216V12.8378C19.1663 15.7973 17.3756 17.5 14.2632 17.5ZM5.73611 3.71622C3.29735 3.71622 2.11208 4.84324 2.11208 7.16216V12.8378C2.11208 15.1568 3.29735 16.2838 5.73611 16.2838H14.2632C16.702 16.2838 17.8873 15.1568 17.8873 12.8378V7.16216C17.8873 4.84324 16.702 3.71622 14.2632 3.71622H5.73611Z" fill="#8E9193"/>
<path d="M9.99818 10.7057C9.2819 10.7057 8.5571 10.4949 8.00284 10.0652L5.33384 8.03818C5.06097 7.82737 5.00981 7.44629 5.23152 7.18683C5.45322 6.92737 5.854 6.87873 6.12687 7.08954L8.79585 9.11656C9.44392 9.61116 10.5439 9.61116 11.192 9.11656L13.861 7.08954C14.1338 6.87873 14.5431 6.91926 14.7563 7.18683C14.978 7.44629 14.9354 7.83548 14.654 8.03818L11.985 10.0652C11.4393 10.4949 10.7145 10.7057 9.99818 10.7057Z" fill="#8E9193"/>
</svg>
`,
  height: 20,
  width: 20,
});

addIcon(IconKey.People, {
  body: `<svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24" fill="none">
  <path d="M18.197 7.77C18.127 7.76 18.057 7.76 17.987 7.77C16.437 7.72 15.207 6.45 15.207 4.89C15.207 3.3 16.497 2 18.097 2C19.687 2 20.987 3.29 20.987 4.89C20.977 6.45 19.747 7.72 18.197 7.77Z" fill="#34C759"/>
  <path d="M21.4556 14.6999C20.3356 15.4499 18.7656 15.7299 17.3156 15.5399C17.6956 14.7199 17.8956 13.8099 17.9056 12.8499C17.9056 11.8499 17.6856 10.8999 17.2656 10.0699C18.7456 9.86991 20.3156 10.1499 21.4456 10.8999C23.0256 11.9399 23.0256 13.6499 21.4556 14.6999Z" fill="#34C759"/>
  <path d="M7.10617 7.77C7.17617 7.76 7.24617 7.76 7.31617 7.77C8.86617 7.72 10.0962 6.45 10.0962 4.89C10.0962 3.29 8.80617 2 7.20617 2C5.61617 2 4.32617 3.29 4.32617 4.89C4.32617 6.45 5.55617 7.72 7.10617 7.77Z" fill="#34C759"/>
  <path d="M7.21516 12.8496C7.21516 13.8196 7.42516 14.7396 7.80516 15.5696C6.39516 15.7196 4.92516 15.4196 3.84516 14.7096C2.26516 13.6596 2.26516 11.9496 3.84516 10.8996C4.91516 10.1796 6.42516 9.88962 7.84516 10.0496C7.43516 10.8896 7.21516 11.8396 7.21516 12.8496Z" fill="#34C759"/>
  <path d="M12.7868 15.87C12.7068 15.86 12.6168 15.86 12.5268 15.87C10.6868 15.81 9.2168 14.3 9.2168 12.44C9.2268 10.54 10.7568 9 12.6668 9C14.5668 9 16.1068 10.54 16.1068 12.44C16.0968 14.3 14.6368 15.81 12.7868 15.87Z" fill="#34C759"/>
  <path d="M9.53484 17.9401C8.02484 18.9501 8.02484 20.6101 9.53484 21.6101C11.2548 22.7601 14.0748 22.7601 15.7948 21.6101C17.3048 20.6001 17.3048 18.9401 15.7948 17.9401C14.0848 16.7901 11.2648 16.7901 9.53484 17.9401Z" fill="#34C759"/>
  </svg>`,
  height: 24,
  width: 24,
});

addIcon(IconKey.MoneySend, {
  body: `<svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24" fill="none">
  <path d="M10.584 10.03C10.584 10.57 10.734 10.65 11.074 10.77L11.584 10.95V9.25H11.284C10.904 9.25 10.584 9.6 10.584 10.03Z" fill="#FF3B30"/>
  <path d="M13.084 14.7498H13.384C13.774 14.7498 14.084 14.3998 14.084 13.9698C14.084 13.4298 13.934 13.3498 13.594 13.2298L13.084 13.0498V14.7498Z" fill="#FF3B30"/>
  <path d="M19.914 5.48L17.864 7.53C17.714 7.68 17.524 7.75 17.334 7.75C17.144 7.75 16.954 7.68 16.804 7.53C16.514 7.24 16.514 6.76 16.804 6.47L18.854 4.42C17.094 2.92 14.824 2 12.334 2C6.81398 2 2.33398 6.48 2.33398 12C2.33398 17.52 6.81398 22 12.334 22C17.854 22 22.334 17.52 22.334 12C22.334 9.51 21.414 7.24 19.914 5.48ZM14.084 11.82C14.724 12.05 15.584 12.51 15.584 13.98C15.584 15.23 14.594 16.26 13.384 16.26H13.084V16.51C13.084 16.92 12.744 17.26 12.334 17.26C11.924 17.26 11.584 16.92 11.584 16.51V16.26H11.504C10.174 16.26 9.08398 15.14 9.08398 13.76C9.08398 13.34 9.42398 13 9.83398 13C10.244 13 10.584 13.34 10.584 13.75C10.584 14.3 10.994 14.75 11.504 14.75H11.584V12.53L10.584 12.18C9.94398 11.95 9.08398 11.49 9.08398 10.02C9.08398 8.77 10.074 7.74 11.284 7.74H11.584V7.5C11.584 7.09 11.924 6.75 12.334 6.75C12.744 6.75 13.084 7.09 13.084 7.5V7.75H13.164C14.494 7.75 15.584 8.87 15.584 10.25C15.584 10.66 15.244 11 14.834 11C14.424 11 14.084 10.66 14.084 10.25C14.084 9.7 13.674 9.25 13.164 9.25H13.084V11.47L14.084 11.82Z" fill="#FF3B30"/>
  <path d="M23.024 1.71C22.944 1.53 22.804 1.38 22.614 1.3C22.524 1.27 22.434 1.25 22.334 1.25H18.334C17.924 1.25 17.584 1.59 17.584 2C17.584 2.41 17.924 2.75 18.334 2.75H20.524L18.854 4.42C19.234 4.75 19.584 5.1 19.914 5.48L21.584 3.81V6C21.584 6.41 21.924 6.75 22.334 6.75C22.744 6.75 23.084 6.41 23.084 6V2C23.084 1.9 23.064 1.81 23.024 1.71Z" fill="#FF3B30"/>
  </svg>`,
  height: 24,
  width: 24,
});

addIcon(IconKey.Close, {
  body: ` 
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
<path d="M13.4099 12.0002L19.7099 5.71019C19.8982 5.52188 20.004 5.26649 20.004 5.00019C20.004 4.73388 19.8982 4.47849 19.7099 4.29019C19.5216 4.10188 19.2662 3.99609 18.9999 3.99609C18.7336 3.99609 18.4782 4.10188 18.2899 4.29019L11.9999 10.5902L5.70994 4.29019C5.52164 4.10188 5.26624 3.99609 4.99994 3.99609C4.73364 3.99609 4.47824 4.10188 4.28994 4.29019C4.10164 4.47849 3.99585 4.73388 3.99585 5.00019C3.99585 5.26649 4.10164 5.52188 4.28994 5.71019L10.5899 12.0002L4.28994 18.2902C4.19621 18.3831 4.12182 18.4937 4.07105 18.6156C4.02028 18.7375 3.99414 18.8682 3.99414 19.0002C3.99414 19.1322 4.02028 19.2629 4.07105 19.3848C4.12182 19.5066 4.19621 19.6172 4.28994 19.7102C4.3829 19.8039 4.4935 19.8783 4.61536 19.9291C4.73722 19.9798 4.86793 20.006 4.99994 20.006C5.13195 20.006 5.26266 19.9798 5.38452 19.9291C5.50638 19.8783 5.61698 19.8039 5.70994 19.7102L11.9999 13.4102L18.2899 19.7102C18.3829 19.8039 18.4935 19.8783 18.6154 19.9291C18.7372 19.9798 18.8679 20.006 18.9999 20.006C19.132 20.006 19.2627 19.9798 19.3845 19.9291C19.5064 19.8783 19.617 19.8039 19.7099 19.7102C19.8037 19.6172 19.8781 19.5066 19.9288 19.3848C19.9796 19.2629 20.0057 19.1322 20.0057 19.0002C20.0057 18.8682 19.9796 18.7375 19.9288 18.6156C19.8781 18.4937 19.8037 18.3831 19.7099 18.2902L13.4099 12.0002Z" fill="#1E2328"/>
</svg>  
        `,
  height: 24,
  width: 24,
});

addIcon(IconKey.Pdf, {
  body: `<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path d="M11.6665 17.7082H8.33317V18.9582H11.6665V17.7082ZM17.7082 11.3024V11.6665H18.9582V11.3024H17.7082ZM12.409 3.84238L15.7082 6.81155L16.544 5.88155L13.2457 2.91238L12.409 3.84238ZM18.9582 11.3024C18.9582 9.89488 18.9707 9.00322 18.6165 8.20655L17.474 8.71572C17.6957 9.21405 17.7082 9.78488 17.7082 11.3024H18.9582ZM15.7082 6.81155C16.8357 7.82655 17.2523 8.21822 17.474 8.71572L18.6165 8.20655C18.2615 7.40905 17.5907 6.82322 16.544 5.88155L15.7082 6.81155ZM8.35817 2.29155C9.6765 2.29155 10.174 2.30155 10.6165 2.47155L11.0648 1.30488C10.3548 1.03155 9.5815 1.04155 8.35817 1.04155V2.29155ZM13.2457 2.91322C12.3407 2.09905 11.7748 1.57655 11.0648 1.30488L10.6173 2.47155C11.0607 2.64155 11.434 2.96488 12.409 3.84238L13.2457 2.91322ZM8.33317 17.7082C6.744 17.7082 5.61567 17.7066 4.75817 17.5916C4.25912 17.5245 3.88582 17.4223 3.59102 17.276C3.28201 17.1227 2.8871 17.1126 2.64317 17.3565C2.39915 17.6006 2.39619 18.0028 2.68376 18.1935C3.21851 18.5483 3.85204 18.7309 4.59234 18.8307C5.55067 18.9599 6.77984 18.9582 8.33317 18.9582V17.7082ZM11.6665 18.9582C13.2198 18.9582 14.4482 18.9599 15.4073 18.8307C16.384 18.699 17.1748 18.4224 17.799 17.799L16.9157 16.9157C16.5623 17.2674 16.079 17.479 15.2407 17.5916C14.3848 17.7066 13.2557 17.7082 11.6665 17.7082V18.9582ZM17.7082 11.6665C17.7082 13.2557 17.7065 14.3849 17.5915 15.2416C17.479 16.0791 17.2673 16.5624 16.9148 16.9149L17.7982 17.7982C18.4232 17.1749 18.699 16.3841 18.8307 15.4074C18.9598 14.449 18.9582 13.2199 18.9582 11.6665H17.7082ZM1.6665 8.33322C2.01168 8.33322 2.29129 8.0531 2.29188 7.70792C2.29407 6.44116 2.30886 5.49871 2.40817 4.75822C2.52067 3.92072 2.73234 3.43738 3.08484 3.08488L2.2015 2.20155C1.5765 2.82488 1.30067 3.61572 1.169 4.59238C1.05768 5.41835 1.04354 6.44551 1.04176 7.70799C1.04127 8.05317 1.32133 8.33322 1.6665 8.33322ZM8.35817 1.04155C6.79567 1.04155 5.5615 1.03988 4.599 1.16905C3.61817 1.30072 2.82484 1.57738 2.20067 2.20072L3.084 3.08405C3.43734 2.73238 3.9215 2.52072 4.76484 2.40822C5.62567 2.29322 6.76067 2.29155 8.35817 2.29155V1.04155Z" fill="currentcolor"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M10.208 2.08301H11.458V4.16634C11.458 5.16608 11.4593 5.85315 11.5287 6.36924C11.5956 6.86743 11.7155 7.11161 11.885 7.28107C12.0544 7.45053 12.2986 7.57038 12.7968 7.63733C13.3129 7.70668 13.9999 7.70801 14.9997 7.70801H18.333V8.95801H14.9549C14.0109 8.95803 13.2394 8.95805 12.6303 8.87619C11.9937 8.79064 11.4416 8.60549 11.0011 8.16495C10.5605 7.72441 10.3754 7.17234 10.2898 6.53573C10.208 5.92662 10.208 5.1551 10.208 4.21106L10.208 2.08301Z" fill="currentcolor"/>
  <path d="M0.897994 14.9999C0.695986 14.9999 0.532227 14.8361 0.532227 14.6341V10.465C0.532227 10.2918 0.672623 10.1514 0.84581 10.1514H2.26045C2.63766 10.1514 2.95016 10.22 3.19795 10.3573C3.44574 10.4946 3.63119 10.6825 3.75429 10.9208C3.8774 11.1575 3.93895 11.4243 3.93895 11.721C3.93895 12.0193 3.87661 12.2876 3.75192 12.5259C3.62882 12.7626 3.44258 12.9505 3.19321 13.0893C2.94542 13.2267 2.63371 13.2953 2.25808 13.2953H1.06963V12.675H2.19179C2.43011 12.675 2.62345 12.634 2.77181 12.5519C2.92017 12.4683 3.02907 12.3547 3.09851 12.211C3.16796 12.0674 3.20268 11.9041 3.20268 11.721C3.20268 11.5379 3.16796 11.3753 3.09851 11.2333C3.02907 11.0912 2.91938 10.98 2.76944 10.8995C2.62108 10.819 2.42538 10.7787 2.18232 10.7787H1.26376V14.6341C1.26376 14.8361 1.1 14.9999 0.897994 14.9999Z" fill="currentcolor"/>
  <path d="M6.36615 14.9999H5.12988C4.94579 14.9999 4.79655 14.8506 4.79655 14.6665V10.4847C4.79655 10.3006 4.94579 10.1514 5.12988 10.1514H6.41587C6.89093 10.1514 7.29892 10.2484 7.63983 10.4426C7.98073 10.6351 8.24194 10.9121 8.42344 11.2735C8.60652 11.6334 8.69806 12.065 8.69806 12.5685C8.69806 13.0736 8.60573 13.5076 8.42108 13.8706C8.23799 14.2336 7.97284 14.513 7.62562 14.7087C7.2784 14.9028 6.85858 14.9999 6.36615 14.9999ZM5.52808 14.3606H6.32591C6.69522 14.3606 7.0022 14.2912 7.24683 14.1523C7.49147 14.0118 7.67455 13.809 7.79608 13.5439C7.9176 13.2772 7.97837 12.952 7.97837 12.5685C7.97837 12.1881 7.9176 11.8654 7.79608 11.6002C7.67613 11.3351 7.49699 11.1338 7.25867 10.9965C7.02035 10.8592 6.72442 10.7906 6.37089 10.7906H5.52808V14.3606Z" fill="currentcolor"/>
  <path d="M9.97352 14.9999C9.77151 14.9999 9.60775 14.8361 9.60775 14.6341V10.5201C9.60775 10.3165 9.77284 10.1514 9.97649 10.1514H12.2995C12.4734 10.1514 12.6144 10.2923 12.6144 10.4662C12.6144 10.6401 12.4734 10.7811 12.2995 10.7811H10.3393V12.2584H12.0853C12.2585 12.2584 12.3989 12.3988 12.3989 12.5721C12.3989 12.7453 12.2585 12.8857 12.0853 12.8857H10.3393V14.6341C10.3393 14.8361 10.1755 14.9999 9.97352 14.9999Z" fill="currentcolor"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M0.845647 10.3595C0.78752 10.3595 0.740397 10.4067 0.740397 10.4648V14.6339C0.740397 14.7209 0.810883 14.7914 0.897831 14.7914C0.984779 14.7914 1.05526 14.7209 1.05526 14.6339V13.5035H0.861136V12.4665H1.05526V10.5702H2.18216C2.44563 10.5702 2.67929 10.6135 2.86863 10.7162L2.76928 10.8993L2.86782 10.7158C3.05311 10.8152 3.19541 10.9573 3.28552 11.1416C3.37093 11.3163 3.41085 11.511 3.41085 11.7208C3.41085 11.9307 3.37091 12.1258 3.28591 12.3016C3.1967 12.4861 3.05669 12.6302 2.87397 12.7332L2.8725 12.7341C2.68327 12.8388 2.4517 12.8832 2.19163 12.8832H1.47193V13.0868H2.25792C2.60921 13.0868 2.88344 13.0225 3.09186 12.9071C3.30811 12.7866 3.46399 12.6276 3.56692 12.4296C3.67437 12.2242 3.73045 11.9892 3.73045 11.7208C3.73045 11.4537 3.6753 11.2206 3.56929 11.0167C3.46615 10.8171 3.31075 10.6579 3.09681 10.5394C2.88848 10.424 2.61345 10.3595 2.26028 10.3595H0.845647ZM1.47193 13.5035V14.6339C1.47193 14.951 1.2149 15.208 0.897831 15.208C0.580764 15.208 0.32373 14.951 0.32373 14.6339V10.4648C0.32373 10.1765 0.5574 9.94287 0.845647 9.94287H2.26028C2.66153 9.94287 3.01151 10.0158 3.29876 10.1749C3.58032 10.331 3.79603 10.5479 3.9391 10.8248C4.07922 11.0943 4.14712 11.3945 4.14712 11.7208C4.14712 12.0489 4.07832 12.3509 3.93649 12.6221C3.79324 12.8974 3.57674 13.1139 3.29442 13.2712C3.00728 13.4303 2.65776 13.5035 2.25792 13.5035H1.47193ZM1.47193 12.4665H2.19163C2.40772 12.4665 2.56262 12.4291 2.67009 12.3699C2.78366 12.3057 2.86123 12.2227 2.91079 12.1202C2.96468 12.0087 2.99418 11.8771 2.99418 11.7208C2.99418 11.5644 2.96466 11.434 2.91119 11.3246C2.8624 11.2248 2.78532 11.1444 2.67074 11.0829L2.66993 11.0824C2.56255 11.0242 2.4048 10.9869 2.18216 10.9869H1.47193V12.4665ZM5.12972 10.3595C5.06068 10.3595 5.00472 10.4155 5.00472 10.4845V14.6664C5.00472 14.7354 5.06068 14.7914 5.12972 14.7914H6.36599C6.83103 14.7914 7.21395 14.6998 7.52348 14.5268C7.83381 14.3518 8.07008 14.1034 8.2349 13.7766C8.40152 13.4491 8.48957 13.0483 8.48957 12.5683C8.48957 12.0898 8.40256 11.6921 8.2376 11.3678L8.2371 11.3669C8.07351 11.0411 7.84086 10.7953 7.53721 10.6238C7.23372 10.451 6.86222 10.3595 6.4157 10.3595H5.12972ZM4.58805 10.4845C4.58805 10.1854 4.83057 9.94287 5.12972 9.94287H6.4157C6.91932 9.94287 7.36443 10.0459 7.74275 10.2614C8.12045 10.4748 8.40991 10.7827 8.60921 11.1794C8.81024 11.5747 8.90623 12.0402 8.90623 12.5683C8.90623 13.0985 8.80929 13.5664 8.6066 13.9649C8.40533 14.3637 8.11151 14.6737 7.72775 14.89C7.34289 15.1052 6.88564 15.208 6.36599 15.208H5.12972C4.83057 15.208 4.58805 14.9655 4.58805 14.6664V10.4845ZM9.97632 10.3595C9.88773 10.3595 9.81592 10.4314 9.81592 10.5199V14.6339C9.81592 14.7209 9.8864 14.7914 9.97335 14.7914C10.0603 14.7914 10.1308 14.7209 10.1308 14.6339V12.6772H12.0851C12.1433 12.6772 12.1904 12.6301 12.1904 12.5719C12.1904 12.5137 12.1433 12.4665 12.0851 12.4665H10.1308V10.5726H12.2993C12.3582 10.5726 12.4059 10.5249 12.4059 10.4661C12.4059 10.4072 12.3582 10.3595 12.2993 10.3595H9.97632ZM9.39925 10.5199C9.39925 10.2012 9.65761 9.94287 9.97632 9.94287H12.2993C12.5883 9.94287 12.8225 10.1771 12.8225 10.4661C12.8225 10.755 12.5883 10.9893 12.2993 10.9893H10.5475V12.0499H12.0851C12.3734 12.0499 12.6071 12.2836 12.6071 12.5719C12.6071 12.8602 12.3734 13.0939 12.0851 13.0939H10.5475V14.6339C10.5475 14.951 10.2904 15.208 9.97335 15.208C9.65629 15.208 9.39925 14.951 9.39925 14.6339V10.5199ZM5.31959 10.5821H6.37072C6.75057 10.5821 7.08476 10.6558 7.36251 10.8159C7.64078 10.9762 7.84871 11.2115 7.98554 11.5138C8.12257 11.813 8.18654 12.167 8.18654 12.5683C8.18654 12.9729 8.12257 13.3293 7.9855 13.6301C7.84654 13.9333 7.63401 14.17 7.35041 14.3328L7.34953 14.3333C7.06522 14.4947 6.72017 14.5688 6.32574 14.5688H5.31959V10.5821ZM5.73625 10.9987V14.1522H6.32574C6.66971 14.1522 6.93848 14.0874 7.14339 13.9712C7.3488 13.8532 7.50245 13.6839 7.60648 13.457C7.71237 13.2245 7.76987 12.9307 7.76987 12.5683C7.76987 12.2092 7.71238 11.9178 7.60652 11.6869L7.6061 11.6859C7.50303 11.4581 7.35275 11.2911 7.1545 11.1769C6.95561 11.0623 6.69795 10.9987 6.37072 10.9987H5.73625Z" fill="currentcolor"/>
  </svg>
  `,
  height: 18,
  width: 18,
});

addIcon(IconKey.Printer, {
  body: `
  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
  <path d="M13.9582 6.45817H6.0415C5.69984 6.45817 5.4165 6.17484 5.4165 5.83317V4.1665C5.4165 2.03317 6.40817 1.0415 8.5415 1.0415H11.4582C13.5915 1.0415 14.5832 2.03317 14.5832 4.1665V5.83317C14.5832 6.17484 14.2998 6.45817 13.9582 6.45817ZM6.6665 5.20817H13.3332V4.1665C13.3332 2.74984 12.8748 2.2915 11.4582 2.2915H8.5415C7.12484 2.2915 6.6665 2.74984 6.6665 4.1665V5.20817Z" fill="currentcolor"/>
  <path d="M10.8332 18.9583H9.1665C7.14984 18.9583 6.0415 17.85 6.0415 15.8333V12.5C6.0415 12.1583 6.32484 11.875 6.6665 11.875H13.3332C13.6748 11.875 13.9582 12.1583 13.9582 12.5V15.8333C13.9582 17.85 12.8498 18.9583 10.8332 18.9583ZM7.2915 13.125V15.8333C7.2915 17.15 7.84984 17.7083 9.1665 17.7083H10.8332C12.1498 17.7083 12.7082 17.15 12.7082 15.8333V13.125H7.2915Z" fill="currentcolor"/>
  <path d="M15 15.6252H13.3333C12.9917 15.6252 12.7083 15.3418 12.7083 15.0002V13.1252H7.29167V15.0002C7.29167 15.3418 7.00833 15.6252 6.66667 15.6252H5C2.98333 15.6252 1.875 14.5168 1.875 12.5002V8.3335C1.875 6.31683 2.98333 5.2085 5 5.2085H15C17.0167 5.2085 18.125 6.31683 18.125 8.3335V12.5002C18.125 14.5168 17.0167 15.6252 15 15.6252ZM13.9583 14.3752H15C16.3167 14.3752 16.875 13.8168 16.875 12.5002V8.3335C16.875 7.01683 16.3167 6.4585 15 6.4585H5C3.68333 6.4585 3.125 7.01683 3.125 8.3335V12.5002C3.125 13.8168 3.68333 14.3752 5 14.3752H6.04167V12.5002C6.04167 12.1585 6.325 11.8752 6.66667 11.8752H13.3333C13.675 11.8752 13.9583 12.1585 13.9583 12.5002V14.3752Z" fill="currentcolor"/>
  <path d="M14.1668 13.125H5.8335C5.49183 13.125 5.2085 12.8417 5.2085 12.5C5.2085 12.1583 5.49183 11.875 5.8335 11.875H14.1668C14.5085 11.875 14.7918 12.1583 14.7918 12.5C14.7918 12.8417 14.5085 13.125 14.1668 13.125Z" fill="currentcolor"/>
  <path d="M8.3335 9.7915H5.8335C5.49183 9.7915 5.2085 9.50817 5.2085 9.1665C5.2085 8.82484 5.49183 8.5415 5.8335 8.5415H8.3335C8.67516 8.5415 8.9585 8.82484 8.9585 9.1665C8.9585 9.50817 8.67516 9.7915 8.3335 9.7915Z" fill="currentcolor"/>
  </svg>
  `,
  height: 18,
  width: 18,
});

addIcon(IconKey.Question, {
  body: `
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M12 22.75C6.07 22.75 1.25 17.93 1.25 12C1.25 6.07 6.07 1.25 12 1.25C17.93 1.25 22.75 6.07 22.75 12C22.75 17.93 17.93 22.75 12 22.75ZM12 2.75C6.9 2.75 2.75 6.9 2.75 12C2.75 17.1 6.9 21.25 12 21.25C17.1 21.25 21.25 17.1 21.25 12C21.25 6.9 17.1 2.75 12 2.75Z" fill="currentcolor"/>
<path d="M12.0252 14.0232C11.5487 14.0232 11.1536 13.6281 11.1536 13.1517V12.9077C11.1536 11.5597 12.1414 10.8973 12.5132 10.6416C12.9432 10.3511 13.0826 10.1536 13.0826 9.85141C13.0826 9.27038 12.6062 8.79389 12.0252 8.79389C11.4441 8.79389 10.9677 9.27038 10.9677 9.85141C10.9677 10.3279 10.5726 10.723 10.0962 10.723C9.61971 10.723 9.22461 10.3279 9.22461 9.85141C9.22461 8.30586 10.4796 7.05078 12.0252 7.05078C13.5707 7.05078 14.8257 8.30586 14.8257 9.85141C14.8257 11.1762 13.8496 11.8385 13.4894 12.0826C13.0362 12.3847 12.8967 12.5823 12.8967 12.9077V13.1517C12.8967 13.6397 12.5016 14.0232 12.0252 14.0232Z" fill="currentcolor"/>
<path d="M12.0259 16.9169C11.5378 16.9169 11.1543 16.5218 11.1543 16.0454C11.1543 15.5689 11.5494 15.1738 12.0259 15.1738C12.5023 15.1738 12.8974 15.5689 12.8974 16.0454C12.8974 16.5218 12.5139 16.9169 12.0259 16.9169Z" fill="currentcolor"/>
</svg>
  `,
  height: 24,
  width: 24,
});

addIcon(IconKey.TotalBooking, {
  body: `<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
  <path d="M15.8872 0.605469H4.11281C1.845 0.605469 0 2.45047 0 4.71828C0 6.98609 1.845 8.83109 4.11281 8.83109H7.10746V12.1539L6.96125 12.0312C6.23848 11.4247 5.10746 11.474 4.44023 12.1413C3.74102 12.8405 3.7868 13.9518 4.54715 14.5898L7.10742 16.7381V18.8086C7.10742 19.1322 7.36977 19.3945 7.69336 19.3945H14.7246C15.0482 19.3945 15.3105 19.1322 15.3105 18.8086V12.2244C15.3105 11.3394 14.6483 10.5895 13.77 10.48L10.623 10.088V8.83102H15.8872C18.155 8.83102 20 6.98602 20 4.7182C20 2.45039 18.155 0.605469 15.8872 0.605469ZM8.2793 18.2227V17.0508H14.1387V18.2227H8.2793ZM13.6252 11.6429C13.9179 11.6794 14.1387 11.9294 14.1387 12.2244V15.8789H7.90664L5.30039 13.692C5.06906 13.4979 5.05504 13.1837 5.26887 12.9699C5.50902 12.7298 5.94785 12.7106 6.20801 12.9289L7.31676 13.8593C7.69707 14.1783 8.27934 13.9077 8.27934 13.4104V5.91797C8.27934 5.59488 8.54219 5.33203 8.86527 5.33203C9.18836 5.33203 9.45121 5.59488 9.45121 5.91797V10.6055C9.45121 10.9011 9.67141 11.1504 9.96473 11.1869L13.6252 11.6429ZM15.8872 7.65918H10.623V5.91797C10.623 4.94871 9.83449 4.16016 8.86523 4.16016C7.89598 4.16016 7.10742 4.94871 7.10742 5.91797V7.65918H4.11277C2.49117 7.65918 1.17188 6.33988 1.17188 4.71828C1.17188 3.09668 2.49117 1.77734 4.11281 1.77734H15.8872C17.5088 1.77734 18.8281 3.09664 18.8281 4.71828C18.8281 6.33992 17.5088 7.65918 15.8872 7.65918Z" fill="#8E9193"/>
  <path d="M15.4412 3.12087L13.5117 5.05028L12.7542 4.29274C12.5254 4.06392 12.1544 4.06392 11.9255 4.29274C11.6967 4.52157 11.6967 4.89255 11.9255 5.12138L13.0974 6.29325C13.3262 6.52208 13.6972 6.52212 13.926 6.29325L16.2698 3.9495C16.4986 3.72067 16.4986 3.3497 16.2698 3.12087C16.041 2.892 15.67 2.892 15.4412 3.12087Z" fill="#8E9193"/>
  </svg>`,
  height: 20,
  width: 20,
});

addIcon(IconKey.TotalBookingsDashboard, {
  body: `<svg width="56" height="56" viewBox="0 0 56 56" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="56" height="56" rx="12" fill="#E5F1FF"/>
  <path d="M21.3337 14.6665H20.0003C16.0003 14.6665 14.667 17.0532 14.667 19.9998V21.3332V39.9998C14.667 41.1065 15.9203 41.7332 16.8003 41.0665L19.0803 39.3598C19.6137 38.9598 20.3603 39.0132 20.8403 39.4932L23.0537 41.7198C23.5737 42.2398 24.427 42.2398 24.947 41.7198L27.187 39.4798C27.6537 39.0132 28.4003 38.9598 28.9203 39.3598L31.2003 41.0665C32.0803 41.7198 33.3337 41.0932 33.3337 39.9998V17.3332C33.3337 15.8665 34.5337 14.6665 36.0003 14.6665H21.3337ZM19.9603 30.6798C19.227 30.6798 18.627 30.0798 18.627 29.3465C18.627 28.6132 19.227 28.0132 19.9603 28.0132C20.6937 28.0132 21.2937 28.6132 21.2937 29.3465C21.2937 30.0798 20.6937 30.6798 19.9603 30.6798ZM19.9603 25.3465C19.227 25.3465 18.627 24.7465 18.627 24.0132C18.627 23.2798 19.227 22.6798 19.9603 22.6798C20.6937 22.6798 21.2937 23.2798 21.2937 24.0132C21.2937 24.7465 20.6937 25.3465 19.9603 25.3465ZM28.0003 30.3465H24.0003C23.4537 30.3465 23.0003 29.8932 23.0003 29.3465C23.0003 28.7998 23.4537 28.3465 24.0003 28.3465H28.0003C28.547 28.3465 29.0003 28.7998 29.0003 29.3465C29.0003 29.8932 28.547 30.3465 28.0003 30.3465ZM28.0003 25.0132H24.0003C23.4537 25.0132 23.0003 24.5598 23.0003 24.0132C23.0003 23.4665 23.4537 23.0132 24.0003 23.0132H28.0003C28.547 23.0132 29.0003 23.4665 29.0003 24.0132C29.0003 24.5598 28.547 25.0132 28.0003 25.0132Z" fill="#007AFF"/>
  <path d="M36.013 14.6665V16.6665C36.893 16.6665 37.733 17.0265 38.3463 17.6265C38.9863 18.2798 39.333 19.1198 39.333 19.9998V23.2265C39.333 24.2132 38.893 24.6665 37.893 24.6665H35.333V17.3465C35.333 16.9732 35.6397 16.6665 36.013 16.6665V14.6665ZM36.013 14.6665C34.533 14.6665 33.333 15.8665 33.333 17.3465V26.6665H37.893C39.9997 26.6665 41.333 25.3332 41.333 23.2265V19.9998C41.333 18.5332 40.733 17.1998 39.773 16.2265C38.7997 15.2665 37.4797 14.6798 36.013 14.6665C36.0263 14.6665 36.013 14.6665 36.013 14.6665Z" fill="#007AFF"/>
  </svg>`,
  height: 56,
  width: 56,
});

addIcon(IconKey.TotalRevenueDashboard, {
  body: `<svg width="57" height="56" viewBox="0 0 57 56" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect x="0.333008" width="56" height="56" rx="12" fill="#FFF4E5"/>
  <path d="M37.4131 20.3333H39.2264C38.9731 19.9733 38.7064 19.64 38.4264 19.3066L37.4131 20.3333Z" fill="#FF9500"/>
  <path d="M37.0267 17.8933C36.6933 17.6133 36.36 17.3466 36 17.0933V18.9066L37.0267 17.8933Z" fill="#FF9500"/>
  <path d="M38.4397 19.3066L42.373 15.3733C42.7597 14.9866 42.7597 14.3466 42.373 13.9599C41.9864 13.5733 41.3464 13.5733 40.9597 13.9599L37.0264 17.8933C37.533 18.3333 37.9997 18.8133 38.4397 19.3066Z" fill="#FF9500"/>
  <path d="M35.9994 16C35.9994 15.4533 35.546 15 34.9994 15C34.466 15 34.0394 15.4267 34.0127 15.9467C34.706 16.28 35.3727 16.6533 35.9994 17.0933V16Z" fill="#FF9500"/>
  <path d="M41.3332 21.3335C41.3332 20.7868 40.8799 20.3335 40.3332 20.3335H39.2266C39.6666 20.9602 40.0532 21.6268 40.3732 22.3202C40.9066 22.2935 41.3332 21.8668 41.3332 21.3335Z" fill="#FF9500"/>
  <path d="M29.333 31.6666H29.733C30.253 31.6666 30.6663 31.1999 30.6663 30.6266C30.6663 29.9066 30.4663 29.7999 30.013 29.6399L29.333 29.3999V31.6666Z" fill="#FF9500"/>
  <path d="M40.3867 22.3198C40.3733 22.3198 40.36 22.3332 40.3333 22.3332H35C34.8667 22.3332 34.7467 22.3065 34.6133 22.2532C34.3733 22.1465 34.1733 21.9598 34.0667 21.7065C34.0267 21.5865 34 21.4665 34 21.3332V15.9998C34 15.9865 34.0133 15.9732 34.0133 15.9465C32.28 15.1332 30.36 14.6665 28.3333 14.6665C20.9733 14.6665 15 20.6398 15 27.9998C15 35.3598 20.9733 41.3332 28.3333 41.3332C35.6933 41.3332 41.6667 35.3598 41.6667 27.9998C41.6667 25.9732 41.2 24.0532 40.3867 22.3198ZM30.6667 27.7598C31.52 28.0532 32.6667 28.6798 32.6667 30.6398C32.6667 32.3065 31.3467 33.6798 29.7333 33.6798H29.3333V34.0132C29.3333 34.5598 28.88 35.0132 28.3333 35.0132C27.7867 35.0132 27.3333 34.5598 27.3333 34.0132V33.6798H27.2267C25.4533 33.6798 24 32.1865 24 30.3465C24 29.7865 24.4533 29.3332 25 29.3332C25.5467 29.3332 26 29.7865 26 30.3332C26 31.0665 26.5467 31.6665 27.2267 31.6665H27.3333V28.7065L26 28.2398C25.1467 27.9465 24 27.3198 24 25.3598C24 23.6932 25.32 22.3198 26.9333 22.3198H27.3333V21.9998C27.3333 21.4532 27.7867 20.9998 28.3333 20.9998C28.88 20.9998 29.3333 21.4532 29.3333 21.9998V22.3332H29.44C31.2133 22.3332 32.6667 23.8265 32.6667 25.6665C32.6667 26.2132 32.2133 26.6665 31.6667 26.6665C31.12 26.6665 30.6667 26.2132 30.6667 25.6665C30.6667 24.9332 30.12 24.3332 29.44 24.3332H29.3333V27.2932L30.6667 27.7598Z" fill="#FF9500"/>
  <path d="M26 25.3735C26 26.0935 26.2 26.2002 26.6533 26.3602L27.3333 26.6002V24.3335H26.9333C26.4267 24.3335 26 24.8002 26 25.3735Z" fill="#FF9500"/>
  </svg>`,
  height: 56,
  width: 56,
});

addIcon(IconKey.TotalCustomersDashboard, {
  body: `<svg width="57" height="56" viewBox="0 0 57 56" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect x="0.666992" width="56" height="56" rx="12" fill="#EAF9EE"/>
  <path d="M36.0404 22.3598C35.947 22.3465 35.8537 22.3465 35.7604 22.3598C33.6937 22.2932 32.0537 20.5998 32.0537 18.5198C32.0537 16.3998 33.7737 14.6665 35.907 14.6665C38.027 14.6665 39.7604 16.3865 39.7604 18.5198C39.747 20.5998 38.107 22.2932 36.0404 22.3598Z" fill="#34C759"/>
  <path d="M40.3874 31.6C38.8941 32.6 36.8008 32.9734 34.8674 32.72C35.3741 31.6267 35.6408 30.4134 35.6541 29.1334C35.6541 27.8001 35.3608 26.5334 34.8008 25.4267C36.7741 25.16 38.8674 25.5334 40.3741 26.5334C42.4808 27.92 42.4808 30.2 40.3874 31.6Z" fill="#34C759"/>
  <path d="M21.2535 22.3598C21.3469 22.3465 21.4402 22.3465 21.5335 22.3598C23.6002 22.2932 25.2402 20.5998 25.2402 18.5198C25.2402 16.3865 23.5202 14.6665 21.3869 14.6665C19.2669 14.6665 17.5469 16.3865 17.5469 18.5198C17.5469 20.5998 19.1869 22.2932 21.2535 22.3598Z" fill="#34C759"/>
  <path d="M21.4005 29.1331C21.4005 30.4265 21.6805 31.6531 22.1871 32.7598C20.3071 32.9598 18.3471 32.5598 16.9071 31.6131C14.8005 30.2131 14.8005 27.9331 16.9071 26.5331C18.3338 25.5731 20.3471 25.1865 22.2405 25.3998C21.6938 26.5198 21.4005 27.7865 21.4005 29.1331Z" fill="#34C759"/>
  <path d="M28.8274 33.16C28.7207 33.1467 28.6007 33.1467 28.4807 33.16C26.0274 33.08 24.0674 31.0667 24.0674 28.5867C24.0807 26.0533 26.1207 24 28.6674 24C31.2007 24 33.254 26.0533 33.254 28.5867C33.2407 31.0667 31.294 33.08 28.8274 33.16Z" fill="#34C759"/>
  <path d="M24.4934 35.92C22.4801 37.2667 22.4801 39.48 24.4934 40.8134C26.7867 42.3467 30.5467 42.3467 32.8401 40.8134C34.8534 39.4667 34.8534 37.2534 32.8401 35.92C30.5601 34.3867 26.8001 34.3867 24.4934 35.92Z" fill="#34C759"/>
  </svg>`,
  height: 56,
  width: 56,
});

addIcon(IconKey.LastTimeDashboard, {
  body: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_16939_52338)">
<path d="M11.8912 12.9788L16.3912 16.3538C16.7227 16.6024 17.1926 16.5352 17.4412 16.2037C17.6899 15.8722 17.6228 15.4024 17.2913 15.1537L13.125 12V5.625C13.125 5.21062 12.7894 4.875 12.375 4.875C11.9606 4.875 11.625 5.21062 11.625 5.625V12.375C11.625 12.624 11.7292 12.8483 11.8912 12.9788Z" fill="#BBBDBE"/>
<path d="M12.7492 0.75C7.25953 0.75 2.57165 4.71225 1.65665 10.125L1.37165 9.70125C1.13953 9.35738 0.673028 9.26663 0.329153 9.49875C-0.014722 9.73088 -0.105472 10.1974 0.126653 10.5413L1.62665 12.7913C1.75003 12.9788 1.95103 13.1014 2.17415 13.125H2.24915C2.4479 13.1243 2.63803 13.0448 2.7779 12.9037L4.6529 11.0287C4.94578 10.7359 4.94578 10.2604 4.6529 9.9675C4.36003 9.67463 3.88453 9.67463 3.59165 9.9675L3.12665 10.4362C3.99028 5.121 8.99915 1.51275 14.314 2.37637C19.6289 3.24 23.2379 8.2485 22.3743 13.5637C21.6074 18.2835 17.5308 21.7507 12.7492 21.75C9.7334 21.8048 6.88115 20.382 5.1104 17.94C4.87003 17.6025 4.40165 17.5234 4.06415 17.7638C3.72665 18.0041 3.64753 18.4725 3.8879 18.81C5.9384 21.6495 9.2474 23.3074 12.7492 23.25C18.9625 23.25 23.9992 18.2134 23.9992 12C23.9992 5.78662 18.9625 0.75 12.7492 0.75Z" fill="#BBBDBE"/>
</g>
<defs>
<clipPath id="clip0_16939_52338">
<rect width="24" height="24" fill="white"/>
</clipPath>
</defs>
</svg>`,
  height: 24,
  width: 24,
});

addIcon(IconKey.UpdateAvatar, {
  body: `<svg width="70" height="70" viewBox="0 0 70 70" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="64" height="64" rx="32" fill="#EDEDED"/>
<path d="M45.3602 38.4267L41.1868 28.6667C40.4268 26.88 39.2935 25.8667 38.0002 25.8C36.7202 25.7333 35.4802 26.6267 34.5335 28.3333L32.0002 32.88C31.4668 33.84 30.7068 34.4133 29.8802 34.48C29.0402 34.56 28.2002 34.12 27.5202 33.2533L27.2268 32.88C26.2802 31.6933 25.1068 31.12 23.9068 31.24C22.7069 31.36 21.6802 32.1867 21.0002 33.5333L18.6935 38.1333C17.8669 39.8 17.9469 41.7333 18.9202 43.3067C19.8935 44.88 21.5868 45.8267 23.4402 45.8267H40.4535C42.2402 45.8267 43.9068 44.9333 44.8935 43.44C45.9068 41.9467 46.0668 40.0667 45.3602 38.4267Z" fill="#1E2328"/>
<path d="M25.2938 27.1733C27.7827 27.1733 29.8004 25.1556 29.8004 22.6667C29.8004 20.1777 27.7827 18.16 25.2938 18.16C22.8048 18.16 20.7871 20.1777 20.7871 22.6667C20.7871 25.1556 22.8048 27.1733 25.2938 27.1733Z" fill="#1E2328"/>
<g filter="url(#filter0_dd_14302_31814)">
<rect x="44" y="44" width="20" height="20" rx="10" fill="#ED1C24"/>
<rect x="45" y="45" width="18" height="18" rx="9" stroke="white" stroke-width="2"/>
<path d="M58.3413 49.6605C57.1428 48.462 55.969 48.4311 54.7397 49.6605L53.9921 50.408C53.9304 50.4698 53.9057 50.5686 53.9304 50.6551C54.3999 52.2922 55.7096 53.6019 57.3467 54.0714C57.3714 54.0776 57.3961 54.0838 57.4208 54.0838C57.4888 54.0838 57.5506 54.0591 57.6 54.0096L58.3413 53.2621C58.9529 52.6567 59.2495 52.0698 59.2495 51.4767C59.2556 50.8651 58.9591 50.2721 58.3413 49.6605Z" fill="white"/>
<path d="M56.2351 54.6335C56.056 54.547 55.883 54.4606 55.7162 54.3617C55.5803 54.2814 55.4506 54.1949 55.3208 54.1022C55.2158 54.0343 55.0922 53.9354 54.9749 53.8366C54.9625 53.8304 54.9193 53.7933 54.8698 53.7439C54.666 53.5709 54.4374 53.3485 54.2335 53.1014C54.215 53.0891 54.1841 53.0458 54.1409 52.9902C54.0791 52.9161 53.9741 52.7925 53.8814 52.6505C53.8073 52.5578 53.7208 52.4219 53.6405 52.286C53.5416 52.1192 53.4551 51.9524 53.3686 51.7794C53.2552 51.5364 52.9363 51.4642 52.7467 51.6538L49.2728 55.1278C49.1924 55.2081 49.1183 55.3625 49.0998 55.4675L48.7662 57.8336C48.7044 58.2537 48.8218 58.6491 49.0812 58.9147C49.3036 59.131 49.6125 59.2483 49.9461 59.2483C50.0203 59.2483 50.0944 59.2422 50.1685 59.2298L52.5408 58.8962C52.652 58.8777 52.8065 58.8035 52.8806 58.7232L56.3603 55.2436C56.5461 55.0577 56.4763 54.738 56.2351 54.6335Z" fill="white"/>
</g>
<defs>
<filter id="filter0_dd_14302_31814" x="38" y="38" width="32" height="32" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="2" operator="erode" in="SourceAlpha" result="effect1_dropShadow_14302_31814"/>
<feOffset/>
<feGaussianBlur stdDeviation="2"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.06 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_14302_31814"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="2" operator="erode" in="SourceAlpha" result="effect2_dropShadow_14302_31814"/>
<feOffset/>
<feGaussianBlur stdDeviation="4"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_14302_31814" result="effect2_dropShadow_14302_31814"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_14302_31814" result="shape"/>
</filter>
</defs>
</svg>`,
  height: 64,
  width: 64,
});

addIcon(IconKey.UpdateEmployee, {
  body: `<svg width="70" height="70" viewBox="0 0 70 70" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="64" height="64" rx="32" fill="#EDEDED"/>
<path d="M45.3337 32.0001C45.3337 24.6534 39.347 18.6667 32.0003 18.6667C24.6537 18.6667 18.667 24.6534 18.667 32.0001C18.667 35.8667 20.3337 39.3467 22.9737 41.7867C22.9737 41.8001 22.9737 41.8001 22.9603 41.8134C23.0937 41.9467 23.2537 42.0534 23.387 42.1734C23.467 42.2401 23.5337 42.3067 23.6137 42.3601C23.8537 42.5601 24.1203 42.7467 24.3737 42.9334C24.467 43.0001 24.547 43.0534 24.6403 43.1201C24.8937 43.2934 25.1603 43.4534 25.4403 43.6001C25.5337 43.6534 25.6403 43.7201 25.7337 43.7734C26.0003 43.9201 26.2803 44.0534 26.5737 44.1734C26.6803 44.2267 26.787 44.2801 26.8937 44.3201C27.187 44.4401 27.4803 44.5467 27.7737 44.6401C27.8803 44.6801 27.987 44.7201 28.0937 44.7467C28.4137 44.8401 28.7337 44.9201 29.0537 45.0001C29.147 45.0267 29.2403 45.0534 29.347 45.0667C29.7203 45.1467 30.0937 45.2001 30.4803 45.2401C30.5337 45.2401 30.587 45.2534 30.6403 45.2668C31.0937 45.3067 31.547 45.3334 32.0003 45.3334C32.4537 45.3334 32.907 45.3067 33.347 45.2668C33.4003 45.2668 33.4537 45.2534 33.507 45.2401C33.8937 45.2001 34.267 45.1467 34.6403 45.0667C34.7337 45.0534 34.827 45.0134 34.9337 45.0001C35.2537 44.9201 35.587 44.8534 35.8937 44.7467C36.0003 44.7067 36.107 44.6667 36.2137 44.6401C36.507 44.5334 36.8137 44.4401 37.0937 44.3201C37.2003 44.2801 37.307 44.2267 37.4137 44.1734C37.6937 44.0534 37.9737 43.9201 38.2537 43.7734C38.3603 43.7201 38.4537 43.6534 38.547 43.6001C38.8137 43.4401 39.0803 43.2934 39.347 43.1201C39.4403 43.0667 39.5203 43.0001 39.6137 42.9334C39.8803 42.7467 40.1337 42.5601 40.3737 42.3601C40.4537 42.2934 40.5203 42.2267 40.6003 42.1734C40.747 42.0534 40.8937 41.9334 41.027 41.8134C41.027 41.8001 41.027 41.8001 41.0137 41.7867C43.667 39.3467 45.3337 35.8667 45.3337 32.0001ZM38.587 38.6267C34.9737 36.2001 29.0537 36.2001 25.4137 38.6267C24.827 39.0134 24.347 39.4667 23.947 39.9601C21.9203 37.9067 20.667 35.0934 20.667 32.0001C20.667 25.7467 25.747 20.6667 32.0003 20.6667C38.2537 20.6667 43.3337 25.7467 43.3337 32.0001C43.3337 35.0934 42.0803 37.9067 40.0537 39.9601C39.667 39.4667 39.1737 39.0134 38.587 38.6267Z" fill="#1E2328"/>
<path d="M32 25.24C29.24 25.24 27 27.48 27 30.24C27 32.9467 29.12 35.1467 31.9333 35.2267C31.9733 35.2267 32.0267 35.2267 32.0533 35.2267C32.08 35.2267 32.12 35.2267 32.1467 35.2267C32.16 35.2267 32.1733 35.2267 32.1733 35.2267C34.8667 35.1333 36.9867 32.9467 37 30.24C37 27.48 34.76 25.24 32 25.24Z" fill="#1E2328"/>
<g filter="url(#filter0_dd_15142_72115)">
<rect x="44" y="44" width="20" height="20" rx="10" fill="#ED1C24"/>
<rect x="45" y="45" width="18" height="18" rx="9" stroke="white" stroke-width="2"/>
<path d="M58.3413 49.6605C57.1428 48.462 55.969 48.4311 54.7397 49.6605L53.9921 50.408C53.9304 50.4698 53.9057 50.5686 53.9304 50.6551C54.3999 52.2922 55.7096 53.6019 57.3467 54.0714C57.3714 54.0776 57.3961 54.0838 57.4208 54.0838C57.4888 54.0838 57.5506 54.0591 57.6 54.0097L58.3413 53.2622C58.9529 52.6567 59.2495 52.0698 59.2495 51.4768C59.2556 50.8652 58.9591 50.2721 58.3413 49.6605Z" fill="white"/>
<path d="M56.2351 54.6335C56.056 54.547 55.883 54.4605 55.7162 54.3617C55.5803 54.2813 55.4506 54.1949 55.3208 54.1022C55.2158 54.0342 55.0922 53.9354 54.9749 53.8365C54.9625 53.8304 54.9193 53.7933 54.8698 53.7439C54.666 53.5709 54.4374 53.3485 54.2335 53.1014C54.215 53.089 54.1841 53.0458 54.1409 52.9902C54.0791 52.9161 53.9741 52.7925 53.8814 52.6504C53.8073 52.5577 53.7208 52.4218 53.6405 52.2859C53.5416 52.1191 53.4551 51.9523 53.3686 51.7793C53.2552 51.5363 52.9363 51.4642 52.7467 51.6538L49.2728 55.1277C49.1924 55.208 49.1183 55.3625 49.0998 55.4675L48.7662 57.8336C48.7044 58.2537 48.8218 58.6491 49.0812 58.9147C49.3036 59.1309 49.6125 59.2483 49.9461 59.2483C50.0203 59.2483 50.0944 59.2421 50.1685 59.2298L52.5408 58.8962C52.652 58.8776 52.8065 58.8035 52.8806 58.7232L56.3603 55.2435C56.5461 55.0577 56.4763 54.738 56.2351 54.6335Z" fill="white"/>
</g>
<defs>
<filter id="filter0_dd_15142_72115" x="38" y="38" width="32" height="32" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="2" operator="erode" in="SourceAlpha" result="effect1_dropShadow_15142_72115"/>
<feOffset/>
<feGaussianBlur stdDeviation="2"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.06 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_15142_72115"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="2" operator="erode" in="SourceAlpha" result="effect2_dropShadow_15142_72115"/>
<feOffset/>
<feGaussianBlur stdDeviation="4"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_15142_72115" result="effect2_dropShadow_15142_72115"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_15142_72115" result="shape"/>
</filter>
</defs>
</svg>`,
  height: 64,
  width: 64,
});

addIcon(IconKey.Price, {
  body: ` 
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M5 14.375H4.375C4.02938 14.375 3.75 14.095 3.75 13.75C3.75 13.405 4.02938 13.125 4.375 13.125H5C5.34562 13.125 5.625 13.405 5.625 13.75C5.625 14.095 5.34562 14.375 5 14.375ZM16.25 6.25C16.25 5.905 15.9706 5.625 15.625 5.625H15C14.6544 5.625 14.375 5.905 14.375 6.25C14.375 6.595 14.6544 6.875 15 6.875H15.625C15.9706 6.875 16.25 6.595 16.25 6.25Z" fill="#8E9193"/>
<path d="M17.312 3.50252C16.1383 3.2244 14.9376 3.0944 13.7351 3.12565C12.4045 3.15627 11.0845 3.37627 9.81139 3.7794C8.65139 4.1469 7.44764 4.34752 6.23514 4.37565C5.32201 4.39565 4.39326 4.31877 3.48889 4.14252C2.93326 4.03315 2.36451 4.17752 1.92951 4.53565C1.49764 4.8919 1.24951 5.41815 1.24951 5.98002V14.6731C1.24951 15.5469 1.84076 16.2975 2.68764 16.4981C3.75514 16.7513 4.84701 16.8788 5.93889 16.8788C6.04764 16.8788 6.15576 16.8775 6.26451 16.875C7.59514 16.8444 8.91514 16.6244 10.1883 16.2213C11.3483 15.8538 12.552 15.6531 13.7645 15.625C14.6789 15.6075 15.6064 15.6825 16.5108 15.8581C17.0651 15.9669 17.6351 15.8225 18.0701 15.465C18.502 15.1088 18.7501 14.5825 18.7501 14.0206V5.32752C18.7501 4.45377 18.1589 3.70315 17.312 3.50252ZM17.5001 14.0206C17.5001 14.2763 17.3589 14.4306 17.2751 14.5C17.1301 14.6194 16.9408 14.6681 16.7501 14.6313C15.757 14.4381 14.7389 14.3506 13.7358 14.3756C12.4051 14.4063 11.0851 14.6263 9.81201 15.0294C8.65201 15.3969 7.44826 15.5975 6.23576 15.6256C5.14264 15.6488 4.04576 15.535 2.97639 15.2819C2.69639 15.2156 2.50014 14.965 2.50014 14.6731V5.9794C2.50014 5.72377 2.64139 5.5694 2.72514 5.50002C2.83889 5.4069 2.97951 5.3569 3.12639 5.3569C3.16701 5.3569 3.20889 5.36065 3.25014 5.36877C4.24326 5.5619 5.26201 5.65002 6.26451 5.6244C7.59514 5.59377 8.91514 5.37377 10.1883 4.97065C11.3483 4.60315 12.552 4.40252 13.7645 4.3744C14.8576 4.3494 15.9539 4.46502 17.0239 4.71815C17.3039 4.7844 17.5001 5.03502 17.5001 5.3269V14.02V14.0206ZM10.0001 7.50002C8.62139 7.50002 7.50014 8.62127 7.50014 10C7.50014 11.3788 8.62139 12.5 10.0001 12.5C11.3789 12.5 12.5001 11.3788 12.5001 10C12.5001 8.62127 11.3789 7.50002 10.0001 7.50002ZM10.0001 11.25C9.31076 11.25 8.75014 10.6894 8.75014 10C8.75014 9.31065 9.31076 8.75002 10.0001 8.75002C10.6895 8.75002 11.2501 9.31065 11.2501 10C11.2501 10.6894 10.6895 11.25 10.0001 11.25Z" fill="#8E9193"/>
</svg>`,
  height: 20,
  width: 20,
});

addIcon(IconKey.Cash, {
  body: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M23.7075 16.455C23.6148 16.2524 23.4827 16.0703 23.3189 15.9193C23.1552 15.7683 22.963 15.6513 22.7536 15.5752C22.5443 15.4992 22.3218 15.4655 22.0993 15.4762C21.8768 15.4869 21.6586 15.5417 21.4575 15.6375L16.155 17.775V17.7075C16.0892 17.1998 15.8402 16.7337 15.4549 16.3967C15.0696 16.0597 14.5744 15.8751 14.0625 15.8775C13.2 15.8775 12.165 15.6525 9.2025 14.4675C7.7025 13.9725 5.4525 14.19 4.335 14.34V14.25C4.33403 14.1745 4.31808 14.0999 4.28805 14.0307C4.25803 13.9614 4.21455 13.8988 4.16012 13.8464C4.10569 13.7941 4.04141 13.7531 3.97101 13.7258C3.9006 13.6985 3.82548 13.6855 3.75 13.6875H0.75C0.60142 13.6894 0.459472 13.7493 0.3544 13.8544C0.249329 13.9595 0.189442 14.1014 0.1875 14.25V22.155C0.1875 22.3042 0.246763 22.4473 0.352252 22.5527C0.457742 22.6582 0.600816 22.7175 0.75 22.7175H3.75C3.85272 22.7163 3.95316 22.6871 4.04042 22.6329C4.12769 22.5786 4.19844 22.5016 4.245 22.41C6.26749 22.7473 8.31458 22.9154 10.365 22.9125C13.41 22.9125 16.4625 22.53 18.2775 21.4875C20.595 20.16 22.7775 18.7575 22.7775 18.7575C22.9905 18.6683 23.1836 18.5377 23.3459 18.3734C23.5081 18.2091 23.6361 18.0143 23.7226 17.8002C23.8091 17.5861 23.8523 17.357 23.8497 17.1261C23.8471 16.8952 23.7988 16.6671 23.7075 16.455ZM3.1875 21.5925H1.3125V14.8425H3.1875V21.5925ZM22.2075 17.7825C22.2075 17.7825 20.025 19.1925 17.7075 20.5125C14.895 22.125 8.295 22.0125 4.2975 21.2625V15.48C5.25 15.3375 7.4625 15.0825 8.7975 15.525C11.9175 16.77 13.02 17.025 14.0475 17.025C14.2847 17.0211 14.5152 17.1045 14.6949 17.2594C14.8747 17.4143 14.9913 17.6298 15.0225 17.865C15.0259 17.93 15.0259 17.995 15.0225 18.06H10.5C10.3508 18.06 10.2077 18.1193 10.1023 18.2248C9.99676 18.3302 9.9375 18.4733 9.9375 18.6225C9.9375 18.7717 9.99676 18.9148 10.1023 19.0203C10.2077 19.1257 10.3508 19.185 10.5 19.185H15.6075C15.6791 19.1841 15.75 19.1714 15.8175 19.1475L21.885 16.7025C22.32 16.53 22.5825 16.7025 22.68 16.935C22.7775 17.1675 22.74 17.5125 22.2075 17.7825ZM12.8175 9.135C12.9093 9.05403 12.9813 8.95314 13.0281 8.84005C13.0749 8.72695 13.0952 8.60465 13.0875 8.4825C13.0932 8.29957 13.0319 8.12084 12.915 7.98C12.7899 7.83376 12.6369 7.71392 12.465 7.6275C12.2664 7.51935 12.0609 7.42416 11.85 7.3425C11.6325 7.26 11.4225 7.17 11.2125 7.0725C11.0087 6.98662 10.815 6.87846 10.635 6.75C10.4616 6.60949 10.3208 6.4329 10.2225 6.2325C10.1099 5.99899 10.0559 5.74158 10.065 5.4825C10.0635 5.11077 10.1967 4.75107 10.44 4.47C10.7206 4.15737 11.1036 3.95523 11.52 3.9V3.15H12.54V3.9C12.8746 3.95906 13.1833 4.11864 13.425 4.3575C13.6531 4.57358 13.8167 4.84883 13.8975 5.1525L12.96 5.46C12.8898 5.27618 12.7639 5.11886 12.6 5.01C12.4121 4.87701 12.1849 4.81098 11.955 4.8225C11.7422 4.80878 11.5309 4.86688 11.355 4.9875C11.2818 5.04322 11.2236 5.11626 11.1856 5.20004C11.1477 5.28383 11.1311 5.37574 11.1375 5.4675C11.1345 5.618 11.1911 5.7636 11.295 5.8725C11.4172 5.99683 11.5624 6.09618 11.7225 6.165C11.9091 6.26055 12.1021 6.34325 12.3 6.4125C12.5172 6.4937 12.73 6.58633 12.9375 6.69C13.1538 6.79422 13.3575 6.92253 13.545 7.0725C13.7284 7.23166 13.8788 7.42533 13.9875 7.6425C14.1072 7.90338 14.1662 8.18803 14.16 8.475C14.1675 8.79125 14.0925 9.10401 13.9425 9.3825C13.8077 9.63491 13.6084 9.8471 13.365 9.9975C13.1147 10.1584 12.8338 10.2656 12.54 10.3125V11.0625H11.52V10.2825C11.1381 10.2005 10.7856 10.0165 10.5 9.75C10.2144 9.48202 10.0004 9.14684 9.8775 8.775L10.83 8.4225C10.9482 8.68852 11.1282 8.9225 11.355 9.105C11.5699 9.27159 11.8331 9.36373 12.105 9.3675C12.3625 9.37688 12.6151 9.29448 12.8175 9.135ZM12 13.3125C13.2238 13.3125 14.4201 12.9496 15.4376 12.2697C16.4551 11.5898 17.2482 10.6235 17.7165 9.49286C18.1848 8.36224 18.3074 7.11814 18.0686 5.91788C17.8299 4.71762 17.2406 3.61512 16.3752 2.74978C15.5099 1.88444 14.4074 1.29514 13.2071 1.05639C12.0069 0.817648 10.7628 0.940181 9.63215 1.4085C8.50153 1.87682 7.53517 2.66988 6.85528 3.68741C6.17539 4.70494 5.8125 5.90123 5.8125 7.125C5.81448 8.76542 6.46702 10.3381 7.62697 11.498C8.78692 12.658 10.3596 13.3105 12 13.3125ZM12 2.0625C13.0013 2.0625 13.9801 2.35941 14.8126 2.91569C15.6451 3.47196 16.294 4.26262 16.6771 5.18767C17.0603 6.11272 17.1606 7.13062 16.9652 8.11265C16.7699 9.09468 16.2877 9.99673 15.5797 10.7047C14.8717 11.4127 13.9697 11.8949 12.9876 12.0902C12.0056 12.2856 10.9877 12.1853 10.0627 11.8021C9.13761 11.419 8.34696 10.7701 7.79068 9.93758C7.23441 9.10505 6.9375 8.12627 6.9375 7.125C6.93948 5.78295 7.47349 4.49644 8.42246 3.54747C9.37144 2.59849 10.658 2.06449 12 2.0625Z" fill="black"/>
</svg>
`,
  height: 24,
  width: 24,
});

addIcon(IconKey.MasterCard, {
  body: `<svg width="24" height="15" viewBox="0 0 24 15" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_15653_13981)">
<path d="M14.9062 1.53418H8.56055V12.8139H14.9062V1.53418Z" fill="#FF5F00"/>
<path d="M8.96348 7.17417C8.96173 4.97342 9.98314 2.8941 11.7334 1.53532C8.75997 -0.776006 4.48998 -0.439342 1.92428 2.30871C-0.641425 5.05676 -0.641425 9.29357 1.92428 12.0416C4.48998 14.7897 8.75997 15.1263 11.7334 12.815C9.98258 11.4558 8.96111 9.37562 8.96348 7.17417Z" fill="#EB001B"/>
<path d="M23.4689 7.17499C23.4688 9.92136 21.8833 12.4266 19.3858 13.6268C16.8883 14.8269 13.9172 14.5113 11.7344 12.8138C13.4839 11.4539 14.5054 9.37486 14.5054 7.17399C14.5054 4.97312 13.4839 2.89407 11.7344 1.53414C13.9172 -0.163275 16.8883 -0.478939 19.3858 0.721209C21.8833 1.92136 23.4688 4.42662 23.4689 7.17299V7.17499Z" fill="#F79E1B"/>
</g>
<defs>
<clipPath id="clip0_15653_13981">
<rect width="24" height="15" rx="4" fill="white"/>
</clipPath>
</defs>
</svg>`,
  height: 15,
  width: 24,
});

addIcon(IconKey.Visa, {
  body: `<svg width="33" height="11" viewBox="0 0 33 11" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M16.019 0.182591L13.8575 10.1775H11.2432L13.4049 0.182591H16.019ZM27.0175 6.63659L28.3938 2.88299L29.1854 6.63659H27.0175ZM29.9358 10.1775H32.3529L30.241 0.182591H28.0112C27.5084 0.182591 27.085 0.470851 26.8975 0.915285L22.9746 10.1775H25.7203L26.2653 8.68491H29.6188L29.9358 10.1775ZM23.1109 6.91456C23.1223 4.2769 19.4243 4.13063 19.4489 2.95195C19.4569 2.59376 19.8022 2.21246 20.5575 2.11494C20.9318 2.06716 21.9648 2.02851 23.1359 2.5621L23.5938 0.441714C22.9646 0.216778 22.1553 0 21.1484 0C18.564 0 16.7458 1.35778 16.7313 3.30373C16.7148 4.7427 18.0302 5.54513 19.019 6.02433C20.0384 6.51402 20.3802 6.82831 20.3752 7.26595C20.3682 7.93648 19.562 8.23348 18.8118 8.24456C17.4966 8.26495 16.7344 7.89317 16.1268 7.61287L15.6523 9.80415C16.2641 10.081 17.3915 10.3222 18.5587 10.3344C21.3061 10.3344 23.1029 8.99259 23.1109 6.91456ZM12.2835 0.182591L8.04758 10.1775H5.28441L3.19975 2.2008C3.07347 1.71053 2.9633 1.53007 2.57897 1.32301C1.95033 0.985019 0.912418 0.669176 0 0.472794L0.0616663 0.182591H4.51025C5.07702 0.182591 5.58665 0.555542 5.71626 1.20121L6.81741 6.98526L9.53699 0.182591H12.2835Z" fill="#182E66"/>
</svg>`,
  height: 11,
  width: 33,
});

addIcon(IconKey.ApplePay, {
  body: `<svg width="14" height="18" viewBox="0 0 14 18" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M10.2597 0.818359C10.2597 0.818359 10.4828 2.15175 9.41285 3.43407C8.27302 4.8051 6.97726 4.58197 6.97726 4.58197C6.97726 4.58197 6.73262 3.50396 7.68966 2.24315C8.76497 0.826424 10.2597 0.818359 10.2597 0.818359Z" fill="#090909"/>
<path d="M13.2329 6.39119C13.2329 6.39119 11.464 7.29446 11.464 9.49079C11.464 11.9667 13.6711 12.8189 13.6711 12.8189C13.6711 12.8189 12.128 17.1578 10.0473 17.1578C9.57498 17.1578 9.15506 17.0007 8.72969 16.8415C8.2931 16.6781 7.85077 16.5126 7.34017 16.5126C6.77095 16.5126 6.20422 16.7183 5.69901 16.9016C5.29278 17.049 4.92632 17.182 4.63037 17.182C2.72975 17.182 0.329102 13.0662 0.329102 9.75962C0.329102 6.50679 2.36145 4.79972 4.26476 4.79972C4.98654 4.79972 5.61298 5.04122 6.13776 5.24354C6.516 5.38935 6.84142 5.51481 7.11166 5.51481C7.32856 5.51481 7.61803 5.39811 7.97023 5.25612C8.51728 5.03557 9.21566 4.75402 10.0285 4.75402C12.327 4.75402 13.2329 6.39119 13.2329 6.39119Z" fill="#090909"/>
</svg>`,
  height: 18,
  width: 14,
});

addIcon(IconKey.AmericanExpress, {
  body: `<svg class="SVGInline-svg SVGInline--cleaned-svg SVG-svg BrandIcon-svg BrandIcon--size--32-svg" height="32" width="32" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32"><g fill="none" fill-rule="evenodd"><path fill="#0193CE" d="M0 0h32v32H0z"></path><path d="M17.79 18.183h4.29l1.31-1.51 1.44 1.51h1.52l-2.2-2.1 2.21-2.27h-1.52l-1.44 1.51-1.26-1.5H17.8v-.85h4.68l.92 1.18 1.09-1.18h4.05l-3.04 3.11 3.04 2.94h-4.05l-1.1-1.17-.92 1.17h-4.68v-.84zm3.67-.84h-2.53v-.84h2.36v-.83h-2.36v-.84h2.7l1.01 1.26-1.18 1.25zm-14.5 1.68h-3.5l2.97-6.05h2.8l.35.67v-.67h3.5l.7 1.68.7-1.68h3.31v6.05h-2.63v-.84l-.34.84h-2.1l-.35-.84v.84H8.53l-.35-1h-.87l-.35 1zm9.96-.84v-4.37h-1.74l-1.4 3.03-1.41-3.03h-1.74v4.04l-2.1-4.04h-1.4l-2.1 4.37h1.23l.35-1h2.27l.35 1h2.43v-3.36l1.6 3.36h1.05l1.57-3.36v3.36h1.04zm-8.39-1.85-.7-1.85-.87 1.85h1.57z" fill="#FFF"></path></g></svg>`,
  height: 32,
  width: 32,
});

addIcon(IconKey.Discover, {
  body: `<svg width="48" height="28" viewBox="0 0 48 28" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<rect width="48" height="28" rx="4" fill="white"/>
<g clip-path="url(#clip0_15707_6022)">
<mask id="mask0_15707_6022" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="1" y="10" width="45" height="13">
<path d="M1.94922 22.251H45.9999V10.9644H1.94922V22.251Z" fill="white"/>
</mask>
<g mask="url(#mask0_15707_6022)">
<path d="M5.58011 16.0937C5.15339 16.4578 4.59907 16.6167 3.72154 16.6167H3.35705V12.2659H3.72154C4.59907 12.2659 5.13157 12.4143 5.58011 12.798C6.0498 13.1932 6.33237 13.8056 6.33237 14.4359C6.33237 15.0675 6.0498 15.6986 5.58011 16.0937ZM3.99359 11.1512H2V17.7305H3.98307C5.03759 17.7305 5.799 17.4954 6.4674 16.9709C7.26163 16.3498 7.73131 15.4137 7.73131 14.4454C7.73131 12.5038 6.19612 11.1512 3.99359 11.1512Z" fill="#201D1C"/>
<path d="M8.35547 17.7305H9.71359V11.1512H8.35547V17.7305Z" fill="#201D1C"/>
<path d="M13.0359 13.6758C12.2209 13.3908 11.9816 13.2029 11.9816 12.8474C11.9816 12.4331 12.408 12.1182 12.9934 12.1182C13.4003 12.1182 13.7347 12.2761 14.0886 12.651L14.7991 11.7717C14.2152 11.2891 13.5165 11.0423 12.7531 11.0423C11.5213 11.0423 10.5816 11.8507 10.5816 12.9275C10.5816 13.8339 11.0191 14.298 12.2947 14.7318C12.8263 14.909 13.0969 15.0271 13.2335 15.1065C13.5048 15.2739 13.6407 15.5109 13.6407 15.7872C13.6407 16.3204 13.1922 16.7153 12.5862 16.7153C11.9384 16.7153 11.4167 16.4092 11.104 15.8378L10.2266 16.6361C10.8523 17.504 11.6038 17.8887 12.6371 17.8887C14.0486 17.8887 15.0386 17.0019 15.0386 15.7282C15.0386 14.6829 14.5809 14.2097 13.0359 13.6758Z" fill="#201D1C"/>
<path d="M15.4668 14.4454C15.4668 16.3793 17.0738 17.8789 19.1419 17.8789C19.7265 17.8789 20.2272 17.7703 20.8446 17.4954V15.9848C20.3017 16.4983 19.8208 16.7054 19.205 16.7054C17.8373 16.7054 16.8664 15.7683 16.8664 14.436C16.8664 13.1728 17.8679 12.1763 19.1419 12.1763C19.7896 12.1763 20.28 12.3948 20.8446 12.9166V11.4067C20.2485 11.1209 19.7583 11.0025 19.1739 11.0025C17.1164 11.0025 15.4668 12.5324 15.4668 14.4454Z" fill="#201D1C"/>
<path d="M31.6119 15.5707L29.7552 11.1513H28.2715L31.2261 17.8994H31.957L34.965 11.1513H33.4928L31.6119 15.5707Z" fill="#201D1C"/>
<path d="M35.5801 17.7305H39.4316V16.6167H36.9371V14.8407H39.3396V13.7262H36.9371V12.2659H39.4316V11.1512H35.5801V17.7305Z" fill="#201D1C"/>
<path d="M42.0833 14.1802H41.6866V12.1875H42.1048C42.9506 12.1875 43.4104 12.5224 43.4104 13.1627C43.4104 13.8241 42.9506 14.1802 42.0833 14.1802ZM44.808 13.0936C44.808 11.8618 43.9101 11.1512 42.3439 11.1512H40.3301V17.7305H41.6866V15.0875H41.8636L43.7435 17.7305H45.4137L43.2219 14.9587C44.2447 14.7622 44.808 14.101 44.808 13.0936Z" fill="#201D1C"/>
<path d="M45.4189 11.4778H45.3942V11.3267H45.4204C45.4927 11.3267 45.5301 11.3512 45.5301 11.401C45.5301 11.4522 45.4922 11.4778 45.4189 11.4778ZM45.6762 11.3989C45.6762 11.2836 45.5922 11.2205 45.4445 11.2205H45.248V11.7988H45.3942V11.5745L45.5655 11.7988H45.7438L45.5425 11.5603C45.6286 11.5384 45.6762 11.4792 45.6762 11.3989Z" fill="#201D1C"/>
<path d="M45.4706 11.9226C45.2367 11.9226 45.0454 11.7387 45.0454 11.5092C45.0454 11.2789 45.2342 11.0951 45.4706 11.0951C45.7032 11.0951 45.893 11.2832 45.893 11.5092C45.893 11.7368 45.7032 11.9226 45.4706 11.9226ZM45.4726 11.0044C45.1753 11.0044 44.9395 11.2285 44.9395 11.5085C44.9395 11.7883 45.1781 12.0127 45.4726 12.0127C45.7621 12.0127 45.9992 11.7859 45.9992 11.5085C45.9992 11.2325 45.7621 11.0044 45.4726 11.0044Z" fill="#201D1C"/>
</g>
<mask id="mask1_15707_6022" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="1" y="10" width="46" height="13">
<path d="M1.94922 22.251V10.9644H46.0106V22.251H1.94922Z" fill="white"/>
</mask>
<g mask="url(#mask1_15707_6022)">
<mask id="mask2_15707_6022" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="21" y="10" width="8" height="8">
<path d="M21.2871 14.4658C21.2871 14.4658 21.2871 14.4658 21.2871 14.4636C21.2871 12.5318 22.9455 10.9646 24.9901 10.9646C27.0368 10.9646 28.6952 12.5318 28.6952 14.4636C28.6952 14.4658 28.6952 14.4658 28.6952 14.4658C28.6952 16.3977 27.0368 17.9648 24.9901 17.9648C22.9455 17.9648 21.2871 16.3977 21.2871 14.4658Z" fill="white"/>
</mask>
<g mask="url(#mask2_15707_6022)">
<rect x="21.1973" y="10.8916" width="7.58544" height="7.14425" fill="url(#pattern0_15707_6022)"/>
</g>
</g>
</g>
<defs>
<pattern id="pattern0_15707_6022" patternContentUnits="objectBoundingBox" width="1" height="1">
<use xlink:href="#image0_15707_6022" transform="scale(0.00793651)"/>
</pattern>
<clipPath id="clip0_15707_6022">
<rect width="44" height="7" fill="white" transform="translate(2 11)"/>
</clipPath>
<image id="image0_15707_6022" width="126" height="126" xlink:href="data:image/png;base64,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"/>
</defs>
</svg>`,
  height: 32,
  width: 32,
});

addIcon(IconKey.Diners, {
  body: `<svg width="48" height="28" viewBox="0 0 48 28" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_15796_1133)">
<rect width="48" height="28" rx="4" fill="white"/>
<path d="M25.6713 23.9999C30.8059 24.026 35.4924 19.5419 35.4924 14.0868C35.4924 8.12141 30.8059 3.99802 25.6713 4H21.2523C16.0562 3.99802 11.7793 8.12264 11.7793 14.0868C11.7793 19.5431 16.0562 24.0261 21.2523 23.9999H25.6713Z" fill="#0079BE"/>
<path d="M21.2744 4.82812C16.5264 4.82971 12.6789 8.92691 12.6777 13.9837C12.6789 19.0395 16.5263 23.1364 21.2744 23.1379C26.0236 23.1364 29.8719 19.0395 29.8726 13.9837C29.8719 8.92691 26.0236 4.82971 21.2744 4.82812ZM15.8252 13.9837C15.8297 11.5126 17.2793 9.40552 19.3235 8.56818V19.3979C17.2793 18.561 15.8297 16.455 15.8252 13.9837ZM23.2247 19.4003V8.56783C25.2696 9.40314 26.7214 11.5115 26.7251 13.9837C26.7214 16.4566 25.2696 18.5634 23.2247 19.4003Z" fill="white"/>
<path d="M25.6713 23.9999C30.8059 24.026 35.4924 19.5419 35.4924 14.0868C35.4924 8.12141 30.8059 3.99802 25.6713 4H21.2523C16.0562 3.99802 11.7793 8.12264 11.7793 14.0868C11.7793 19.5431 16.0562 24.0261 21.2523 23.9999H25.6713Z" fill="#0079BE"/>
<path d="M21.2744 4.82812C16.5264 4.82971 12.6789 8.92691 12.6777 13.9837C12.6789 19.0395 16.5263 23.1364 21.2744 23.1379C26.0236 23.1364 29.8719 19.0395 29.8726 13.9837C29.8719 8.92691 26.0236 4.82971 21.2744 4.82812ZM15.8252 13.9837C15.8297 11.5126 17.2793 9.40552 19.3235 8.56818V19.3979C17.2793 18.561 15.8297 16.455 15.8252 13.9837ZM23.2247 19.4003V8.56783C25.2696 9.40314 26.7214 11.5115 26.7251 13.9837C26.7214 16.4566 25.2696 18.5634 23.2247 19.4003Z" fill="white"/>
</g>
<defs>
<clipPath id="clip0_15796_1133">
<rect width="48" height="28" rx="4" fill="white"/>
</clipPath>
</defs>
</svg>`,
  height: 18,
  width: 18,
});

addIcon(IconKey.Union, {
  body: `<svg width="48" height="28" viewBox="0 0 48 28" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="48" height="28" rx="4" fill="white"/>
<g clip-path="url(#clip0_15708_47129)">
<path d="M15.4733 5H22.4491C23.4228 5 24.0285 5.77102 23.8013 6.72009L20.5536 20.2654C20.3244 21.2112 19.3494 21.9829 18.375 21.9829H11.3999C10.4275 21.9829 9.82049 21.2112 10.0477 20.2654L13.2967 6.72009C13.5239 5.77102 14.4983 5 15.4733 5Z" fill="#E21836"/>
<path d="M21.8693 5H29.8913C30.8649 5 30.4259 5.77102 30.1968 6.72009L26.9496 20.2654C26.7218 21.2112 26.7929 21.9829 25.8172 21.9829H17.7953C16.8196 21.9829 16.2159 21.2112 16.4451 20.2654L19.6921 6.72009C19.9227 5.77102 20.8951 5 21.8693 5Z" fill="#00447C"/>
<path d="M29.5721 5H36.5479C37.523 5 38.1287 5.77102 37.8996 6.72009L34.6524 20.2654C34.4232 21.2112 33.4476 21.9829 32.4726 21.9829H25.5001C24.5245 21.9829 23.9193 21.2112 24.1479 20.2654L27.3956 6.72009C27.6227 5.77102 28.5965 5 29.5721 5Z" fill="#007B84"/>
<path d="M17.2956 9.3413C16.5783 9.3484 16.3664 9.3413 16.2987 9.32577C16.2727 9.44585 15.7887 11.615 15.7873 11.6169C15.6831 12.0557 15.6072 12.3686 15.3496 12.5706C15.2034 12.688 15.0327 12.7446 14.8348 12.7446C14.5167 12.7446 14.3313 12.5912 14.3002 12.3002L14.2943 12.2002C14.2943 12.2002 14.3912 11.6124 14.3912 11.6091C14.3912 11.6091 14.8992 9.63233 14.9902 9.37103C14.995 9.35616 14.9963 9.3484 14.9975 9.3413C14.0086 9.34972 13.8333 9.3413 13.8213 9.32577C13.8146 9.34708 13.7901 9.46964 13.7901 9.46964L13.2714 11.6976L13.2268 11.8866L13.1406 12.5047C13.1406 12.688 13.1777 12.8376 13.2515 12.9642C13.4878 13.3654 14.162 13.4255 14.5434 13.4255C15.0347 13.4255 15.4957 13.3241 15.8072 13.1389C16.3479 12.8286 16.4893 12.3434 16.6155 11.9124L16.674 11.6912C16.674 11.6912 17.1973 9.63811 17.2863 9.37103C17.2897 9.35616 17.291 9.3484 17.2956 9.3413ZM19.0763 10.9975C18.9501 10.9975 18.7196 11.0273 18.5125 11.1259C18.4373 11.1634 18.3662 11.2066 18.2913 11.2498L18.3589 11.0124L18.3219 10.9724C17.8827 11.0588 17.7844 11.0704 17.3787 11.1259L17.3447 11.1478C17.2976 11.5272 17.2558 11.8125 17.0812 12.5582C17.0147 12.8331 16.9457 13.1106 16.8765 13.3848L16.8952 13.4196C17.3109 13.3983 17.437 13.3983 17.7983 13.4041L17.8276 13.3732C17.8735 13.1448 17.8795 13.0912 17.9811 12.6286C18.0289 12.4093 18.1285 11.9273 18.1777 11.7557C18.268 11.715 18.3571 11.6751 18.4421 11.6751C18.6446 11.6751 18.6199 11.8467 18.6121 11.9151C18.6034 12.0299 18.5296 12.4048 18.454 12.7267L18.4035 12.9345C18.3683 13.0879 18.3297 13.2371 18.2945 13.3892L18.3098 13.4196C18.7196 13.3983 18.8445 13.3983 19.1945 13.4041L19.2356 13.3732C19.2989 13.0164 19.3174 12.921 19.4296 12.4015L19.4861 12.1628C19.5957 11.6957 19.6508 11.4589 19.5678 11.2659C19.4801 11.0497 19.2696 10.9975 19.0763 10.9975ZM21.0655 11.4866C20.8477 11.5272 20.7088 11.5543 20.5707 11.5718C20.4338 11.5931 20.3004 11.6125 20.0899 11.6409L20.0732 11.6556L20.0579 11.6673C20.036 11.8196 20.0207 11.9512 19.9916 12.106C19.9669 12.266 19.929 12.4479 19.8673 12.7092C19.8195 12.9092 19.7949 12.9789 19.7677 13.0493C19.7411 13.1196 19.7119 13.188 19.6582 13.3847L19.6707 13.4029L19.6813 13.4196C19.878 13.4105 20.0067 13.4041 20.139 13.4029C20.2711 13.3983 20.408 13.4029 20.6198 13.4041L20.6384 13.3894L20.6583 13.3732C20.6889 13.1958 20.6935 13.148 20.7122 13.0615C20.7307 12.9687 20.7627 12.8402 20.8411 12.4969C20.8781 12.3357 20.9194 12.175 20.9579 12.0105C20.9978 11.8467 21.0396 11.6853 21.0794 11.5241L21.0735 11.5046L21.0655 11.4866ZM21.0701 10.8272C20.8722 10.7137 20.5249 10.7497 20.2911 10.9065C20.058 11.0601 20.0314 11.2781 20.2287 11.3931C20.4232 11.5034 20.7719 11.4705 21.0037 11.3125C21.2363 11.1556 21.2653 10.9395 21.0701 10.8272ZM22.2671 13.4551C22.6675 13.4551 23.0779 13.348 23.3869 13.0298C23.6246 12.7718 23.7336 12.388 23.7713 12.2299C23.8942 11.706 23.7985 11.4614 23.6783 11.3124C23.4957 11.0853 23.173 11.0124 22.8382 11.0124C22.6369 11.0124 22.1574 11.0318 21.7828 11.3672C21.5138 11.6092 21.3895 11.9376 21.3146 12.2524C21.2389 12.5731 21.1518 13.1506 21.6985 13.3655C21.8671 13.4358 22.1103 13.4551 22.2671 13.4551ZM22.2358 12.2763C22.3281 11.8796 22.4371 11.5466 22.7153 11.5466C22.9332 11.5466 22.949 11.7944 22.8521 12.1924C22.8348 12.2808 22.7552 12.6093 22.6476 12.7492C22.5724 12.8524 22.4835 12.915 22.3852 12.915C22.356 12.915 22.182 12.915 22.1793 12.6641C22.178 12.5403 22.204 12.4137 22.2358 12.2763ZM24.7723 13.4041L24.8035 13.3732C24.8479 13.1448 24.8552 13.0911 24.9535 12.6286C25.0027 12.4093 25.1043 11.9273 25.1521 11.7557C25.2426 11.7149 25.3301 11.675 25.4179 11.675C25.619 11.675 25.5945 11.8466 25.5865 11.9149C25.5792 12.0299 25.5054 12.4047 25.4284 12.7266L25.3806 12.9344C25.3441 13.088 25.3043 13.237 25.2691 13.3893L25.2844 13.4197C25.6955 13.3983 25.8157 13.3983 26.1677 13.4041L26.2102 13.3732C26.2719 13.0163 26.2886 12.9208 26.4042 12.4015L26.4593 12.1627C26.5695 11.6956 26.6252 11.4589 26.5436 11.266C26.4533 11.0498 26.2415 10.9976 26.0509 10.9976C25.9245 10.9976 25.6928 11.0272 25.4869 11.1259C25.4133 11.1634 25.3395 11.2065 25.267 11.2498L25.3301 11.0125L25.2963 10.9723C24.8573 11.0589 24.757 11.0704 24.3518 11.1259L24.3207 11.1479C24.2715 11.5273 24.2316 11.8124 24.0569 12.5583C23.9905 12.8331 23.9214 13.1106 23.8524 13.3848L23.8709 13.4197C24.2873 13.3983 24.4116 13.3983 24.7723 13.4041ZM27.7929 13.4196C27.8188 13.297 27.9723 12.5706 27.9737 12.5706C27.9737 12.5706 28.1044 12.0376 28.1124 12.0183C28.1124 12.0183 28.1535 11.9628 28.1947 11.9408H28.2552C28.8263 11.9408 29.4713 11.9408 29.9768 11.5796C30.3207 11.3318 30.5559 10.966 30.6608 10.5213C30.688 10.4123 30.7081 10.2826 30.7081 10.153C30.7081 9.98269 30.6729 9.81422 30.5712 9.68258C30.3134 9.33225 29.8001 9.32581 29.2076 9.32316C29.2057 9.32316 28.9154 9.32581 28.9154 9.32581C28.1569 9.33489 27.8528 9.33225 27.7278 9.31738C27.7173 9.37106 27.6974 9.46653 27.6974 9.46653C27.6974 9.46653 27.4256 10.6898 27.4256 10.6918C27.4256 10.6918 26.7755 13.2926 26.7449 13.4151C27.4071 13.4073 27.6787 13.4073 27.7929 13.4196ZM28.2964 11.2466C28.2964 11.2466 28.5852 10.0258 28.5839 10.0304L28.5932 9.96783L28.5973 9.92009L28.7128 9.93165C28.7128 9.93165 29.3086 9.98137 29.3225 9.98269C29.5576 10.0711 29.6546 10.2988 29.5869 10.5961C29.5252 10.8678 29.3437 11.0963 29.1106 11.2066C28.9187 11.3001 28.6835 11.3079 28.4412 11.3079H28.2845L28.2964 11.2466ZM30.095 12.2989C30.0187 12.6151 29.9309 13.1925 30.4748 13.3983C30.6483 13.47 30.8037 13.4913 30.9616 13.4835C31.1284 13.4748 31.283 13.3935 31.4262 13.2766C31.4132 13.3246 31.4003 13.3727 31.3874 13.4209L31.4121 13.4518C31.8033 13.4358 31.9247 13.4358 32.3486 13.4389L32.387 13.4105C32.4489 13.0571 32.5072 12.7138 32.6681 12.0376C32.7464 11.7137 32.8246 11.393 32.9051 11.0704L32.8925 11.0349C32.4548 11.1137 32.3379 11.1305 31.9169 11.1885L31.8849 11.2138C31.8806 11.2466 31.8762 11.2782 31.8721 11.3097C31.8067 11.207 31.7118 11.1193 31.5654 11.0646C31.3782 10.9931 30.9385 11.0853 30.5605 11.4196C30.295 11.6582 30.1674 11.9853 30.095 12.2989ZM31.0142 12.3183C31.1079 11.9286 31.2155 11.5989 31.4943 11.5989C31.6707 11.5989 31.7635 11.757 31.7446 12.0266C31.7297 12.0938 31.7135 12.1647 31.6943 12.2448C31.6664 12.3605 31.6361 12.4753 31.6067 12.5903C31.5768 12.6689 31.542 12.7431 31.5037 12.7925C31.4319 12.8912 31.2612 12.9525 31.163 12.9525C31.1351 12.9525 30.963 12.9525 30.9571 12.7061C30.9557 12.5834 30.9817 12.457 31.0142 12.3183ZM35.8142 11.0317L35.7804 10.9942C35.3473 11.0795 35.2689 11.093 34.871 11.1452L34.8418 11.1736C34.8404 11.1782 34.8392 11.1853 34.8372 11.1918L34.8358 11.1853C34.5396 11.8492 34.5483 11.706 34.3072 12.2286C34.3059 12.2048 34.3059 12.1899 34.3045 12.1646L34.2441 11.0317L34.2062 10.9942C33.7526 11.0795 33.7419 11.093 33.3229 11.1452L33.2903 11.1736C33.2857 11.1872 33.2857 11.202 33.283 11.2182L33.2857 11.224C33.3381 11.484 33.3255 11.426 33.378 11.8363C33.4025 12.0376 33.4351 12.2401 33.4596 12.439C33.5009 12.7718 33.5241 12.9357 33.5746 13.4436C33.2916 13.8971 33.2246 14.0687 32.9523 14.4668L32.9541 14.4708L32.7623 14.7654C32.7404 14.7965 32.7205 14.8178 32.6926 14.8269C32.662 14.8416 32.6222 14.8442 32.567 14.8442H32.4607L32.3027 15.3546L32.8446 15.3637C33.1628 15.3623 33.3627 15.2178 33.4703 15.0236L33.8111 14.4564H33.8056L33.8415 14.4164C34.0707 13.9371 35.8142 11.0317 35.8142 11.0317ZM30.095 17.7348H29.8651L30.7159 15.001H30.9982L31.0878 14.7194L31.0965 15.0326C31.0859 15.2262 31.2427 15.3978 31.6545 15.3694H32.1307L32.2947 14.843H32.1154C32.0124 14.843 31.9646 14.8177 31.9706 14.7635L31.9619 14.4449H31.08V14.4466C30.7948 14.4523 29.9433 14.4731 29.7709 14.5177C29.5623 14.5699 29.3424 14.7235 29.3424 14.7235L29.4288 14.4416H28.6038L28.4319 15.001L27.5697 17.7766H27.4024L27.2383 18.2992H28.8815L28.8264 18.4734H29.6361L29.6898 18.2992H29.917L30.095 17.7348ZM29.4208 15.5565C29.2887 15.592 29.0428 15.6997 29.0428 15.6997L29.2615 15.001H29.917L29.7588 15.5101C29.7588 15.5101 29.5563 15.5217 29.4208 15.5565ZM29.4334 16.5546C29.4334 16.5546 29.2275 16.5797 29.092 16.6095C28.9585 16.6488 28.7082 16.7727 28.7082 16.7727L28.934 16.0456H29.5929L29.4334 16.5546ZM29.0661 17.7412H28.4086L28.5992 17.1276H29.2547L29.0661 17.7412ZM30.6496 16.0456H31.5973L31.4611 16.474H30.5008L30.3566 16.9425H31.1969L30.5606 17.8127C30.5161 17.8767 30.4761 17.8993 30.4317 17.9173C30.3872 17.9393 30.3287 17.965 30.261 17.965H30.0279L29.8678 18.4781H30.4775C30.7944 18.4781 30.9816 18.338 31.1199 18.1542L31.5562 17.5741L31.6498 18.1631C31.6697 18.2734 31.7513 18.338 31.8066 18.3631C31.8676 18.3928 31.9307 18.4439 32.0198 18.4515C32.1154 18.4554 32.1844 18.4586 32.2303 18.4586H32.5299L32.7098 17.8844H32.5916C32.5238 17.8844 32.407 17.8734 32.3871 17.8527C32.3672 17.8276 32.3672 17.789 32.3565 17.7302L32.2614 17.1398H31.8722L32.0429 16.9425H33.0014L33.1488 16.474H32.2614L32.3997 16.0456H33.2843L33.4484 15.5172H30.8109L30.6496 16.0456ZM22.6449 17.8605L22.8661 17.1457H23.7752L23.9414 16.614H23.0314L23.1703 16.174H24.0595L24.2243 15.6591H21.9993L21.8379 16.174H22.3434L22.2086 16.614H21.7017L21.5338 17.1548H22.0391L21.7442 18.1005C21.7045 18.2257 21.763 18.2735 21.8 18.3316C21.8379 18.3882 21.8764 18.4257 21.9627 18.447C22.0518 18.4664 22.1129 18.4779 22.1958 18.4779H23.2208L23.4034 17.8889L22.9491 17.9496C22.8613 17.9496 22.6184 17.9393 22.6449 17.8605ZM22.7492 14.4384L22.5188 14.8429C22.4695 14.9313 22.4251 14.9861 22.3851 15.0114C22.3499 15.0327 22.2802 15.0416 22.1792 15.0416H22.059L21.8983 15.5591H22.2976C22.4895 15.5591 22.637 15.4907 22.7073 15.4565C22.783 15.4172 22.8029 15.4396 22.8614 15.3848L22.9962 15.2713H24.2429L24.4083 14.7325H23.4958L23.6551 14.4384H22.7492ZM24.5896 17.8709C24.5684 17.8412 24.5837 17.7888 24.6162 17.6798L24.9569 16.5842H26.169C26.3457 16.5818 26.4732 16.5798 26.5562 16.574C26.6453 16.5649 26.7422 16.534 26.8478 16.4785C26.9568 16.4204 27.0125 16.3591 27.0596 16.2887C27.1122 16.2185 27.1965 16.0649 27.269 15.8281L27.6973 14.4416L26.4394 14.4487C26.4394 14.4487 26.0521 14.5042 25.8815 14.5655C25.7094 14.6339 25.4636 14.8248 25.4636 14.8248L25.5772 14.4448H24.8001L23.7123 17.9495C23.6737 18.0856 23.6478 18.1844 23.6419 18.2437C23.6399 18.3076 23.7249 18.3709 23.78 18.4186C23.8451 18.4664 23.9413 18.4586 24.0336 18.4664C24.1307 18.4735 24.2688 18.4779 24.4594 18.4779H25.0565L25.2398 17.8767L24.7053 17.9258C24.6481 17.9258 24.6068 17.896 24.5896 17.8709ZM25.1767 15.8443H26.4499L26.369 16.0907C26.3576 16.0965 26.3304 16.0785 26.2008 16.0933H25.0984L25.1767 15.8443ZM25.4318 15.0178H26.7157L26.6234 15.3146C26.6234 15.3146 26.0182 15.3088 25.9213 15.3261C25.4949 15.3978 25.2458 15.6191 25.2458 15.6191L25.4318 15.0178ZM26.3975 16.9159C26.3869 16.9528 26.3703 16.9752 26.347 16.9921C26.3211 17.0083 26.2793 17.0141 26.2169 17.0141H26.0355L26.0462 16.7139H25.2916L25.261 18.1812C25.2598 18.287 25.2704 18.3483 25.3501 18.3974C25.4299 18.4586 25.6755 18.4664 26.0062 18.4664H26.4791L26.6498 17.9172L26.2382 17.9392L26.1013 17.9469C26.0826 17.9392 26.0647 17.9321 26.0448 17.9128C26.0275 17.8961 25.9983 17.9063 26.003 17.8004L26.0062 17.4243L26.438 17.407C26.6711 17.407 26.7707 17.3333 26.8557 17.2631C26.9368 17.1959 26.9633 17.1186 26.9939 17.0141L27.0664 16.6811H26.4731L26.3975 16.9159Z" fill="#FEFEFE"/>
</g>
<defs>
<clipPath id="clip0_15708_47129">
<rect width="28" height="17" fill="white" transform="translate(10 5)"/>
</clipPath>
</defs>
</svg>`,
  height: 18,
  width: 18,
});

addIcon(IconKey.JCB, {
  body: `<svg width="48" height="28" viewBox="0 0 48 28" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="48" height="28" rx="4" fill="white"/>
<path d="M29.278 14.3869C29.7917 14.398 30.3083 14.3642 30.8199 14.4045C31.3381 14.5011 31.463 15.2855 31.0026 15.5424C30.6886 15.7116 30.3154 15.6054 29.9749 15.6353H29.278V14.3869ZM31.117 12.9739C31.2311 13.3767 30.8428 13.7384 30.4548 13.6829H29.278C29.2863 13.303 29.2619 12.8907 29.29 12.5308C29.7614 12.5441 30.2372 12.5037 30.7058 12.5519C30.9072 12.6025 31.0757 12.7681 31.117 12.9739ZM33.9489 7.00003C33.9708 7.76933 33.9521 8.57929 33.9584 9.3642C33.9569 12.5553 33.9614 15.7464 33.9559 18.9377C33.9352 20.1337 32.8753 21.1729 31.6876 21.1967C30.4988 21.2016 29.3098 21.1975 28.1208 21.1988V16.3745C29.4162 16.3678 30.7124 16.388 32.0073 16.3644C32.608 16.3266 33.266 15.9303 33.294 15.2692C33.3647 14.6053 32.7388 14.146 32.1445 14.0735C31.916 14.0676 31.9227 14.0069 32.1445 13.9804C32.7113 13.8579 33.1564 13.2713 32.9896 12.6837C32.8475 12.0658 32.1645 11.8266 31.5964 11.8278C30.438 11.8199 29.2795 11.8267 28.121 11.8244C28.1285 10.9238 28.1054 10.0222 28.1335 9.12217C28.2253 7.9478 29.3119 6.97932 30.4828 7.00034C31.6383 7.00016 32.7937 7.00016 33.9489 7.00012V7.00003Z" fill="url(#paint0_linear_7237_131923)"/>
<path d="M15.0308 9.24402C15.0604 8.04995 16.1248 7.01928 17.311 7.00183C18.4954 6.99818 19.68 7.0013 20.8645 7.00025C20.8612 10.9953 20.8709 14.9909 20.8596 18.9857C20.814 20.1653 19.7611 21.1763 18.5879 21.1972C17.4012 21.2015 16.2144 21.1978 15.0277 21.199V16.212C16.1804 16.4843 17.3892 16.6002 18.5651 16.4196C19.2681 16.3065 20.0371 15.9613 20.2751 15.2321C20.4503 14.6083 20.3517 13.9517 20.3777 13.3115V11.8246H18.3426C18.3335 12.808 18.3613 13.7931 18.3279 14.7753C18.273 15.379 17.6752 15.7626 17.1058 15.7421C16.3997 15.7496 15.0003 15.2303 15.0003 15.2303C14.9968 13.3879 15.0208 11.0805 15.0308 9.24419V9.24402Z" fill="url(#paint1_linear_7237_131923)"/>
<path d="M21.624 12.4246C21.5169 12.4473 21.6025 12.0597 21.5751 11.9127C21.5824 10.983 21.5598 10.0522 21.5875 9.1232C21.679 7.94386 22.774 6.97297 23.9497 7.00057H27.4121C27.4089 10.9957 27.4185 14.9912 27.4072 18.986C27.3616 20.1656 26.3087 21.1766 25.1354 21.1975C23.9487 21.202 22.7618 21.1982 21.575 21.1994V15.7354C22.3856 16.4005 23.4872 16.504 24.497 16.5058C25.2582 16.5055 26.015 16.3882 26.7543 16.2126V15.2116C25.9212 15.6268 24.9418 15.8906 24.0183 15.652C23.374 15.4916 22.9064 14.869 22.9168 14.2042C22.8422 13.5128 23.2474 12.7828 23.9269 12.5773C24.7705 12.3132 25.6899 12.5151 26.4806 12.8585C26.65 12.9472 26.822 13.0572 26.7543 12.7741V11.9873C25.4318 11.6726 24.0243 11.5568 22.6957 11.8991C22.3111 12.0076 21.9364 12.1721 21.624 12.4247V12.4246Z" fill="url(#paint2_linear_7237_131923)"/>
<defs>
<linearGradient id="paint0_linear_7237_131923" x1="28.0809" y1="13.7958" x2="33.9215" y2="13.7958" gradientUnits="userSpaceOnUse">
<stop stop-color="#58B03A"/>
<stop offset="1" stop-color="#55B330"/>
</linearGradient>
<linearGradient id="paint1_linear_7237_131923" x1="14.9035" y1="14.2356" x2="20.7688" y2="14.2356" gradientUnits="userSpaceOnUse">
<stop stop-color="#0F6EB6"/>
<stop offset="1" stop-color="#006DBA"/>
</linearGradient>
<linearGradient id="paint2_linear_7237_131923" x1="21.5665" y1="13.9339" x2="27.411" y2="13.9339" gradientUnits="userSpaceOnUse">
<stop stop-color="#DE0D3D"/>
<stop offset="1" stop-color="#E30138"/>
</linearGradient>
</defs>
</svg>`,
  height: 18,
  width: 18,
});

addIcon(IconKey.LoyalPoint, {
  body: `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M8.00125 14.7203C11.7126 14.7203 14.7213 11.7116 14.7213 8.00027C14.7213 4.28892 11.7126 1.28027 8.00125 1.28027C4.2899 1.28027 1.28125 4.28892 1.28125 8.00027C1.28125 11.7116 4.2899 14.7203 8.00125 14.7203ZM8.00125 13.5786C4.92045 13.5786 2.42297 11.0811 2.42297 8.00027C2.42297 4.91948 4.92045 2.42199 8.00125 2.42199C11.082 2.42199 13.5795 4.91948 13.5795 8.00027C13.5795 11.0811 11.082 13.5786 8.00125 13.5786ZM4.8202 5.92631C4.76236 5.81398 4.84391 5.68027 4.97025 5.68027H10.2939V7.39771H5.88659C5.69696 7.39771 5.52323 7.29175 5.43642 7.12316L4.8202 5.92631ZM5.75436 7.86825C5.69621 7.75591 5.77775 7.62189 5.90425 7.62189H10.2938V9.33933H6.82384C6.63461 9.33933 6.46117 9.2338 6.37418 9.06574L5.75436 7.86825ZM7.14042 9.56289C7.01595 9.56289 6.9343 9.69301 6.98845 9.80508L7.56292 10.9942C7.64743 11.1692 7.82457 11.2803 8.01884 11.2803H10.2939V9.56289H7.14042Z" fill="#848281"/>
</svg>`,
});

addIcon(IconKey.LoyalPointDetail, {
  body: `<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M18.4016 9.99961C18.4016 14.6388 14.6408 18.3996 10.0016 18.3996C5.36237 18.3996 1.60156 14.6388 1.60156 9.99961C1.60156 5.36042 5.36237 1.59961 10.0016 1.59961C14.6408 1.59961 18.4016 5.36042 18.4016 9.99961ZM3.02871 9.99961C3.02871 13.8506 6.15056 16.9725 10.0016 16.9725C13.8526 16.9725 16.9744 13.8506 16.9744 9.99961C16.9744 6.14861 13.8526 3.02676 10.0016 3.02676C6.15056 3.02676 3.02871 6.14861 3.02871 9.99961Z" fill="#8E9193"/>
<path d="M5.96118 7.40715C5.88889 7.26675 5.99083 7.09961 6.14875 7.09961H12.8033V9.2464H7.29417C7.05714 9.2464 6.83997 9.11396 6.73146 8.90321L5.96118 7.40715Z" fill="#8E9193"/>
<path d="M7.12888 9.83458C7.0562 9.69415 7.15812 9.52663 7.31625 9.52663H12.8032V11.6734H8.46574C8.2292 11.6734 8.01239 11.5415 7.90366 11.3314L7.12888 9.83458Z" fill="#8E9193"/>
<path d="M8.67149 12.2556C8.60382 12.1155 8.70588 11.9529 8.86146 11.9529H12.8033V14.0997H9.95948C9.71665 14.0997 9.49522 13.9607 9.38959 13.7421L8.67149 12.2556Z" fill="#8E9193"/>
</svg>`,
  height: 18,
  width: 18,
});

addIcon(IconKey.LoyalPointActive, {
  body: `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M8.00125 14.7203C11.7126 14.7203 14.7213 11.7116 14.7213 8.00027C14.7213 4.28892 11.7126 1.28027 8.00125 1.28027C4.2899 1.28027 1.28125 4.28892 1.28125 8.00027C1.28125 11.7116 4.2899 14.7203 8.00125 14.7203ZM8.00125 13.5786C4.92045 13.5786 2.42297 11.0811 2.42297 8.00027C2.42297 4.91948 4.92045 2.42199 8.00125 2.42199C11.082 2.42199 13.5795 4.91948 13.5795 8.00027C13.5795 11.0811 11.082 13.5786 8.00125 13.5786ZM4.8202 5.92631C4.76236 5.81398 4.84391 5.68027 4.97025 5.68027H10.2939V7.39771H5.88659C5.69696 7.39771 5.52323 7.29175 5.43642 7.12316L4.8202 5.92631ZM5.75436 7.86825C5.69621 7.75591 5.77775 7.62189 5.90425 7.62189H10.2938V9.33933H6.82384C6.63461 9.33933 6.46117 9.2338 6.37418 9.06574L5.75436 7.86825ZM7.14042 9.56289C7.01595 9.56289 6.9343 9.69301 6.98845 9.80508L7.56292 10.9942C7.64743 11.1692 7.82457 11.2803 8.01884 11.2803H10.2939V9.56289H7.14042Z" fill="#ED1C24"/>
</svg>`,
});

addIcon(IconKey.GooglePay, {
  body: `<svg width="36" height="16" viewBox="0 0 36 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_15627_17596)">
<path d="M25.518 4.54865C26.5076 4.54865 27.2724 4.83093 27.8573 5.39549C28.4421 5.95998 28.7119 6.7127 28.7119 7.65358V12.1698H27.4524V11.1349H27.4074C26.8676 11.9817 26.1028 12.4051 25.2031 12.4051C24.4383 12.4051 23.7635 12.1698 23.2237 11.6995C22.7288 11.229 22.4139 10.5704 22.4139 9.86476C22.4139 9.11201 22.6838 8.50042 23.2237 8.02998C23.7635 7.55954 24.5283 7.37137 25.428 7.37137C26.2377 7.37137 26.8676 7.51255 27.3624 7.84181V7.51255C27.3624 7.07343 27.2056 6.63439 26.8921 6.34829L26.8225 6.28939C26.4626 5.96005 26.0128 5.77189 25.518 5.77189C24.7532 5.77189 24.1684 6.1012 23.7635 6.75983L22.5939 6.00696C23.3136 5.01902 24.2583 4.54865 25.518 4.54865ZM19.175 1.34961C19.9821 1.34961 20.7493 1.64336 21.3626 2.19115L21.4693 2.29042C22.099 2.85498 22.4139 3.70182 22.4139 4.59564C22.4139 5.48946 22.099 6.28924 21.4693 6.90078C20.8395 7.51233 20.0747 7.84167 19.175 7.84167L17.0157 7.79461V12.1698H15.666V1.34961H19.175ZM25.6979 8.50034C25.158 8.50034 24.7082 8.64147 24.3483 8.92373C23.9884 9.15892 23.8085 9.48821 23.8085 9.91159C23.8085 10.288 23.9885 10.6173 24.2583 10.8055C24.5733 11.0407 24.9331 11.1818 25.2931 11.1818C25.7969 11.1818 26.3007 10.9769 26.6948 10.6053L26.7776 10.5232C27.2274 10.0998 27.4524 9.58233 27.4524 9.01777C27.0475 8.68851 26.4626 8.50027 25.6979 8.50027M19.265 2.66674H17.0157V6.43049H19.265C19.7598 6.43049 20.2547 6.24233 20.5696 5.86601C21.2893 5.16034 21.2893 3.98424 20.6146 3.27851L20.5696 3.23145C20.2097 2.85512 19.7598 2.6199 19.265 2.66696M35.9997 4.78387L31.5461 15.4629H30.1965L31.861 11.7464L28.9369 4.83093H30.3764L32.4907 10.1469H32.5357L34.6051 4.83093H35.9997V4.78387Z" fill="#5F6368"/>
<path d="M11.6623 6.85315C11.6623 6.42977 11.6173 6.00639 11.5723 5.58301H5.94922V7.98227H9.14316C9.00823 8.73492 8.60337 9.44058 7.97359 9.86404V11.4165H9.90795C11.0326 10.3345 11.6624 8.73492 11.6624 6.85323" fill="#4285F4"/>
<path d="M5.94894 12.9224C7.56838 12.9224 8.91795 12.3579 9.90767 11.417L7.9733 9.86455C7.43345 10.2409 6.75866 10.4761 5.94894 10.4761C4.41936 10.4761 3.06978 9.39411 2.61992 7.88867H0.640625V9.48816C1.67527 11.6051 3.69964 12.9224 5.94894 12.9224Z" fill="#34A853"/>
<path d="M2.62039 7.8884C2.35053 7.13568 2.35053 6.28891 2.62039 5.48913V3.88965H0.641092C-0.213697 5.63031 -0.213697 7.70024 0.641092 9.48788L2.62039 7.8884Z" fill="#FBBC04"/>
<path d="M5.94893 2.94885C6.80365 2.94885 7.61337 3.27819 8.24323 3.88973L9.95266 2.10208C8.87294 1.06708 7.43344 0.45554 5.99386 0.502525C3.74456 0.502525 1.67527 1.8198 0.685547 3.93679L2.66491 5.53628C3.06977 4.03083 4.41935 2.94885 5.94893 2.94885Z" fill="#EA4335"/>
</g>
<defs>
<clipPath id="clip0_15627_17596">
<rect width="36" height="15" fill="white" transform="translate(0 0.5)"/>
</clipPath>
</defs>
</svg>`,
  height: 16,
  width: 36,
});

addIcon(IconKey.LoyalOneAvatar, {
  body: `<svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_24636_94150)">
<path d="M32 64C14.3551 64 0 49.6449 0 32C0 14.3551 14.3551 0 32 0C49.6449 0 64 14.3551 64 32C64 49.6449 49.6449 64 32 64Z" fill="#FEE567"/>
<path d="M31.7977 60C47.349 60 60 47.4172 60 31.9508C60.0014 28.0375 59.1776 24.1673 57.5818 20.5903C55.986 17.0132 53.6537 13.8088 50.7354 11.1839C49.4896 11.0653 48.2289 11 46.9572 11C26.3571 11 9.49338 26.856 8 46.9653C13.0031 54.7925 21.7984 60 31.7977 60Z" fill="#FFCD36"/>
<path d="M32 55C19.3178 55 9 44.6822 9 32C9 19.3178 19.3178 9 32 9C44.6822 9 55 19.3178 55 32C55 44.6822 44.6822 55 32 55Z" fill="#FFAA00"/>
<path d="M47.7858 13.4616C48.2694 13.9452 49.8459 14.6917 50.5738 15.0253C50.6066 15.0401 50.6345 15.0641 50.654 15.0944C50.6735 15.1247 50.6839 15.1599 50.6839 15.1959C50.6839 15.232 50.6735 15.2672 50.654 15.2975C50.6345 15.3278 50.6066 15.3518 50.5738 15.3666C49.841 15.6992 48.2694 16.4457 47.7858 16.9294C47.3022 17.413 46.5547 18.9845 46.2221 19.7183C46.2073 19.7511 46.1833 19.779 46.153 19.7985C46.1227 19.818 46.0874 19.8284 46.0514 19.8284C46.0154 19.8284 45.9801 19.818 45.9499 19.7985C45.9196 19.779 45.8956 19.7511 45.8807 19.7183C45.5472 18.9855 44.8016 17.415 44.317 16.9303C43.8324 16.4457 42.2569 15.7002 41.529 15.3676C41.4962 15.3528 41.4684 15.3288 41.4489 15.2985C41.4293 15.2682 41.4189 15.233 41.4189 15.1969C41.4189 15.1609 41.4293 15.1256 41.4489 15.0954C41.4684 15.0651 41.4962 15.0411 41.529 15.0262C42.2618 14.6927 43.8334 13.9471 44.317 13.4625C44.8007 12.9779 45.5472 11.4074 45.8807 10.6745C45.8956 10.6417 45.9196 10.6139 45.9499 10.5944C45.9801 10.5748 46.0154 10.5645 46.0514 10.5645C46.0874 10.5645 46.1227 10.5748 46.153 10.5944C46.1833 10.6139 46.2073 10.6417 46.2221 10.6745C46.5547 11.4064 47.3012 12.9769 47.7858 13.4616Z" fill="white"/>
<circle cx="32" cy="32" r="30" stroke="white" stroke-width="4"/>
<path d="M37.5172 28.3951L33.4641 23.3287C32.7134 22.3904 31.2864 22.3904 30.5358 23.3287L26.4827 28.3951L20.3384 25.3229C18.9429 24.6252 17.3553 25.8377 17.6613 27.3677L20.1613 39.8677C20.3367 40.7441 21.1062 41.3749 21.9999 41.3749H41.9999C42.8937 41.3749 43.6632 40.7441 43.8386 39.8677L46.3386 27.3677C46.6446 25.8377 45.0571 24.6252 43.6614 25.3229L37.5172 28.3951Z" fill="#FEE567"/>
</g>
<defs>
<clipPath id="clip0_24636_94150">
<rect width="64" height="64" fill="white"/>
</clipPath>
</defs>
</svg>`,
  height: 64,
  width: 64,
});

addIcon(IconKey.CancelBookingsDashboard, {
  body: `<svg width="33" height="32" viewBox="0 0 33 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M22.2729 14.1174C22.2729 13.4754 21.7525 12.9551 21.1106 12.9551H9.13886C8.49692 12.9551 7.97656 13.4754 7.97656 14.1174C7.97656 14.7593 8.49692 15.2797 9.13886 15.2797H21.1106C21.7525 15.2797 22.2729 14.7593 22.2729 14.1174Z" fill="#FF3B30"/>
<path d="M9.13886 17.6074C8.49692 17.6074 7.97656 18.1278 7.97656 18.7697C7.97656 19.4117 8.49692 19.932 9.13886 19.932H16.4097C17.0516 19.932 17.572 19.4117 17.572 18.7697C17.572 18.1278 17.0516 17.6074 16.4097 17.6074H9.13886Z" fill="#FF3B30"/>
<path d="M15.478 28.7643H7.98318C6.7014 28.7643 5.65858 27.7214 5.65858 26.4397V5.98318C5.65858 4.7014 6.7014 3.65858 7.98318 3.65858H22.273C23.5548 3.65858 24.5976 4.7014 24.5976 5.98318V16.1938C24.5976 16.8358 25.118 17.3561 25.7599 17.3561C26.4019 17.3561 26.9222 16.8358 26.9222 16.1938V5.98318C26.9222 3.41962 24.8366 1.33398 22.273 1.33398H7.98318C5.41962 1.33398 3.33398 3.41962 3.33398 5.98318V26.4397C3.33398 29.0032 5.41962 31.0889 7.98318 31.0889H15.478C16.1199 31.0889 16.6403 30.5685 16.6403 29.9266C16.6403 29.2846 16.1199 28.7643 15.478 28.7643Z" fill="#FF3B30"/>
<path d="M21.1106 8.30664H9.13886C8.49692 8.30664 7.97656 8.827 7.97656 9.46894C7.97656 10.1109 8.49692 10.6312 9.13886 10.6312H21.1106C21.7525 10.6312 22.2729 10.1109 22.2729 9.46894C22.2729 8.827 21.7525 8.30664 21.1106 8.30664Z" fill="#FF3B30"/>
<path d="M27.9147 29.9261C27.5553 29.9261 27.1958 29.7937 26.9121 29.51L19.8186 22.4165C19.2701 21.8679 19.2701 20.96 19.8186 20.4114C20.3672 19.8629 21.2752 19.8629 21.8237 20.4114L28.9172 27.5049C29.4658 28.0534 29.4658 28.9614 28.9172 29.51C28.6335 29.7748 28.2741 29.9261 27.9147 29.9261Z" fill="#FF3B30"/>
<path d="M20.7479 29.9984C20.3885 29.9984 20.0291 29.866 19.7454 29.5822C19.1968 29.0337 19.1968 28.1257 19.7454 27.5772L26.8389 20.4837C27.3874 19.9351 28.2954 19.9351 28.844 20.4837C29.3925 21.0322 29.3925 21.9402 28.844 22.4888L21.7505 29.5822C21.4857 29.866 21.1074 29.9984 20.7479 29.9984Z" fill="#FF3B30"/>
</svg>`,
  height: 32,
  width: 32,
});

addIcon(IconKey.Cake, {
  body: `<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M15.9485 6.25013C15.9485 6.25013 15.9414 6.25013 15.9383 6.25013H10.4688V5.42513C11.3664 5.02435 11.893 4.44701 12.036 3.70404C12.3578 2.03138 10.4696 0.339974 10.2531 0.152474C10.0219 -0.0475261 9.67893 -0.0514323 9.44377 0.144661C9.22033 0.330599 7.27111 2.01185 7.58674 3.68763C7.72893 4.4431 8.27736 5.02825 9.21877 5.43138V6.25013H3.73205C2.35783 6.2556 1.24455 7.37825 1.25002 8.75013V9.78919C1.25002 10.6853 1.61877 11.5611 2.26174 12.19C2.2883 12.2165 2.31643 12.2415 2.34377 12.2665V16.2493C2.34377 16.9384 2.90471 17.4993 3.59377 17.4993H16.0938C16.7828 17.4993 17.3438 16.9384 17.3438 16.2493V12.2665C17.3711 12.2415 17.3992 12.2165 17.4258 12.19C18.0688 11.5603 18.4375 10.6853 18.4375 9.78841V8.73138C18.4321 7.36107 17.3149 6.24935 15.9477 6.24935L15.9485 6.25013ZM8.81565 3.45716C8.74611 3.09076 8.95549 2.56966 9.40549 1.98919C9.5508 1.80169 9.70236 1.63294 9.83518 1.49466C9.96252 1.63294 10.1063 1.80013 10.2446 1.98607C10.6797 2.57122 10.8797 3.09779 10.8078 3.46966C10.7414 3.81263 10.4156 4.10326 9.83908 4.33529C9.37893 4.15716 8.89377 3.87357 8.81486 3.45716H8.81565ZM2.50002 8.74779C2.49846 8.41576 2.62658 8.10326 2.86018 7.8681C3.09377 7.63216 3.40549 7.50169 3.73674 7.50091H15.9453C16.6281 7.50091 17.1844 8.05482 17.1875 8.73529V9.78997C17.1875 10.3525 16.9555 10.9025 16.5516 11.2978C16.1594 11.6814 15.6477 11.8868 15.1149 11.8759C14.018 11.8533 13.125 10.9423 13.1258 9.84466C13.1258 9.83607 13.125 9.82747 13.1242 9.8181C13.1242 9.8056 13.1242 9.7931 13.1227 9.7806C13.1227 9.77513 13.1211 9.76966 13.1203 9.76419C13.118 9.74935 13.1164 9.73372 13.1133 9.71888C13.1133 9.71576 13.1117 9.71263 13.111 9.70951C13.1071 9.69232 13.1031 9.67513 13.0977 9.65872C13.0969 9.6556 13.0953 9.65325 13.0946 9.65013C13.0891 9.63372 13.0836 9.61732 13.0766 9.60169C13.0688 9.58294 13.0602 9.56419 13.0508 9.547C13.0508 9.54544 13.0492 9.54466 13.0485 9.5431C13.0391 9.52669 13.0297 9.51029 13.0196 9.49544C13.0164 9.49154 13.0133 9.48763 13.0102 9.48372C13.0016 9.47122 12.993 9.45951 12.9836 9.44779C12.9805 9.44388 12.9774 9.44075 12.9742 9.43763C12.9641 9.42591 12.9539 9.41419 12.9438 9.40404C12.9399 9.40013 12.936 9.39701 12.9321 9.3931C12.9211 9.38294 12.911 9.37279 12.8992 9.36341C12.8946 9.3595 12.8899 9.35638 12.8852 9.35247C12.8742 9.34388 12.8633 9.33529 12.8516 9.32747C12.8469 9.32435 12.8414 9.32201 12.8367 9.31888C12.8242 9.31107 12.8125 9.30325 12.8 9.29622C12.7977 9.29466 12.7946 9.29388 12.7922 9.29232C12.7766 9.2845 12.7617 9.27669 12.7453 9.26966C12.7422 9.2681 12.7391 9.26732 12.736 9.26654C12.7203 9.26029 12.7039 9.25404 12.6875 9.24857C12.6797 9.24622 12.6719 9.24466 12.6641 9.24232C12.6524 9.23919 12.6399 9.23529 12.6281 9.23294C12.6196 9.23138 12.611 9.2306 12.6024 9.22904C12.5906 9.22747 12.5789 9.22513 12.5664 9.22357C12.5563 9.22279 12.5461 9.22279 12.5367 9.222C12.5258 9.222 12.5141 9.22044 12.5031 9.22044C12.4672 9.22044 12.4328 9.22435 12.3985 9.22982C12.3914 9.23138 12.3844 9.23138 12.3774 9.23294C12.1149 9.28685 11.9125 9.50482 11.8821 9.77591C11.8821 9.77747 11.8821 9.77982 11.8813 9.78138C11.8797 9.79466 11.8797 9.80716 11.8789 9.82044C11.8789 9.82904 11.8774 9.83685 11.8774 9.84466C11.8774 10.965 10.9664 11.8759 9.84611 11.8759C8.7258 11.8759 7.81486 10.965 7.81486 9.84466C7.81486 9.83216 7.81408 9.81966 7.8133 9.80794C7.8133 9.79935 7.8133 9.78997 7.81174 9.7806C7.81096 9.77122 7.80861 9.76263 7.80783 9.75404C7.80627 9.74232 7.80471 9.7306 7.80236 9.71888C7.80236 9.71654 7.8008 9.71419 7.8008 9.71185C7.74846 9.47122 7.55783 9.28216 7.31643 9.23216C7.2758 9.22357 7.23361 9.21966 7.19065 9.21966C7.18205 9.21966 7.17346 9.22044 7.16486 9.22122C7.15236 9.22122 7.13908 9.22122 7.12658 9.22279C7.1219 9.22279 7.11721 9.22435 7.11252 9.22513C7.09611 9.22747 7.07971 9.22904 7.06408 9.23216L7.06096 9.23294C7.02111 9.24154 6.98283 9.25325 6.9469 9.26888H6.94533C6.81486 9.32513 6.70783 9.42357 6.64065 9.547C6.63596 9.5556 6.63205 9.56419 6.62815 9.57357C6.62346 9.58294 6.61877 9.59232 6.61486 9.60169C6.61018 9.61185 6.60705 9.62279 6.60315 9.63294C6.60002 9.64154 6.59611 9.65013 6.59377 9.65872C6.58908 9.67357 6.58596 9.68841 6.58205 9.70404C6.58127 9.70872 6.57971 9.71341 6.57815 9.71888C6.57502 9.73372 6.57346 9.74935 6.57111 9.76419C6.57111 9.76966 6.56877 9.77513 6.56877 9.7806C6.56721 9.7931 6.56721 9.8056 6.56721 9.8181C6.56721 9.82669 6.56565 9.83529 6.56565 9.84388C6.56565 10.9415 5.67346 11.8525 4.5758 11.8751C4.04221 11.8868 3.53127 11.6814 3.13908 11.297C2.7344 10.9009 2.50315 10.3517 2.50315 9.78919V8.74779H2.50002ZM16.0938 16.2501H3.59377V12.9861C3.89455 13.0783 4.20939 13.1259 4.53049 13.1259C4.55315 13.1259 4.5758 13.1259 4.59924 13.1259C5.65861 13.104 6.59924 12.5689 7.18518 11.7642C7.78205 12.5884 8.7508 13.1259 9.84377 13.1259C10.9367 13.1259 11.9063 12.5884 12.5024 11.7642C13.0883 12.5697 14.0289 13.104 15.0883 13.1259C15.4344 13.1337 15.7719 13.0861 16.0938 12.9876V16.2509V16.2501Z" fill="#ADACAA"/>
</svg>`,
  height: 20,
  width: 20,
});

addIcon(IconKey.IdentityCard, {
  body: `<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M14.168 18.125H5.83464C2.15964 18.125 1.04297 17.0083 1.04297 13.3333V6.66667C1.04297 2.99167 2.15964 1.875 5.83464 1.875H14.168C17.843 1.875 18.9596 2.99167 18.9596 6.66667V13.3333C18.9596 17.0083 17.843 18.125 14.168 18.125ZM5.83464 3.125C2.8513 3.125 2.29297 3.69167 2.29297 6.66667V13.3333C2.29297 16.3083 2.8513 16.875 5.83464 16.875H14.168C17.1513 16.875 17.7096 16.3083 17.7096 13.3333V6.66667C17.7096 3.69167 17.1513 3.125 14.168 3.125H5.83464Z" fill="#ADACAA"/>
<path d="M15.8346 7.29199H11.668C11.3263 7.29199 11.043 7.00866 11.043 6.66699C11.043 6.32533 11.3263 6.04199 11.668 6.04199H15.8346C16.1763 6.04199 16.4596 6.32533 16.4596 6.66699C16.4596 7.00866 16.1763 7.29199 15.8346 7.29199Z" fill="#ADACAA"/>
<path d="M15.8333 10.625H12.5C12.1583 10.625 11.875 10.3417 11.875 10C11.875 9.65833 12.1583 9.375 12.5 9.375H15.8333C16.175 9.375 16.4583 9.65833 16.4583 10C16.4583 10.3417 16.175 10.625 15.8333 10.625Z" fill="#ADACAA"/>
<path d="M15.8346 13.958H14.168C13.8263 13.958 13.543 13.6747 13.543 13.333C13.543 12.9913 13.8263 12.708 14.168 12.708H15.8346C16.1763 12.708 16.4596 12.9913 16.4596 13.333C16.4596 13.6747 16.1763 13.958 15.8346 13.958Z" fill="#ADACAA"/>
<path d="M7.08255 10.0333C5.90755 10.0333 4.94922 9.07493 4.94922 7.89993C4.94922 6.72493 5.90755 5.7666 7.08255 5.7666C8.25755 5.7666 9.21589 6.72493 9.21589 7.89993C9.21589 9.07493 8.25755 10.0333 7.08255 10.0333ZM7.08255 7.0166C6.59922 7.0166 6.19922 7.4166 6.19922 7.89993C6.19922 8.38327 6.59922 8.78327 7.08255 8.78327C7.56589 8.78327 7.96589 8.38327 7.96589 7.89993C7.96589 7.4166 7.56589 7.0166 7.08255 7.0166Z" fill="#ADACAA"/>
<path d="M10.0009 14.2335C9.68423 14.2335 9.40923 13.9918 9.3759 13.6668C9.28423 12.7668 8.55923 12.0418 7.6509 11.9585C7.26757 11.9251 6.88423 11.9251 6.5009 11.9585C5.59257 12.0418 4.86757 12.7585 4.7759 13.6668C4.74257 14.0085 4.43423 14.2668 4.09256 14.2251C3.7509 14.1918 3.5009 13.8835 3.53423 13.5418C3.68423 12.0418 4.8759 10.8501 6.38423 10.7168C6.84257 10.6751 7.30923 10.6751 7.76756 10.7168C9.26756 10.8585 10.4676 12.0501 10.6176 13.5418C10.6509 13.8835 10.4009 14.1918 10.0592 14.2251C10.0426 14.2335 10.0176 14.2335 10.0009 14.2335Z" fill="#ADACAA"/>
</svg>`,
  height: 20,
  width: 20,
});

addIcon(IconKey.Gender, {
  body: `<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M9.375 4.375C7.13134 4.375 5.3125 6.19384 5.3125 8.4375C5.3125 10.6812 7.13134 12.5 9.375 12.5C11.6187 12.5 13.4375 10.6812 13.4375 8.4375C13.4375 6.19384 11.6187 4.375 9.375 4.375ZM4.0625 8.4375C4.0625 5.50349 6.44099 3.125 9.375 3.125C12.309 3.125 14.6875 5.50349 14.6875 8.4375C14.6875 11.3715 12.309 13.75 9.375 13.75C6.44099 13.75 4.0625 11.3715 4.0625 8.4375Z" fill="#ADACAA"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M9.375 12.5C9.72018 12.5 10 12.7798 10 13.125V18.125C10 18.4702 9.72018 18.75 9.375 18.75C9.02982 18.75 8.75 18.4702 8.75 18.125V13.125C8.75 12.7798 9.02982 12.5 9.375 12.5Z" fill="#ADACAA"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M6.25 15.9375C6.25 15.5923 6.52982 15.3125 6.875 15.3125H11.875C12.2202 15.3125 12.5 15.5923 12.5 15.9375C12.5 16.2827 12.2202 16.5625 11.875 16.5625H6.875C6.52982 16.5625 6.25 16.2827 6.25 15.9375Z" fill="#ADACAA"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M12.5 1.875C12.5 1.52982 12.7798 1.25 13.125 1.25H16.25C16.5952 1.25 16.875 1.52982 16.875 1.875V5C16.875 5.34518 16.5952 5.625 16.25 5.625C15.9048 5.625 15.625 5.34518 15.625 5V2.5H13.125C12.7798 2.5 12.5 2.22018 12.5 1.875Z" fill="#ADACAA"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M16.6918 1.43306C16.9359 1.67714 16.9359 2.07286 16.6918 2.31694L13.2856 5.72319C13.0415 5.96727 12.6458 5.96727 12.4017 5.72319C12.1576 5.47911 12.1576 5.08339 12.4017 4.83931L15.8079 1.43306C16.052 1.18898 16.4477 1.18898 16.6918 1.43306Z" fill="#ADACAA"/>
</svg>`,
  height: 20,
  width: 20,
});

addIcon(IconKey.ShieldTick, {
  body: `<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M8.73984 1.85879L4.58151 3.42546C3.62318 3.78379 2.83984 4.91712 2.83984 5.93379V12.1255C2.83984 13.1088 3.48984 14.4005 4.28151 14.9921L7.86484 17.6671C9.03984 18.5505 10.9732 18.5505 12.1482 17.6671L15.7315 14.9921C16.5232 14.4005 17.1732 13.1088 17.1732 12.1255V5.93379C17.1732 4.90879 16.3898 3.77546 15.4315 3.41712L11.2732 1.85879C10.5648 1.60046 9.43151 1.60046 8.73984 1.85879Z" stroke="#ADACAA" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M7.54297 9.89206L8.88464 11.2337L12.468 7.65039" stroke="#ADACAA" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round"/>
</svg>`,
  height: 20,
  width: 20,
});

addIcon(IconKey.DotSix, {
  body: `<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M7.1875 5.625C7.70527 5.625 8.125 5.20527 8.125 4.6875C8.125 4.16973 7.70527 3.75 7.1875 3.75C6.66973 3.75 6.25 4.16973 6.25 4.6875C6.25 5.20527 6.66973 5.625 7.1875 5.625Z" fill="#1E2328"/>
<path d="M12.8125 5.625C13.3303 5.625 13.75 5.20527 13.75 4.6875C13.75 4.16973 13.3303 3.75 12.8125 3.75C12.2947 3.75 11.875 4.16973 11.875 4.6875C11.875 5.20527 12.2947 5.625 12.8125 5.625Z" fill="#1E2328"/>
<path d="M7.1875 10.9375C7.70527 10.9375 8.125 10.5178 8.125 10C8.125 9.48223 7.70527 9.0625 7.1875 9.0625C6.66973 9.0625 6.25 9.48223 6.25 10C6.25 10.5178 6.66973 10.9375 7.1875 10.9375Z" fill="#1E2328"/>
<path d="M12.8125 10.9375C13.3303 10.9375 13.75 10.5178 13.75 10C13.75 9.48223 13.3303 9.0625 12.8125 9.0625C12.2947 9.0625 11.875 9.48223 11.875 10C11.875 10.5178 12.2947 10.9375 12.8125 10.9375Z" fill="#1E2328"/>
<path d="M7.1875 16.25C7.70527 16.25 8.125 15.8303 8.125 15.3125C8.125 14.7947 7.70527 14.375 7.1875 14.375C6.66973 14.375 6.25 14.7947 6.25 15.3125C6.25 15.8303 6.66973 16.25 7.1875 16.25Z" fill="#1E2328"/>
<path d="M12.8125 16.25C13.3303 16.25 13.75 15.8303 13.75 15.3125C13.75 14.7947 13.3303 14.375 12.8125 14.375C12.2947 14.375 11.875 14.7947 11.875 15.3125C11.875 15.8303 12.2947 16.25 12.8125 16.25Z" fill="#1E2328"/>
</svg>`,
  height: 32,
  width: 32,
});

addIcon(IconKey.Palette, {
  body: `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M13.473 2.44002C11.9759 0.968407 10.0055 0.16687 7.91785 0.187904C5.83175 0.21044 3.87786 1.04353 2.41827 2.53467C0.957924 4.02582 0.166153 5.99623 0.187938 8.08308C0.222493 11.3501 2.3146 14.2768 5.39155 15.3653C5.65897 15.4614 5.93241 15.5073 6.20135 15.5073C7.18317 15.5073 8.10641 14.8988 8.45797 13.923C8.55337 13.6578 8.60145 13.3806 8.59995 13.1026V12.8074C8.59694 12.1486 9.13105 11.6108 9.78685 11.6077H13.274C14.4 11.6077 15.3631 10.8415 15.6162 9.74475V9.74325C15.7514 9.1513 15.8168 8.54357 15.8123 7.93735C15.7965 5.86026 14.9657 3.90863 13.473 2.44077V2.44002ZM14.4451 9.47432C14.3181 10.0227 13.8366 10.4051 13.274 10.4051H9.78535C8.46398 10.4103 7.39351 11.4898 7.39877 12.8089V13.1049C7.39877 13.2454 7.37548 13.3836 7.32816 13.5151C7.10505 14.1348 6.4192 14.4571 5.79645 14.2325C3.19051 13.3107 1.41992 10.834 1.39062 8.06956C1.37184 6.30423 2.04192 4.63655 3.27765 3.37527C4.51339 2.114 6.16604 1.40861 7.93062 1.38983C7.95166 1.38983 7.97269 1.38983 7.99448 1.38983C9.73427 1.38983 11.3787 2.06592 12.6309 3.29715C13.8945 4.53889 14.5976 6.19004 14.6104 7.94636C14.6149 8.45944 14.5593 8.97402 14.4451 9.47432Z" fill="#ADACAA"/>
<path d="M7.99973 4.99638C8.49759 4.99638 8.90118 4.59278 8.90118 4.09493C8.90118 3.59707 8.49759 3.19348 7.99973 3.19348C7.50188 3.19348 7.09829 3.59707 7.09829 4.09493C7.09829 4.59278 7.50188 4.99638 7.99973 4.99638Z" fill="#ADACAA"/>
<path d="M4.61931 6.94951C5.11716 6.94951 5.52075 6.54592 5.52075 6.04807C5.52075 5.55021 5.11716 5.14662 4.61931 5.14662C4.12145 5.14662 3.71786 5.55021 3.71786 6.04807C3.71786 6.54592 4.12145 6.94951 4.61931 6.94951Z" fill="#ADACAA"/>
<path d="M4.61931 10.8558C5.11716 10.8558 5.52075 10.4522 5.52075 9.95434C5.52075 9.45648 5.11716 9.05289 4.61931 9.05289C4.12145 9.05289 3.71786 9.45648 3.71786 9.95434C3.71786 10.4522 4.12145 10.8558 4.61931 10.8558Z" fill="#ADACAA"/>
<path d="M11.3802 6.94951C11.878 6.94951 12.2816 6.54592 12.2816 6.04807C12.2816 5.55021 11.878 5.14662 11.3802 5.14662C10.8823 5.14662 10.4787 5.55021 10.4787 6.04807C10.4787 6.54592 10.8823 6.94951 11.3802 6.94951Z" fill="#ADACAA"/>
</svg>
`,
});
