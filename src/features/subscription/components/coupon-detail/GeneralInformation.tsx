import { BookingEntity, CreateSubscriptionCouponDto } from '#/api/requests';
import { FormInput } from '#/shared/components/formField/FormInput';
import { FormInputArea } from '#/shared/components/formField/FormInputArea';
import { FormInputNumber } from '#/shared/components/formField/FormInputNumber';
import { FormRadio } from '#/shared/components/formField/FormRadio';
import { TypographyUI } from '#/shared/components/TypographyUI';
import { Checkbox, Col, Radio, Row } from 'antd';
import { useTranslation } from 'react-i18next';
import { type UseFormReturn } from 'react-hook-form';
import { useEffect, useState } from 'react';
import useSubscriptionStore from '#/store/useSubscriptionStore';
import { ErrorMessage } from '#/shared/components/formField/ErrorMessage';
import { FormTreeSelector } from '#/shared/components/selectors/TreeSelector';
import { type CheckboxChangeEvent } from 'antd/es/checkbox';

const vehicleDefault = [
  BookingEntity.vehicleType.TYPICAL_CAR,
  BookingEntity.vehicleType.ACCESSIBLE_VAN,
  BookingEntity.vehicleType.VAN,
  BookingEntity.vehicleType.ELECTRIC_CAR,
];

interface Props {
  type: CreateSubscriptionCouponDto.type;
  form: UseFormReturn<CreateSubscriptionCouponDto>;
  disabled?: boolean;
}

export function GeneralInformation({ form, type, disabled }: Props) {
  const { t } = useTranslation();
  const {
    isShowErrorStore,
    setIsCheckMaxDiscountStore,
    setIsCheckMinSpendStore,
  } = useSubscriptionStore();

  const { maxDiscountLimit, minSpendForAdoption } = form.getValues();
  const [isCheckMinSpend, setIsCheckMinSpend] = useState<boolean>(false);
  const [isCheckMaxDiscount, setIsCheckMaxDiscount] = useState<boolean>(false);

  const [couponType, setCouponType] =
    useState<CreateSubscriptionCouponDto.type>(type);

  const ALL_VEHICLES = [
    {
      label: t('coupon.create.vehicleTypes.typical'),
      value: BookingEntity.vehicleType.TYPICAL_CAR,
    },
    {
      label: t('coupon.create.vehicleTypes.van'),
      value: BookingEntity.vehicleType.VAN,
    },
    {
      label: t('coupon.create.vehicleTypes.accessible'),
      value: BookingEntity.vehicleType.ACCESSIBLE_VAN,
    },
    {
      label: t('coupon.create.vehicleTypes.electric'),
      value: BookingEntity.vehicleType.ELECTRIC_CAR,
    },
  ];

  const vehicleTreeData = [
    {
      children: ALL_VEHICLES,
      label: t('coupon.create.vehicleTypes.all'),
      value: vehicleDefault.toString(),
    },
  ];

  const amountPlaceholder = {
    [CreateSubscriptionCouponDto.type.PERCENTAGE]: t(
      'coupon.create.percentage.placeholder',
    ),
    [CreateSubscriptionCouponDto.type.AMOUNT]: t(
      'coupon.create.amount.placeholder',
      { unit: '$CAD' },
    ),
    [CreateSubscriptionCouponDto.type.DROP_RATE]: t(
      'coupon.create.dropRate.placeholder',
    ),
  };

  const onChangeCouponType = (event: CheckboxChangeEvent) => {
    setIsCheckMaxDiscountStore(false);
    const value = event.target.value as CreateSubscriptionCouponDto.type;
    setCouponType(value);
    form.setValue('type', value);
    if (form.getValues('type') === CreateSubscriptionCouponDto.type.AMOUNT)
      form.setValue('maxDiscountLimit', 0);
    if (form.getValues('type') === CreateSubscriptionCouponDto.type.DROP_RATE)
      form.setValue('topUpPercent', 0);

    if (form.getValues('value')) form.setValue('value', 0);
  };

  useEffect(() => {
    if (form.getValues('maxDiscountLimit')) setIsCheckMaxDiscount(true);
    if (form.getValues('minSpendForAdoption')) setIsCheckMinSpend(true);
  }, []);

  return (
    <Row gutter={[12, 12]}>
      <Col span={12}>
        <FormInput
          disabled={disabled}
          form={form}
          label={t('coupon.create.couponTitle.title')}
          name="title"
          placeholder={t('coupon.create.couponTitle.placeholder')}
          showRequired
        />
      </Col>
      <Col span={12}>
        <FormInput
          disabled={disabled}
          form={form}
          label={t('coupon.create.couponTitle.titleFr')}
          name="titleFr"
          placeholder={t('coupon.create.couponTitle.placeholder')}
          showRequired
        />
      </Col>
      <Col span={12}>
        <FormInputArea
          disabled={disabled}
          form={form}
          label={t('subscription.create.description')}
          name="description"
          placeholder={t('subscription.create.descriptionPlaceholder')}
          rows={2}
          showRequired
        />
      </Col>
      <Col span={12}>
        <FormInputArea
          disabled={disabled}
          form={form}
          label={t('subscription.create.descriptionFr')}
          name="descriptionFr"
          placeholder={t('subscription.create.descriptionPlaceholder')}
          rows={2}
          showRequired
        />
      </Col>
      <Col span={12}>
        <FormInputNumber
          className="w-full"
          disabled={disabled}
          form={form}
          formatter={
            couponType === CreateSubscriptionCouponDto.type.PERCENTAGE
              ? value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
              : undefined
          }
          label={
            <div className="mb-2">
              <FormRadio
                disabled={disabled}
                form={form}
                haveShowError={false}
                label={null}
                name="type"
                onChange={onChangeCouponType}
              >
                <Radio value={CreateSubscriptionCouponDto.type.AMOUNT}>
                  <TypographyUI font="Semibold" type="BodySm">
                    {t('coupon.create.amount.title', {
                      unit: '$CAD',
                    })}
                  </TypographyUI>
                </Radio>
                <Radio value={CreateSubscriptionCouponDto.type.PERCENTAGE}>
                  <TypographyUI font="Semibold" type="BodySm">
                    {t('coupon.create.percentage.title')}
                  </TypographyUI>
                </Radio>
                <Radio value={CreateSubscriptionCouponDto.type.DROP_RATE}>
                  <TypographyUI font="Semibold" type="BodySm">
                    {t('coupon.create.dropRate.title')}
                    <span className="text-error-color">*</span>
                  </TypographyUI>
                </Radio>
              </FormRadio>
            </div>
          }
          min={0}
          name="value"
          placeholder={amountPlaceholder[type]}
          prefix={
            type === CreateSubscriptionCouponDto.type.DROP_RATE ? (
              <div className="flex items-center border-r h-full border-solid border-neutral-300">
                <div className="mr-2">$1 CAD = </div>
              </div>
            ) : (
              <></>
            )
          }
          showRequired
          suffix={
            type === CreateSubscriptionCouponDto.type.DROP_RATE ? (
              <span className="text-neutral-300">
                {t('loyalPoints.loyalPoints')}
              </span>
            ) : (
              <></>
            )
          }
          type="number"
        />
      </Col>
      <Col span={12}>
        <Checkbox
          checked={isCheckMaxDiscount}
          className="font-semibold text-sm mb-2"
          disabled={
            disabled ||
            couponType !== CreateSubscriptionCouponDto.type.PERCENTAGE
          }
          name="checkMaxDiscountLimit"
          onChange={() => {
            setIsCheckMaxDiscount(!isCheckMaxDiscount);
            setIsCheckMaxDiscountStore(!isCheckMaxDiscount);

            if (form.getValues('maxDiscountLimit'))
              form.setValue('maxDiscountLimit', 0);
          }}
        >
          {t('coupon.create.checkMaxDiscountLimit.title')}
        </Checkbox>
        <FormInputNumber
          className="w-full"
          disabled={
            disabled ||
            couponType !== CreateSubscriptionCouponDto.type.PERCENTAGE ||
            !isCheckMaxDiscount
          }
          form={form}
          label={null}
          name="maxDiscountLimit"
          placeholder={t('coupon.create.checkMaxDiscountLimit.placeholder')}
          type="number"
        />
        {isShowErrorStore &&
        isCheckMaxDiscount &&
        couponType === CreateSubscriptionCouponDto.type.PERCENTAGE &&
        !maxDiscountLimit ? (
          <ErrorMessage>{t('common.validate.pleaseEnterAmount')}</ErrorMessage>
        ) : null}
      </Col>

      <Col span={12}>
        <FormInputNumber
          className="w-full"
          disabled={disabled}
          form={form}
          label={t('subscription.create.quantity')}
          name="quantity"
          placeholder={t('subscription.create.quantityPlaceholder')}
          showRequired
          type="number"
        />
      </Col>

      <Col span={12}>
        <FormTreeSelector
          className="h-10 w-full"
          disabled={disabled}
          form={form}
          label={t('coupon.create.vehicleTypes.title')}
          mode="multiple"
          name="vehicleTypes"
          showRequired
          treeData={vehicleTreeData}
          treeDefaultExpandAll
        />
      </Col>

      <Col className=" mb-2" span={12}>
        <FormTreeSelector
          className="h-10 w-full"
          defaultValue={BookingEntity.paymentMethodType.CARD}
          disabled
          form={form}
          label={t('coupon.create.paymentMethod')}
          mode="multiple"
          name="paymentMethods"
          showRequired
          treeData={[]}
          treeDefaultExpandAll
        />
      </Col>

      <Col className="mb-2" span={12}>
        <Checkbox
          checked={isCheckMinSpend}
          className="font-semibold text-sm xs:text-sm"
          disabled={disabled}
          name="checkMinSpendForAdoption"
          onChange={() => {
            setIsCheckMinSpend(!isCheckMinSpend);
            setIsCheckMinSpendStore(!isCheckMinSpend);

            if (isCheckMinSpend) {
              form.setValue('minSpendForAdoption', 0);
            }
          }}
        >
          {t('coupon.create.checkMinSpendForAdoption.title')}
        </Checkbox>
        <FormInputNumber
          className="mt-2 w-full"
          disabled={disabled || !isCheckMinSpend}
          form={form}
          label={null}
          name="minSpendForAdoption"
          placeholder={t('coupon.create.checkMinSpendForAdoption.placeholder')}
          showRequired
          type="number"
        />
        {isShowErrorStore && isCheckMinSpend && !minSpendForAdoption ? (
          <ErrorMessage>{t('common.validate.pleaseEnterAmount')}</ErrorMessage>
        ) : null}
      </Col>

      <Col span={12}>
        {form.watch('type') !== CreateSubscriptionCouponDto.type.DROP_RATE && (
          <FormInputNumber
            className="mt-2 w-full"
            disabled={disabled}
            form={form}
            formatter={value =>
              `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
            }
            label={
              <div className="text-neutral-900 font-semibold">
                {t('coupon.create.taxiLoyalSplit.title')} {' (%)'}
              </div>
            }
            max={100}
            min={0}
            name="topUpPercent"
            placeholder={t('coupon.create.taxiLoyalSplit.placeholder')}
            type="number"
          />
        )}
      </Col>
    </Row>
  );
}
