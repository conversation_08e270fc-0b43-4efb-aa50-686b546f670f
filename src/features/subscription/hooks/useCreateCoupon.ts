/* eslint-disable @typescript-eslint/no-explicit-any */
import { useForm } from 'react-hook-form';
import { upsertCouponSchema } from '../schema/coupon.schema';
import type { ObjectSchema } from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import useSubscriptionStore from '../../../store/useSubscriptionStore';
import { useEffect, useState } from 'react';
import { BookingEntity, CreateSubscriptionCouponDto } from '#/api/requests';
import { useSubscriptionsServiceSubscriptionSuperAdminControllerCreateCouponOfSubscription } from '#/api/queries';
import { showErrorMessage } from '#/shared/utils';
import { notification } from 'antd';
import { t } from 'i18next';
import {
  DEFAULT_DIVIDE_BY_METER,
  DEFAULT_UINT_DIVIDED_BY_CENT,
} from '#/shared/constants';
import { useParams } from 'react-router-dom';
import { useCouponList } from './useCouponList';

interface Props {
  onCancel?: () => void;
  onSuccess?: () => void;
  isCreate?: boolean;
}

export const useCreateCoupon = ({ onCancel, onSuccess, isCreate }: Props) => {
  const params = useParams();
  const [isDisableCreate, setIsDisableCreate] = useState<boolean>(false);

  const {
    isConfigAreaCheckboxStore,
    isDisableCreateStore,
    isConfigPickupStore,
    isConfigDropOffStore,
    isCheckMaxDiscountStore,
    isCheckMinSpendStore,
    setIsShowErrorStore,
    setDisableCreateStore,
    setIsConfigPickupStore,
    setIsConfigDropOffStore,
    setIsCheckMaxDiscountStore,
    setIsCheckMinSpendStore,
  } = useSubscriptionStore();
  const form = useForm<CreateSubscriptionCouponDto>({
    resolver: yupResolver(
      upsertCouponSchema as unknown as ObjectSchema<CreateSubscriptionCouponDto>,
    ),
  });

  const {
    applyForPickUpAreaCoordinates,
    applyForPickUpAreaRadius,
    applyForDropOffAreaCoordinates,
    applyForDropOffAreaRadius,
    maxDiscountLimit,
    minSpendForAdoption,
  } = form.getValues();

  const { refetch } = useCouponList();

  const { mutate, isPending } =
    useSubscriptionsServiceSubscriptionSuperAdminControllerCreateCouponOfSubscription(
      {
        onError: showErrorMessage,
        onSuccess() {
          onSuccess?.();
          onCancel?.();
          notification.success({
            description: t('coupon.create.notification.description'),
            message: t('coupon.create.notification.message'),
          });
          refetch();
        },
      },
    );

  const setDefaultValue = () => {
    form.setValue('paymentMethods', ['CARD']);
    form.setValue('type', CreateSubscriptionCouponDto.type.AMOUNT);
    form.setValue('vehicleTypes', [
      BookingEntity.vehicleType.TYPICAL_CAR,
      BookingEntity.vehicleType.ACCESSIBLE_VAN,
      BookingEntity.vehicleType.VAN,
      BookingEntity.vehicleType.ELECTRIC_CAR,
    ]);
  };

  useEffect(() => {
    setDefaultValue();
  }, []);

  const onCloseModal = () => {
    onCancel?.();
    setIsShowErrorStore(false);
    setIsConfigPickupStore(false);
    setIsConfigDropOffStore(false);
    setIsCheckMaxDiscountStore(false);
    setIsCheckMinSpendStore(false);
    setDisableCreateStore(false);
  };

  const onCreateCouponInSubscription = () => {
    setIsShowErrorStore(true);

    !isDisableCreate &&
      form.handleSubmit(formData => {
        console.log('formData', formData);
        const {
          applyForPickUpAreaCoordinates,
          applyForDropOffAreaCoordinates,
        } = formData;

        if (applyForPickUpAreaCoordinates) {
          const [lat, lng] = applyForPickUpAreaCoordinates
            .toString()
            .split(',');
          formData.applyForPickUpAreaCoordinates = [Number(lat), Number(lng)];
        }

        if (applyForDropOffAreaCoordinates) {
          const [lat, lng] = applyForDropOffAreaCoordinates
            .toString()
            .split(',');
          formData.applyForDropOffAreaCoordinates = [Number(lat), Number(lng)];
        }

        if (maxDiscountLimit) {
          formData.maxDiscountLimit =
            maxDiscountLimit * DEFAULT_UINT_DIVIDED_BY_CENT;
        }

        if (minSpendForAdoption) {
          formData.minSpendForAdoption =
            minSpendForAdoption * DEFAULT_UINT_DIVIDED_BY_CENT;
        }

        mutate({
          id: params.id as string,
          requestBody: {
            ...formData,
            applyForDropOffAreaRadius: applyForDropOffAreaRadius
              ? applyForDropOffAreaRadius * DEFAULT_DIVIDE_BY_METER
              : null,
            applyForPickUpAreaRadius: applyForPickUpAreaRadius
              ? applyForPickUpAreaRadius * DEFAULT_DIVIDE_BY_METER
              : null,
            value:
              formData.type === CreateSubscriptionCouponDto.type.AMOUNT
                ? formData.value * DEFAULT_UINT_DIVIDED_BY_CENT
                : formData.value,
          },
        });
      })();
  };

  useEffect(() => {
    if (
      (applyForPickUpAreaCoordinates && !applyForPickUpAreaRadius) ||
      (!applyForPickUpAreaCoordinates && applyForPickUpAreaRadius) ||
      (applyForDropOffAreaCoordinates && !applyForDropOffAreaRadius) ||
      (!applyForDropOffAreaCoordinates && applyForDropOffAreaRadius) ||
      (isConfigAreaCheckboxStore &&
        !applyForPickUpAreaCoordinates &&
        !applyForPickUpAreaRadius &&
        !applyForDropOffAreaCoordinates &&
        !applyForDropOffAreaRadius) ||
      (isConfigPickupStore &&
        (!applyForPickUpAreaCoordinates || !applyForPickUpAreaRadius)) ||
      (isConfigDropOffStore &&
        (!applyForDropOffAreaCoordinates || !applyForDropOffAreaRadius)) ||
      (isCheckMaxDiscountStore && maxDiscountLimit == undefined) ||
      (isCheckMinSpendStore && minSpendForAdoption == undefined)
    ) {
      setDisableCreateStore(true);
    } else {
      setDisableCreateStore(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    applyForPickUpAreaCoordinates,
    applyForPickUpAreaRadius,
    applyForDropOffAreaCoordinates,
    applyForDropOffAreaRadius,
    maxDiscountLimit,
    minSpendForAdoption,
    isCheckMinSpendStore,
    isCheckMaxDiscountStore,
    isConfigAreaCheckboxStore,
    isConfigPickupStore,
    isConfigDropOffStore,
  ]);

  useEffect(() => {
    setIsDisableCreate(isDisableCreateStore);
  }, [isDisableCreateStore]);

  return {
    form,
    isCreate,
    isPending,
    onCloseModal,
    onCreateCouponInSubscription,
  };
};
