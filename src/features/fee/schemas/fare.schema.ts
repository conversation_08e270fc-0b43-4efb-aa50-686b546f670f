import { CreateFixedFareDto } from '#/api/requests';
import { yupResolver } from '@hookform/resolvers/yup';
import i18next from 'i18next';
import * as yup from 'yup';

export const fareSchema = () =>
  yupResolver(
    yup.object().shape({
      amount: yup
        .number()
        .min(5, i18next.t('common.validate.requiredMin', { min: 5 }))
        .required(i18next.t('common.validate.requiredField'))
        .test(
          'is-decimal',
          'Price must be a valid number with up to 2 decimal places',
          value => (value ? /^\d+(\.\d{1,2})?$/.test(value.toString()) : false),
        ),
      applyDateType: yup
        .string()
        .required(i18next.t('common.validate.requiredField')),
      applyRepeatDay: yup.array().when('applyDateType', {
        is: CreateFixedFareDto.applyDateType.DAY,
        then: schema =>
          schema.required(i18next.t('common.validate.requiredField')),
      }),
      endApplyDate: yup.string().nullable(),
      endTime: yup.object().nullable(),
      fromZones: yup.array().min(1, i18next.t('common.validate.requiredField')),
      isAllowCoupon: yup.boolean(),
      isRoundTrip: yup.boolean(),
      startApplyDate: yup.string().nullable(),
      startTime: yup.object().nullable(),
      status: yup.string().nullable(),
      toZones: yup.array().min(1, i18next.t('common.validate.requiredField')),
      vehicleTypes: yup
        .array()
        .min(1, i18next.t('common.validate.requiredField')),
    }),
  );
