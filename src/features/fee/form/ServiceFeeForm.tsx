import { CreateServiceFeeDto } from '#/api/requests';
import { FormDatePicker } from '#/shared/components/formField/FormDatePicker';
import { FormInputNumber } from '#/shared/components/formField/FormInputNumber';
import { FormRadio } from '#/shared/components/formField/FormRadio';
import { FormEntitySelector } from '#/shared/components/selectors/FormEntitySelector';
import { TypographyUI } from '#/shared/components/TypographyUI';
import { Checkbox, Col, Form, Radio, type RadioChangeEvent, Row } from 'antd';
import { type UseFormReturn } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { FormInput } from '../../../shared/components/formField/FormInput';
import { FormServiceSelector } from '#/shared/components/selectors/Service/FormServiceSelector';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import useConfigFeeStore from '#/store/useConfigFeeStore';
import type { DataType } from '../components/SpecificTable';
import SpecificTable from '../components/SpecificTable';

interface Props {
  form: UseFormReturn<CreateServiceFeeDto>;
  defaultVehicles?: string[];
}

export function FeeForm({ form, defaultVehicles }: Props) {
  const { t } = useTranslation();
  const [isCheckPickUpArea, setIsCheckPickUpArea] = useState<boolean>(false);
  const [isCheckDropOffArea, setIsCheckDropOffArea] = useState<boolean>(false);

  const [pickupList, setPickupList] = useState<DataType[]>([]);
  const [dropOffList, setDropOffList] = useState<DataType[]>([]);
  const { specificPickUpAreas, specificDropOffAreas } = form.getValues();

  const {
    isShowErrorStore,
    setApplyForPickUpAreaStore,
    setApplyForDropOffAreaStore,
    setIsConfigPickupStore,
    setIsConfigDropOffStore,
    applyForDropOffAreaStore,
    applyForPickUpAreaStore,
    setIsEditingPickupAreaStore,
    setIsEditingDropOffAreaStore,
  } = useConfigFeeStore();

  const [applyFromType, setApplyFromType] = useState(
    CreateServiceFeeDto.applyFromType.ALL,
  );

  const basedOnOptions = [
    {
      label: t('fee.create.basedOn.ride'),
      value: CreateServiceFeeDto.perType.RIDE,
    },
    {
      label: t('fee.create.basedOn.km'),
      value: CreateServiceFeeDto.perType.KILOMETER,
    },
  ];

  const onChangeApplyFromType = (e: RadioChangeEvent) => {
    setApplyFromType(e.target.value as CreateServiceFeeDto.applyFromType);
    form.setValue('applyFrom', '');
  };

  useEffect(() => {
    setApplyFromType(form.getValues('applyFromType'));
  }, [form.getValues('applyFromType')]);

  useEffect(() => {
    if (pickupList.length) {
      const coordinates = pickupList.map(
        pickup => pickup?.coordinates?.split(','),
      );

      const newPickupList = pickupList.map((pickup, index) => ({
        coordinates: [
          Number(coordinates[index][0]),
          Number(coordinates[index][1]),
        ],
        name: pickup.name,
        radius: pickup?.radius,
      }));
      setApplyForPickUpAreaStore(newPickupList);
    } else {
      setApplyForPickUpAreaStore([]);
    }
  }, [pickupList]);

  useEffect(() => {
    if (specificPickUpAreas?.length) {
      setIsCheckPickUpArea(true);
      const coordinates = specificPickUpAreas.map(
        pickup => pickup?.coordinates,
      ) as [][];

      const newApplyForPickUpArea = specificPickUpAreas.map(
        (pickup, index) => ({
          coordinates: `${String(coordinates[index][0])}, ${String(
            coordinates[index][1],
          )}`,
          key: index.toString(),
          name: pickup.name,
          radius: pickup.radius,
        }),
      );
      setPickupList(newApplyForPickUpArea);
    }
  }, [specificPickUpAreas]);

  useEffect(() => {
    if (dropOffList.length) {
      const coordinates = dropOffList.map(
        dropOff => dropOff?.coordinates?.split(','),
      );

      const newDropOffList = dropOffList.map((dropOff, index) => ({
        coordinates: [
          Number(coordinates[index][0]),
          Number(coordinates[index][1]),
        ],
        name: dropOff.name,
        radius: dropOff?.radius,
      }));
      setApplyForDropOffAreaStore(newDropOffList);
    } else {
      setApplyForDropOffAreaStore([]);
    }
  }, [dropOffList]);

  useEffect(() => {
    if (specificDropOffAreas?.length) {
      setIsCheckDropOffArea(true);
      const coordinates = specificDropOffAreas.map(
        dropOff => dropOff?.coordinates,
      ) as [][];

      const newApplyForDropOffArea = specificDropOffAreas.map(
        (dropOff, index) => ({
          coordinates: `${String(coordinates[index][0])}, ${String(
            coordinates[index][1],
          )}`,
          key: index.toString(),
          name: dropOff.name,
          radius: dropOff.radius,
        }),
      );
      setDropOffList(newApplyForDropOffArea);
    }
  }, [specificDropOffAreas]);

  useEffect(() => {
    if (!isCheckPickUpArea) {
      setPickupList([]);
      setApplyForPickUpAreaStore([]);
      setIsEditingPickupAreaStore(null);
    }

    setIsConfigPickupStore(isCheckPickUpArea);
  }, [isCheckPickUpArea]);

  useEffect(() => {
    if (!isCheckDropOffArea) {
      setDropOffList([]);
      setApplyForDropOffAreaStore([]);
      setIsEditingDropOffAreaStore(null);
    }

    setIsConfigDropOffStore(isCheckDropOffArea);
  }, [isCheckDropOffArea]);

  return (
    <Form
      autoComplete="off"
      initialValues={{ remember: true }}
      layout="vertical"
      name="planInformation"
      size="large"
    >
      <Row gutter={[16, 8]}>
        <Col span={24}>
          <FormInput
            form={form}
            label={t('fee.create.name')}
            name="name"
            placeholder={t('fee.create.namePlaceholder')}
            showRequired
          />
        </Col>
        <Col span={24}>
          <FormInput
            form={form}
            label={t('fee.create.nameFr')}
            name="nameFr"
            placeholder={t('fee.create.namePlaceholder')}
            showRequired
          />
        </Col>
        <Col span={24}>
          <FormInputNumber
            className="w-full"
            form={form}
            label={t('fee.amount')}
            name="amount"
            onKeyDown={evt =>
              ['e', 'E', '+', '-'].includes(evt.key) && evt.preventDefault()
            }
            placeholder={t('fee.create.amountPlaceholder')}
            showRequired
            type="number"
          />
        </Col>
        <Col span={24}>
          <FormServiceSelector
            className="h-10 w-full"
            defaultVehicles={defaultVehicles}
            form={form}
            label={t('fee.vehicleType')}
            mode="multiple"
            name="vehicleTypes"
            placeholder={t('fee.create.vehicleTypePlaceholder')}
            showRequired
            treeDefaultExpandAll
          />
        </Col>
        <Col span={24}>
          <FormEntitySelector
            className="w-full h-10"
            form={form}
            label={t('fee.basedOn')}
            name="perType"
            options={basedOnOptions}
            placeholder={t('fee.create.basedOn.placeholder')}
            showRequired
          />
        </Col>
        <TypographyUI className="ml-2" font="Semibold" type="BodySm">
          {t('fee.create.applyFrom.title')}{' '}
          <span className="text-error-color">*</span>
        </TypographyUI>
        <Col span={24}>
          <FormRadio
            form={form}
            haveShowError={false}
            label={null}
            name="applyFromType"
            onChange={onChangeApplyFromType}
          >
            <Radio value={CreateServiceFeeDto.applyFromType.ALL}>
              {t('fee.create.applyFrom.all')}
            </Radio>
            <Radio value={CreateServiceFeeDto.applyFromType.SPECIFIC_DATE}>
              {t('fee.create.applyFrom.specificDate')}
            </Radio>
          </FormRadio>
        </Col>
        {applyFromType == CreateServiceFeeDto.applyFromType.SPECIFIC_DATE ? (
          <Col span={24}>
            <FormDatePicker
              className="w-full"
              disabledNextDate={true}
              disabledNextTime={true}
              form={form}
              format="DD/MM/YYYY"
              isUtcFormat={true}
              label={null}
              name="applyFrom"
              placeholder={t('fee.create.applyFromPlaceholder')}
              value={
                form.getValues('applyFrom')
                  ? dayjs(form.getValues('applyFrom')).add(1, 'day')
                  : undefined
              }
            />
          </Col>
        ) : null}
        {/* applySpecificArea */}
        <div className="py-2">
          <TypographyUI className="w-full" font="Semibold" type="BodySm">
            {t('fee.applySpecificArea.title')}
          </TypographyUI>
          <TypographyUI className="text-neutral-600" type="Caption1">
            {t('fee.applySpecificArea.description')}
          </TypographyUI>
        </div>
        <Col span={24}>
          <Checkbox
            checked={isCheckPickUpArea}
            className="font-semibold text-base"
            name="checkConfigArea"
            onChange={() => {
              setIsCheckPickUpArea(!isCheckPickUpArea);
              form.setValue('specificPickUpAreas', []);
            }}
          >
            <span className="text-sm">
              {t('coupon.create.advanced.configPickUpArea')}
            </span>
          </Checkbox>
        </Col>
        <Row className="px-4">
          {isCheckPickUpArea ? (
            <SpecificTable
              dataSource={pickupList}
              setDataSource={setPickupList}
              setIsEditingDropOffAreaStore={setIsEditingDropOffAreaStore}
              setIsEditingPickupAreaStore={setIsEditingPickupAreaStore}
              tableName="pickup"
            />
          ) : null}
        </Row>
        {isShowErrorStore &&
        isCheckPickUpArea &&
        applyForPickUpAreaStore?.length == 0 ? (
          <span className="text-sm text-primary-color ml-4 mt-2">
            {t('coupon.atLeast1PickUpPoint')}
          </span>
        ) : null}
        <Col span={24}>
          <Checkbox
            checked={isCheckDropOffArea}
            className="font-semibold text-base"
            name="checkConfigArea"
            onChange={() => {
              setIsCheckDropOffArea(!isCheckDropOffArea);
              form.setValue('specificDropOffAreas', []);
            }}
          >
            <span className="text-sm">
              {t('coupon.create.advanced.configDropOffArea')}
            </span>
          </Checkbox>
        </Col>
        <Row className="px-4">
          {isCheckDropOffArea ? (
            <SpecificTable
              dataSource={dropOffList}
              setDataSource={setDropOffList}
              setIsEditingDropOffAreaStore={setIsEditingDropOffAreaStore}
              setIsEditingPickupAreaStore={setIsEditingPickupAreaStore}
              tableName="dropOff"
            />
          ) : null}
        </Row>
        {isShowErrorStore &&
        isCheckDropOffArea &&
        applyForDropOffAreaStore?.length == 0 ? (
          <span className="text-sm text-primary-color ml-4 mt-2">
            {t('coupon.atLeast1DropOffPoint')}
          </span>
        ) : null}
      </Row>
    </Form>
  );
}
