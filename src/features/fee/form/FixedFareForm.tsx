import { CreateFixedFareDto, type FixedFareEntity } from '#/api/requests';
import { ErrorMessage } from '#/shared/components/formField/ErrorMessage';
import { FormInputNumber } from '#/shared/components/formField/FormInputNumber';
import { FormRadio } from '#/shared/components/formField/FormRadio';
import { FormRangeDatePicker } from '#/shared/components/formField/FormRangeDatePicker';
import { FormTimePicker } from '#/shared/components/formField/FormTimePicker';
import { FormWeekDay } from '#/shared/components/formField/FormWeekDay';
import { FormServiceSelector } from '#/shared/components/selectors/Service/FormServiceSelector';
import { FormZoneSelector } from '#/shared/components/selectors/ZoneSelector/ZoneSelector';
import { TypographyUI } from '#/shared/components/TypographyUI';
import useConfigFareStore from '#/store/useConfigFareStore';
import { InfoCircleOutlined } from '@ant-design/icons';
import type { RadioChangeEvent } from 'antd';
import { Checkbox, Col, Form, Radio, Row, Tooltip } from 'antd';
import dayjs from 'dayjs';
import { InfoCircle } from 'iconsax-react';
import { isUndefined } from 'lodash-es';
import { useEffect, useState } from 'react';
import type { UseFormReturn } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

interface Props {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  form: UseFormReturn<any>;
  isStartTimeEqualsEndTime: boolean;
  applyDateType: CreateFixedFareDto.applyDateType;
  setApplyDateType: (value: CreateFixedFareDto.applyDateType) => void;
  fare: FixedFareEntity | undefined;
}

export function FixedFareForm({
  form,
  isStartTimeEqualsEndTime,
  applyDateType,
  setApplyDateType,
  fare,
}: Props) {
  const { t } = useTranslation();
  const { isShowErrorStore } = useConfigFareStore();
  const [isRoundTrip, setIsRoundTrip] = useState<boolean>(false);
  const [isAllowCoupon, setIsAllowCoupon] = useState<boolean>(false);

  const { startTime, endTime, startApplyDate } = form.getValues();
  const startHour = startTime?.hour;
  const startMinute = startTime?.minute as number;
  const endHour = endTime?.hour;

  const fromZonesIncludeValue = (fare?.fromZones || []).map(zone => ({
    label: zone.name,
    value: zone.zoneId,
  }));

  const toZonesIncludeValue = (fare?.toZones || []).map(zone => ({
    label: zone.name,
    value: zone.zoneId,
  }));

  const onChangeApplyTime = (e: RadioChangeEvent) => {
    const { value } = e.target;
    setApplyDateType(value as CreateFixedFareDto.applyDateType);
    form.setValue('specificDate', []);
    form.setValue('startApplyDate', '');
    form.setValue('endApplyDate', '');
    form.setValue('applyRepeatDay', []);
    form.setValue('startTime', undefined);
    form.setValue('endTime', undefined);
  };

  useEffect(() => {
    setApplyDateType(form.getValues('applyDateType'));
  }, [form.getValues('applyDateType')]);

  useEffect(() => {
    setIsRoundTrip(form.getValues('isRoundTrip'));
  }, [form.getValues('isRoundTrip')]);

  useEffect(() => {
    setIsAllowCoupon(form.getValues('isAllowCoupon'));
  }, [form.getValues('isAllowCoupon')]);

  return (
    <Form
      autoComplete="off"
      initialValues={{ remember: true }}
      layout="vertical"
      name="planInformation"
      size="large"
    >
      <Row gutter={[16, 8]}>
        <Col span={24}>
          <FormZoneSelector
            className="w-full h-10"
            form={form}
            includeValue={fromZonesIncludeValue}
            label={t('common.from')}
            maxTagCount="responsive"
            mode="multiple"
            name="fromZones"
            placeholder={t('fixedFare.zoneSelects')}
            showRequired
            size="large"
          />
        </Col>
        <Col span={24}>
          <FormZoneSelector
            className="w-full h-10"
            form={form}
            includeValue={toZonesIncludeValue}
            label={t('common.to')}
            maxTagCount="responsive"
            mode="multiple"
            name="toZones"
            placeholder={t('fixedFare.zoneSelects')}
            showRequired
            size="large"
          />
        </Col>
        <Col span={24}>
          <Checkbox
            checked={isRoundTrip}
            onChange={() => {
              setIsRoundTrip(!isRoundTrip);
              form.setValue('isRoundTrip', !isRoundTrip);
            }}
          />
          <TypographyUI className="ml-2" font="Semibold" type="BodySm">
            {t('fixedFare.roundTrip')}
          </TypographyUI>
          <Tooltip
            styles={{
              body: {
                backgroundColor: '#fff',
                color: '#34383D',
                fontSize: '12px',
                padding: '4px 0 0 4px',
              },
            }}
            title={t('fixedFare.roundTripTooltip')}
          >
            <InfoCircleOutlined className="ml-1" />
          </Tooltip>
        </Col>
        <Col span={24}>
          <Checkbox
            checked={isAllowCoupon}
            onChange={() => {
              setIsAllowCoupon(!isAllowCoupon);
              form.setValue('isAllowCoupon', !isAllowCoupon);
            }}
          />
          <TypographyUI className="ml-2" font="Semibold" type="BodySm">
            {t('fixedFare.couponsAllowed')}
          </TypographyUI>
        </Col>
        <Col span={24}>
          <FormInputNumber
            className="w-full"
            form={form}
            label={
              <div className="flex text-neutral-2 font-semibold text-sm mb-2">
                {t('bookingHistory.detail.receipt.amountCharged')}
                <Tooltip
                  className="mx-1"
                  styles={{
                    body: {
                      backgroundColor: '#fff',
                      color: '#34383D',
                      fontSize: '12px',
                      fontWeight: 500,
                      padding: '8px 0 0 8px',
                      width: '480px',
                    },
                  }}
                  title={
                    <div className="text-xs">
                      {t('fixedFare.amountChargedTooltip')}
                    </div>
                  }
                >
                  <InfoCircle className="rotate-180" size={16} />
                </Tooltip>
                <div className="text-error-color">*</div>
              </div>
            }
            name="amount"
            onKeyDown={evt =>
              ['e', 'E', '+', '-'].includes(evt.key) && evt.preventDefault()
            }
            placeholder={t('fixedFare.fixedFarePlaceholder')}
            showRequired
            type="number"
          />
        </Col>
        <Col span={24}>
          <FormServiceSelector
            className="h-10 w-full"
            defaultVehicles={fare?.vehicleTypes}
            form={form}
            label={t('common.service')}
            mode="multiple"
            name="vehicleTypes"
            placeholder={t('fee.create.vehicleTypePlaceholder')}
            showRequired
            treeDefaultExpandAll
          />
        </Col>
        <TypographyUI className="ml-2" font="Semibold" type="BodySm">
          {t('rate.create.applyTime.title')}{' '}
          <span className="text-error-color">*</span>
        </TypographyUI>
        <Col span={24}>
          <FormRadio
            form={form}
            haveShowError={false}
            label={null}
            name="applyDateType"
            onChange={onChangeApplyTime}
          >
            <Radio value={CreateFixedFareDto.applyDateType.ALL}>
              {t('rate.create.applyTime.all')}
            </Radio>
            <Radio value={CreateFixedFareDto.applyDateType.SPECIFIC_DATE}>
              {t('rate.create.applyTime.specificDate')}
            </Radio>
            <Radio value={CreateFixedFareDto.applyDateType.DAY}>
              {t('rate.create.applyTime.days')}
            </Radio>
          </FormRadio>
        </Col>
        {applyDateType === CreateFixedFareDto.applyDateType.SPECIFIC_DATE && (
          <Col span={24}>
            <FormRangeDatePicker
              className="lg:mt-2 w-full h-10"
              defaultValue={[
                form.watch('startApplyDate')
                  ? dayjs(form.watch('startApplyDate')).utc()
                  : null,
                form.watch('endApplyDate')
                  ? dayjs(form.watch('endApplyDate')).utc()
                  : null,
              ]}
              form={form}
              isDisabledDate={true}
              label={null}
              name="specificDate"
              onCalendarChange={values => {
                if (values?.[0] && values[1]) {
                  form.setValue(
                    'startApplyDate',
                    values[0].format('YYYY-MM-DD'),
                  );
                  form.setValue('endApplyDate', values[1].format('YYYY-MM-DD'));
                }
              }}
              placeholder={[
                t('rate.create.applyTime.startDate'),
                t('rate.create.applyTime.endDate'),
              ]}
            />
            {isShowErrorStore &&
            !startApplyDate &&
            !form.getValues('startApplyDate') ? (
              <ErrorMessage>{t('common.validate.requiredField')}</ErrorMessage>
            ) : null}
          </Col>
        )}
        {applyDateType === CreateFixedFareDto.applyDateType.DAY && (
          <Col span={24}>
            <FormWeekDay form={form} label={null} name="applyRepeatDay" />
          </Col>
        )}
        {(applyDateType === CreateFixedFareDto.applyDateType.DAY ||
          applyDateType === CreateFixedFareDto.applyDateType.SPECIFIC_DATE) && (
          <>
            <Col span={12}>
              <FormTimePicker
                className="w-full h-10"
                form={form}
                format="HH:mm"
                label={null}
                name="startTime"
                picker="time"
                placeholder={t('rate.create.applyTime.startTime')}
              />
              {isShowErrorStore &&
              isUndefined(startHour) &&
              isUndefined(form.getValues('startTime.hour')) ? (
                <ErrorMessage>
                  {t('common.validate.requiredField')}
                </ErrorMessage>
              ) : null}
            </Col>
            <Col span={12}>
              <FormTimePicker
                className="w-full h-10"
                form={form}
                format="HH:mm"
                isDisabledTime={
                  form.getValues('startApplyDate') &&
                  form.getValues('startApplyDate') ===
                    form.getValues('endApplyDate')
                }
                label={null}
                name="endTime"
                picker="time"
                placeholder={t('rate.create.applyTime.endTime')}
                startHour={startHour}
                startMinute={startMinute}
              />
              {isShowErrorStore &&
              isUndefined(endHour) &&
              isUndefined(form.getValues('endTime.hour')) ? (
                <ErrorMessage>
                  {t('common.validate.requiredField')}
                </ErrorMessage>
              ) : null}
            </Col>

            {isStartTimeEqualsEndTime ? (
              <Col span={24}>
                <ErrorMessage>
                  {t('fixedFare.startTimeEqualsEndTime')}
                </ErrorMessage>
              </Col>
            ) : null}
          </>
        )}
      </Row>
      <TypographyUI className="text-yellow-400 mt-4 text-xs">
        {applyDateType === CreateFixedFareDto.applyDateType.ALL
          ? t('rate.pricingRuleNote')
          : t('rate.pricingRuleNoteOfDays')}
      </TypographyUI>
    </Form>
  );
}
