import { CreateRateFeeDto } from '#/api/requests';
import { FormInputNumber } from '#/shared/components/formField/FormInputNumber';
import { FormRadio } from '#/shared/components/formField/FormRadio';
import { FormServiceSelector } from '#/shared/components/selectors/Service/FormServiceSelector';
import { TypographyUI } from '#/shared/components/TypographyUI';
import type { RadioChangeEvent } from 'antd';
import { Checkbox, Col, Form, Radio, Row } from 'antd';
import { type UseFormReturn } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { FormInput } from '../../../shared/components/formField/FormInput';
import type { CreateRateFeeDtoCustom } from '../hooks/useCreateRateFee';
import { isUndefined } from 'lodash-es';
import dayjs from 'dayjs';
import { FormWeekDay } from '#/shared/components/formField/FormWeekDay';
import { useEffect, useState } from 'react';
import DateRangePicker from '#/shared/components/DateRangePicker';
import { FormRangeDatePicker } from '#/shared/components/formField/FormRangeDatePicker';
import { ErrorMessage } from '#/shared/components/formField/ErrorMessage';
import useServiceFeeStore from '#/store/useConfigRateStore';
import type { DataType } from '../components/SpecificTable';
import SpecificTable from '../components/SpecificTable';

interface Props {
  form: UseFormReturn<CreateRateFeeDtoCustom>;
  defaultVehicles?: string[];
}

export function RateFeeForm({ form, defaultVehicles }: Props) {
  const { t } = useTranslation();
  const [isCheckPickUpArea, setIsCheckPickUpArea] = useState<boolean>(false);
  const [isCheckDropOffArea, setIsCheckDropOffArea] = useState<boolean>(false);

  const [pickupList, setPickupList] = useState<DataType[]>([]);
  const [dropOffList, setDropOffList] = useState<DataType[]>([]);

  const {
    isShowErrorStore,
    setApplyDateTypeStore,
    applyDateTypeStore,
    applyForDropOffAreaStore,
    applyForPickUpAreaStore,
    setApplyForPickUpAreaStore,
    setApplyForDropOffAreaStore,
    setIsEditingPickupAreaStore,
    setIsEditingDropOffAreaStore,
    setIsConfigPickupStore,
    setIsConfigDropOffStore,
  } = useServiceFeeStore();

  const {
    startTime,
    endTime,
    startApplyDate,
    endApplyDate,
    specificPickUpAreas,
    specificDropOffAreas,
  } = form.getValues();

  const startHour = startTime?.hour;
  const startMinute = startTime?.minute as number;
  const endHour = endTime?.hour;
  const endMinute = endTime?.minute as number;

  const onChangeApplyTime = (e: RadioChangeEvent) => {
    const { value } = e.target;
    setApplyDateTypeStore(value as CreateRateFeeDto.applyDateType);

    form.setValue('startApplyDate', '');
    form.setValue('endApplyDate', '');
    form.setValue('applyRepeatDay', []);

    if (value === CreateRateFeeDto.applyDateType.ALL) {
      form.setValue('startTime.hour', undefined);
      form.setValue('startTime.minute', undefined);
      form.setValue('endTime.hour', undefined);
      form.setValue('endTime.minute', undefined);
    }
  };

  useEffect(() => {
    setApplyDateTypeStore(form.getValues('applyDateType'));
  }, []);

  useEffect(() => {
    if (pickupList.length) {
      const coordinates = pickupList.map(
        pickup => pickup?.coordinates?.split(','),
      );

      const newPickupList = pickupList.map((pickup, index) => ({
        coordinates: [
          Number(coordinates[index][0]),
          Number(coordinates[index][1]),
        ],
        name: pickup.name,
        radius: pickup?.radius,
      }));
      setApplyForPickUpAreaStore(newPickupList);
    } else {
      setApplyForPickUpAreaStore([]);
    }
  }, [pickupList]);

  useEffect(() => {
    if (specificPickUpAreas?.length) {
      setIsCheckPickUpArea(true);
      const coordinates = specificPickUpAreas.map(
        pickup => pickup?.coordinates,
      ) as [][];

      const newApplyForPickUpArea = specificPickUpAreas.map(
        (pickup, index) => ({
          coordinates: `${String(coordinates[index][0])}, ${String(
            coordinates[index][1],
          )}`,
          key: index.toString(),
          name: pickup.name,
          radius: pickup.radius,
        }),
      );
      setPickupList(newApplyForPickUpArea);
    }
  }, [specificPickUpAreas]);

  useEffect(() => {
    if (dropOffList.length) {
      const coordinates = dropOffList.map(
        dropOff => dropOff?.coordinates?.split(','),
      );

      const newDropOffList = dropOffList.map((dropOff, index) => ({
        coordinates: [
          Number(coordinates[index][0]),
          Number(coordinates[index][1]),
        ],
        name: dropOff.name,
        radius: dropOff?.radius,
      }));
      setApplyForDropOffAreaStore(newDropOffList);
    } else {
      setApplyForDropOffAreaStore([]);
    }
  }, [dropOffList]);

  useEffect(() => {
    if (specificDropOffAreas?.length) {
      setIsCheckDropOffArea(true);
      const coordinates = specificDropOffAreas.map(
        dropOff => dropOff?.coordinates,
      ) as [][];

      const newApplyForDropOffArea = specificDropOffAreas.map(
        (dropOff, index) => ({
          coordinates: `${String(coordinates[index][0])}, ${String(
            coordinates[index][1],
          )}`,
          key: index.toString(),
          name: dropOff.name,
          radius: dropOff.radius,
        }),
      );
      setDropOffList(newApplyForDropOffArea);
    }
  }, [specificDropOffAreas]);

  useEffect(() => {
    if (!isCheckPickUpArea) {
      setPickupList([]);
      setApplyForPickUpAreaStore([]);
      setIsEditingPickupAreaStore(null);
    }

    setIsConfigPickupStore(isCheckPickUpArea);
  }, [isCheckPickUpArea]);

  useEffect(() => {
    if (!isCheckDropOffArea) {
      setDropOffList([]);
      setApplyForDropOffAreaStore([]);
      setIsEditingDropOffAreaStore(null);
    }

    setIsConfigDropOffStore(isCheckDropOffArea);
  }, [isCheckDropOffArea]);

  return (
    <Form
      autoComplete="off"
      initialValues={{ remember: true }}
      layout="vertical"
      name="planInformation"
      size="large"
    >
      <Row gutter={[16, 8]}>
        <Col span={24}>
          <FormInput
            form={form}
            label={t('fee.create.name')}
            name="name"
            placeholder={t('rate.create.namePlaceholder')}
            showRequired
          />
        </Col>
        <Col span={24}>
          <FormInput
            form={form}
            label={t('fee.create.nameFr')}
            name="nameFr"
            placeholder={t('rate.create.namePlaceholder')}
            showRequired
          />
        </Col>
        <Col span={24}>
          <FormInputNumber
            className="w-full"
            form={form}
            label={t('rate.create.kmRate')}
            name="kmRate"
            onKeyDown={evt =>
              ['e', 'E', '+', '-'].includes(evt.key) && evt.preventDefault()
            }
            placeholder={t('rate.create.kmRatePlaceholder')}
            showRequired
            type="number"
          />
        </Col>
        <Col span={24}>
          <FormServiceSelector
            className="h-10 w-full"
            defaultVehicles={defaultVehicles}
            form={form}
            label={t('coupon.create.vehicleTypes.title')}
            mode="multiple"
            name="vehicleTypes"
            placeholder={t('fee.create.vehicleTypePlaceholder')}
            showRequired
            treeDefaultExpandAll
          />
        </Col>

        <TypographyUI className="ml-2" font="Semibold" type="BodySm">
          {t('rate.create.applyTime.title')}{' '}
          <span className="text-error-color">*</span>
        </TypographyUI>
        <Col span={24}>
          <FormRadio
            form={form}
            haveShowError={false}
            label={null}
            name="applyDateType"
            onChange={onChangeApplyTime}
          >
            <Radio value={CreateRateFeeDto.applyDateType.ALL}>
              {t('rate.create.applyTime.all')}
            </Radio>
            <Radio value={CreateRateFeeDto.applyDateType.SPECIFIC_DATE}>
              {t('rate.create.applyTime.specificDate')}
            </Radio>
            <Radio value={CreateRateFeeDto.applyDateType.DAY}>
              {t('rate.create.applyTime.days')}
            </Radio>
          </FormRadio>
        </Col>
        {applyDateTypeStore ===
          CreateRateFeeDto.applyDateType.SPECIFIC_DATE && (
          <Col span={24}>
            <FormRangeDatePicker
              className="lg:mt-2 w-full h-10"
              defaultValue={[
                startApplyDate ? dayjs(startApplyDate).utc() : null,
                endApplyDate ? dayjs(endApplyDate).utc() : null,
              ]}
              form={form}
              isDisabledDate={true}
              label={null}
              name="specificDate"
              onCalendarChange={values => {
                if (values?.[0] && values[1]) {
                  form.setValue(
                    'startApplyDate',
                    values[0].format('YYYY-MM-DD'),
                  );
                  form.setValue('endApplyDate', values[1].format('YYYY-MM-DD'));
                }
              }}
              placeholder={[
                t('rate.create.applyTime.startDate'),
                t('rate.create.applyTime.endDate'),
              ]}
            />
            {isShowErrorStore &&
            !startApplyDate &&
            !form.getValues('startApplyDate') ? (
              <ErrorMessage>{t('common.validate.requiredField')}</ErrorMessage>
            ) : null}
          </Col>
        )}
        {applyDateTypeStore === CreateRateFeeDto.applyDateType.DAY && (
          <FormWeekDay form={form} label={null} name="applyRepeatDay" />
        )}
        {(applyDateTypeStore === CreateRateFeeDto.applyDateType.DAY ||
          applyDateTypeStore ===
            CreateRateFeeDto.applyDateType.SPECIFIC_DATE) && (
          <Col span={24}>
            <DateRangePicker
              allowClear={false}
              className="w-full h-10"
              defaultValue={[
                !isUndefined(startHour)
                  ? dayjs().hour(startHour).minute(startMinute)
                  : null,
                !isUndefined(endHour)
                  ? dayjs().hour(endHour).minute(endMinute)
                  : null,
              ]}
              format="HH:mm"
              onChange={values => {
                if (values?.[0] && values[1]) {
                  form.setValue('startTime', {
                    hour: values[0].hour(),
                    minute: values[0].minute(),
                  });
                  form.setValue('endTime', {
                    hour: values[1].hour(),
                    minute: values[1].minute(),
                  });
                }
              }}
              picker="time"
            />
            {isShowErrorStore &&
            isUndefined(startHour) &&
            isUndefined(form.getValues('startTime.hour')) ? (
              <ErrorMessage>{t('common.validate.requiredField')}</ErrorMessage>
            ) : null}
          </Col>
        )}
      </Row>
      <TypographyUI className="text-yellow-400 mt-4 text-xs">
        {t('rate.pricingRuleNote')}
      </TypographyUI>
      {/* applySpecificArea */}
      <div className="py-4">
        <TypographyUI className="w-full" font="Semibold" type="BodySm">
          {t('fee.applySpecificArea.title')}
        </TypographyUI>
        <TypographyUI className="text-neutral-600" type="Caption1">
          {t('fee.applySpecificArea.description')}
        </TypographyUI>
      </div>
      <Col span={24}>
        <Checkbox
          checked={isCheckPickUpArea}
          className="font-semibold text-base"
          name="checkConfigArea"
          onChange={() => {
            setIsCheckPickUpArea(!isCheckPickUpArea);
            form.setValue('specificPickUpAreas', []);
          }}
        >
          <span className="text-sm">
            {t('coupon.create.advanced.configPickUpArea')}
          </span>
        </Checkbox>
      </Col>
      <Row className="px-4">
        {isCheckPickUpArea ? (
          <SpecificTable
            dataSource={pickupList}
            setDataSource={setPickupList}
            setIsEditingDropOffAreaStore={setIsEditingDropOffAreaStore}
            setIsEditingPickupAreaStore={setIsEditingPickupAreaStore}
            tableName="pickup"
          />
        ) : null}
      </Row>
      {isShowErrorStore &&
      isCheckPickUpArea &&
      applyForPickUpAreaStore?.length == 0 ? (
        <span className="text-sm text-primary-color ml-4 mt-2">
          {t('coupon.atLeast1PickUpPoint')}
        </span>
      ) : null}
      <Col span={24}>
        <Checkbox
          checked={isCheckDropOffArea}
          className="font-semibold text-base mt-4"
          name="checkConfigArea"
          onChange={() => {
            setIsCheckDropOffArea(!isCheckDropOffArea);
            form.setValue('specificDropOffAreas', []);
          }}
        >
          <span className="text-sm">
            {t('coupon.create.advanced.configDropOffArea')}
          </span>
        </Checkbox>
      </Col>
      <Row className="px-4">
        {isCheckDropOffArea ? (
          <SpecificTable
            dataSource={dropOffList}
            setDataSource={setDropOffList}
            setIsEditingDropOffAreaStore={setIsEditingDropOffAreaStore}
            setIsEditingPickupAreaStore={setIsEditingPickupAreaStore}
            tableName="dropOff"
          />
        ) : null}
      </Row>
      {isShowErrorStore &&
      isCheckDropOffArea &&
      applyForDropOffAreaStore?.length == 0 ? (
        <span className="text-sm text-primary-color ml-4 mt-2">
          {t('coupon.atLeast1DropOffPoint')}
        </span>
      ) : null}
    </Form>
  );
}
