import { useTranslation } from 'react-i18next';
import { ModalUI } from '#/shared/components/modalUI/ModalUI';
import useCreateRateFee from '../hooks/useCreateRateFee';
import { RateFeeForm } from '../form/RateFeeForm';
import type { SpecificTimeType } from '#/api/requests';
import useServiceFeeStore from '#/store/useConfigRateStore';

interface Props {
  visible: boolean;
  onCancel: () => void;
  onSuccess?: () => void;
}

export function CreateRateFeeModal({ visible, onCancel, onSuccess }: Props) {
  const { t } = useTranslation();
  const { setIsShowErrorStore } = useServiceFeeStore();

  const { form, onCreateRateFee } = useCreateRateFee({
    onCancel,
    onSuccess,
  });

  const onHandleCancel = () => {
    onCancel();
    form.reset();
    form.setValue('applyRepeatDay', []);
    form.setValue('startApplyDate', '');
    form.setValue('endApplyDate', '');
    form.setValue('startTime', undefined as unknown as SpecificTimeType);
    form.setValue('endTime', undefined as unknown as SpecificTimeType);
    setIsShowErrorStore(false);
  };

  return (
    <ModalUI
      className="w-[992px]"
      closable={false}
      maskClosable={false}
      okButtonProps={{
        loading: false,
      }}
      onCancel={onHandleCancel}
      onOk={onCreateRateFee}
      open={visible}
      title={t('rate.create.new')}
    >
      <RateFeeForm form={form} />
    </ModalUI>
  );
}
