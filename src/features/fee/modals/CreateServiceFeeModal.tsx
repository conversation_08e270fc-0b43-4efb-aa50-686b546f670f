import { useTranslation } from 'react-i18next';
import { ModalUI } from '#/shared/components/modalUI/ModalUI';
import { FeeForm } from '../form/ServiceFeeForm';
import useCreateServiceFee from '../hooks/useCreateServiceFee';

interface Props {
  visible: boolean;
  onCancel: () => void;
  onSuccess?: () => void;
}

export function CreateServiceFeeModal({ visible, onCancel, onSuccess }: Props) {
  const { t } = useTranslation();
  const { form, onCreateFee } = useCreateServiceFee({
    onCloseModal: onCancel,
    onSuccess,
  });

  const onHandleCancel = () => {
    onCancel();
    form.reset();
  };

  return (
    <ModalUI
      className="w-[992px]"
      closable={false}
      maskClosable={false}
      okButtonProps={{
        loading: false,
      }}
      onCancel={onHandleCancel}
      onOk={onCreateFee}
      open={visible}
      title={t('fee.create.new')}
    >
      <FeeForm form={form} />
    </ModalUI>
  );
}
