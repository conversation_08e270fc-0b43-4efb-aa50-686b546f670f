import { ConfirmModalUI } from '#/shared/components/modalUI/ConfirmModalUI';
import { useTranslation } from 'react-i18next';
import { useDeleteFixedFare } from '../hooks/useDeleteFixedFare';

interface Props {
  visible: boolean;
  onCancel: () => void;
  id: string;
  onSuccess?: () => void;
}

export function DeleteFixedFareModal({
  id,
  visible,
  onCancel,
  onSuccess,
}: Props) {
  const { t } = useTranslation();

  const { isLoading, onDelete } = useDeleteFixedFare({
    id,
    onCancel() {
      onCancel();
    },
    onSuccess: () => {
      onCancel();
      onSuccess?.();
    },
  });

  return (
    <ConfirmModalUI
      closeIcon={false}
      desc={t('fixedFare.delete.desc')}
      okButtonProps={{
        loading: isLoading,
      }}
      okText={t('common.button.delete')}
      onCancel={onCancel}
      onOk={onDelete}
      open={visible}
      title={t('fixedFare.delete.title')}
    />
  );
}
