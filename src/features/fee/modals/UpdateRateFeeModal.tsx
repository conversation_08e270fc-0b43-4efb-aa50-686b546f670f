import type { RateFeeEntity } from '#/api/requests';
import { ModalUI } from '#/shared/components/modalUI/ModalUI';
import useServiceFeeStore from '#/store/useConfigRateStore';
import { useTranslation } from 'react-i18next';
import { RateFeeForm } from '../form/RateFeeForm';
import useUpdateRateFee from '../hooks/useUpdateRateFee';

interface Props {
  selectedFee: RateFeeEntity | undefined;
  visible: boolean;
  onCancel: () => void;
  onSuccess?: () => void;
}

export function UpdateRateFeeModal({
  selectedFee,
  visible,
  onCancel,
  onSuccess,
}: Props) {
  const { t } = useTranslation();
  const { setIsShowErrorStore } = useServiceFeeStore();

  const { form, onUpdateFee, isPending } = useUpdateRateFee({
    onCloseModal: onCancel,
    onSuccess,
    selectedFee,
  });

  const onHandleCancel = () => {
    onCancel();
    setIsShowErrorStore(false);
  };

  return (
    <ModalUI
      className="w-[992px]"
      closable={false}
      maskClosable={false}
      okButtonProps={{
        loading: isPending,
      }}
      okText={t('common.button.save')}
      onCancel={onHandleCancel}
      onOk={onUpdateFee}
      open={visible}
      title={t('rate.update.title')}
    >
      <RateFeeForm defaultVehicles={selectedFee?.vehicleTypes} form={form} />
    </ModalUI>
  );
}
