import type { ServiceFeeEntity } from '#/api/requests';
import { ModalUI } from '#/shared/components/modalUI/ModalUI';
import { useTranslation } from 'react-i18next';
import { FeeForm } from '../form/ServiceFeeForm';
import useUpdateServiceFee from '../hooks/useUpdateServiceFee';

interface Props {
  selectedFee: ServiceFeeEntity | undefined;
  visible: boolean;
  onCancel: () => void;
  onSuccess?: () => void;
}

export function UpdateServiceFeeModal({
  selectedFee,
  visible,
  onCancel,
  onSuccess,
}: Props) {
  const { t } = useTranslation();
  const { form, onUpdateFee, isPending, setIsShowErrorStore } =
    useUpdateServiceFee({
      onCloseModal: onCancel,
      onSuccess,
      selectedFee,
    });

  const onHandleCancel = () => {
    onCancel();
    setIsShowErrorStore(false);
  };

  return (
    <ModalUI
      className="w-[992px]"
      closable={false}
      maskClosable={false}
      okButtonProps={{
        loading: isPending,
      }}
      okText={t('common.button.save')}
      onCancel={onHandleCancel}
      onOk={onUpdateFee}
      open={visible}
      title={t('fee.update.title')}
    >
      <FeeForm defaultVehicles={selectedFee?.vehicleTypes} form={form} />
    </ModalUI>
  );
}
