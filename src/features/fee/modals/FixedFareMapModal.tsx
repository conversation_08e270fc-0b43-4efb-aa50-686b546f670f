import { useZoneServiceZonePrivateControllerGetZoneListByIds } from '#/api/loyalhub/queries';
import type { ZoneResponse } from '#/api/loyalhub/requests';
import { ModalUI } from '#/shared/components/modalUI/ModalUI';
import { DEFAULT_POSITION } from '#/shared/constants';
import useGlobalStore from '#/store/useGlobalStore';
import { GoogleMap, Marker, Polygon } from '@react-google-maps/api';
import { Spin } from 'antd';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import './styles.scss';
import type { SimpleZoneEntity } from '#/api/requests';

interface Props {
  onHandleCloseMap: () => void;
  fromZones: SimpleZoneEntity[];
  toZones: SimpleZoneEntity[];
}

interface PositionItem {
  lat: number;
  lng: number;
}

const containerStyle = {
  height: '600px',
};

const optionsPolyline = {
  clickable: false,
  draggable: false,
  editable: false,
  fillColor: '#085daa',
  fillOpacity: 0.15,
  radius: 100,
  strokeColor: '#2C2C2B',
  strokeOpacity: 0.8,
  strokeWeight: 2,
  visible: true,
  zIndex: 1,
};

const zoomDefault = 11;

const calculateCentroid = (coords: PositionItem[]) => {
  let latSum = 0,
    lngSum = 0;
  coords?.forEach(coord => {
    latSum += coord.lat;
    lngSum += coord.lng;
  });
  return { lat: latSum / coords?.length, lng: lngSum / coords?.length };
};

const convertCoordinates = (rawCoordinates: number[][]) =>
  rawCoordinates?.map(([lng, lat]) => ({
    lat,
    lng,
  }));

interface ZoneNameProps {
  name: string;
  position: number[][];
}

function ZoneName({ name, position }: ZoneNameProps) {
  return (
    <Marker
      icon={{
        path: google.maps.SymbolPath.CIRCLE,
        scale: 0, // Hide default marker icon
      }}
      label={{
        color: '#2C2C2B',
        fontSize: '14px',
        fontWeight: 'bold',
        text: name,
      }}
      position={calculateCentroid(convertCoordinates(position))}
    />
  );
}

function FixedFareMapModal({ onHandleCloseMap, fromZones, toZones }: Props) {
  const { t } = useTranslation();
  const { isLoadedMap } = useGlobalStore();

  const [pickUpList, setPickUpList] = useState<ZoneResponse[]>([]);
  const [dropOffList, setDropOffList] = useState<ZoneResponse[]>([]);

  const { data } = useZoneServiceZonePrivateControllerGetZoneListByIds({
    ids: fromZones?.map(item => item._id).join(','),
  });

  const { data: dropOffData } =
    useZoneServiceZonePrivateControllerGetZoneListByIds({
      ids: toZones?.map(item => item._id).join(','),
    });

  useEffect(() => {
    setPickUpList(data?.data as ZoneResponse[]);
  }, [data]);

  useEffect(() => {
    setDropOffList(dropOffData?.data as ZoneResponse[]);
  }, [dropOffData]);

  return (
    <ModalUI
      className="fixed-fare-map xxl:w-[70%] w-[60%]"
      footer={null}
      onCancel={onHandleCloseMap}
      open
      title={t('fixedFare.viewOnMap')}
    >
      {!isLoadedMap ? (
        <Spin spinning={true} />
      ) : (
        <div className="text-center" style={{ position: 'relative' }}>
          {pickUpList?.length > 0 ? (
            <GoogleMap
              center={calculateCentroid(
                convertCoordinates(pickUpList?.[0]?.points?.coordinates[0]),
              )}
              mapContainerStyle={containerStyle}
              zoom={zoomDefault}
            >
              {pickUpList?.map((item: ZoneResponse) => (
                <div key={item.id}>
                  <Polygon
                    options={optionsPolyline}
                    path={convertCoordinates(item?.points?.coordinates[0])}
                  />
                  <ZoneName
                    name={item.name}
                    position={item?.points?.coordinates[0]}
                  />
                </div>
              ))}
              {dropOffList?.map((item: ZoneResponse) => (
                <div key={item.id}>
                  <Polygon
                    options={{
                      ...optionsPolyline,
                      fillColor: '#FE353D',
                    }}
                    path={convertCoordinates(item?.points?.coordinates[0])}
                  />
                  <ZoneName
                    name={item.name}
                    position={item?.points?.coordinates[0]}
                  />
                </div>
              ))}
            </GoogleMap>
          ) : (
            <GoogleMap
              center={DEFAULT_POSITION}
              mapContainerStyle={containerStyle}
              zoom={zoomDefault}
            />
          )}
          <div className="flex items-center gap-2 rounded-md bg-white bottom-4 xxl:bottom-16 left-6 px-2 absolute font-bold cursor-pointer shadow-md text-black w-28 h-[42px]">
            <div className="w-3 h-3 rounded-sm border border-solid border-w-[1px] border-neutral-850 bg-[#2C2C2B80]" />
            {t('common.from')}
            <div className="w-3 h-3 rounded-sm border border-solid border-w-[1px] border-neutral-850 bg-[#FE353D4D]" />
            {t('common.to')}
          </div>
        </div>
      )}
    </ModalUI>
  );
}

export default React.memo(FixedFareMapModal);
