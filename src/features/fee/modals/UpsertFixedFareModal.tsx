import type { FixedFareEntity, SpecificTimeType } from '#/api/requests';
import { ModalUI } from '#/shared/components/modalUI/ModalUI';
import { useTranslation } from 'react-i18next';
import { FixedFareForm } from '../form/FixedFareForm';
import { useUpsertFixedFare } from '../hooks/useUpsertFixedFare';
import useConfigFareStore from '#/store/useConfigFareStore';

interface Props {
  visible: boolean;
  onCancel: () => void;
  fareId?: string;
  type: 'list' | 'detail';
  fare?: FixedFareEntity | undefined;
}

export function UpsertFixedFareModal({
  visible,
  onCancel,
  fareId,
  type,
  fare,
}: Props) {
  const { t } = useTranslation();
  const { setIsShowErrorStore } = useConfigFareStore();
  const {
    form,
    handleSubmit,
    isStartTimeEqualsEndTime,
    setApplyDateType,
    applyDateType,
    isLoadingModal,
  } = useUpsertFixedFare({
    fareId,
    onClose: onCancel,
    type,
  });

  const onHandleCancel = () => {
    onCancel();
    form.reset();
    form.setValue('applyRepeatDay', []);
    form.setValue('startApplyDate', '');
    form.setValue('endApplyDate', '');
    form.setValue('startTime', undefined as unknown as SpecificTimeType);
    form.setValue('endTime', undefined as unknown as SpecificTimeType);
    setIsShowErrorStore(false);
  };

  return (
    <ModalUI
      className="w-[768px]"
      closable={false}
      loading={isLoadingModal}
      maskClosable={false}
      okButtonProps={{
        loading: false,
      }}
      okText={fareId ? t('common.button.save') : t('common.button.create')}
      onCancel={onHandleCancel}
      onOk={() => {
        form.handleSubmit(handleSubmit)();
      }}
      open={visible}
      title={
        fareId
          ? t('fixedFare.editFixedFare')
          : t('fixedFare.createNewFixedFare')
      }
    >
      <FixedFareForm
        applyDateType={applyDateType}
        fare={fare}
        form={form}
        isStartTimeEqualsEndTime={isStartTimeEqualsEndTime()}
        setApplyDateType={setApplyDateType}
      />
    </ModalUI>
  );
}
