import BaseButton from '#/shared/components/Button/BaseButton';
import { Filter<PERSON>rapperUI } from '#/shared/components/FilterWrapperUI';
import { FormInput } from '#/shared/components/formField/FormInput';
import { ServiceSelector } from '#/shared/components/selectors/Service/ServiceSelector';
import { TypographyUI } from '#/shared/components/TypographyUI';
import { Button } from 'antd';
import { useTranslation } from 'react-i18next';
import useServiceFeeList from '../hooks/useServiceFeeList';
import { Add } from 'iconsax-react';
import { FormEntitySelector } from '#/shared/components/selectors/FormEntitySelector';
import { TableUI } from '#/shared/components/tableUI/TableUI';
import { CreateServiceFeeModal } from '../modals/CreateServiceFeeModal';
import { useState } from 'react';
import { UpdateServiceFeeModal } from '../modals/UpdateServiceFeeModal';
import { CreateServiceFeeDto, ServiceFeeEntity } from '#/api/requests';
import { DeleteServiceFeeModal } from '../modals/DeleteServiceFeeModal';
import { hasPermission } from '#/shared/utils';
import useAuthStore from '#/store/useAuthStore';
import { Permission } from '#/shared/constants';
import { FormEmployeeSelector } from '#/shared/components/selectors/Employee/EmployeeSelector';

export default function ServiceFee() {
  const { t } = useTranslation();
  const { user } = useAuthStore();
  const { permissions } = user;
  const {
    columns,
    createdById,
    current,
    feeList,
    form,
    idDelete,
    isLoading,
    limit,
    meta,
    onTableChange,
    q,
    refetch,
    serviceFeeSelected,
    setIdDelete,
    setServiceFeeSelected,
    status,
    vehicleType,
    onViewDetail,
  } = useServiceFeeList();

  const hasCreateFee = hasPermission(permissions, Permission.CREATE_CONFIG_FEE);

  const showClearFilter =
    q ||
    vehicleType ||
    status ||
    createdById?.length > 0 ||
    form.watch('perType');
  const [isShowCreateModal, setIsShowCreateModal] = useState(false);

  const onClearFilter = () => {
    form.reset();
  };

  const onCancelCreateFee = () => {
    setIsShowCreateModal(false);
  };

  const onHandleChangeVehicleType = (value: string) => {
    form.setValue('vehicleType', value);
  };

  const onCancelUpdateFee = () => {
    setServiceFeeSelected(undefined);
  };

  return (
    <>
      <FilterWrapperUI
        addOnActions={
          <BaseButton
            className={`${hasCreateFee ? 'flex mt-2' : 'hidden'}`}
            content={t('fee.newFee')}
            icon={<Add size={20} />}
            onClick={() => setIsShowCreateModal(true)}
            type="primary"
          />
        }
        filters={
          <div className="flex flex-wrap space-x-2 space-y-2">
            <FormInput
              className="w-60"
              form={form}
              label={null}
              name="q"
              onKeyDown={(e: React.KeyboardEvent<HTMLInputElement>) => {
                if (e.key === ' ' && q.length === 0) {
                  e.preventDefault();
                }
              }}
              placeholder={t('fee.search')}
              size="middle"
            />

            <ServiceSelector
              className="text-sm w-44 h-8"
              form={form}
              name="vehicleTypes"
              onChange={(value: string) => {
                onHandleChangeVehicleType(value);
              }}
              placeholder={t('fee.type')}
            />

            <FormEntitySelector
              allowClear
              className="w-40"
              form={form}
              label={null}
              name="status"
              options={[
                {
                  label: t('common.status.active'),
                  value: ServiceFeeEntity.status.ACTIVE,
                },
                {
                  label: t('common.status.inactive'),
                  value: ServiceFeeEntity.status.INACTIVE,
                },
              ]}
              placeholder={t('adminAndRole.user.table.filter.status')}
              size="middle"
            />

            <FormEntitySelector
              allowClear
              className="w-40"
              form={form}
              label={null}
              name="perType"
              options={[
                {
                  label: t('fee.create.basedOn.ride'),
                  value: CreateServiceFeeDto.perType.RIDE,
                },
                {
                  label: t('fee.create.basedOn.km'),
                  value: CreateServiceFeeDto.perType.KILOMETER,
                },
              ]}
              placeholder={t('fee.perType.placeholder')}
              size="middle"
            />

            <FormEmployeeSelector
              className="sm:w-66 w-[276px] h-8"
              form={form}
              isActive={true}
              isFilter={true}
              label={null}
              maxTagCount="responsive"
              mode="multiple"
              name="createdById"
              onClear={() => form.setValue('createdById', '')}
              placeholder={t('notification.table.createdBy')}
              showRequired
            />

            {showClearFilter ? (
              <Button
                className="px-2 bg-neutral-6"
                onClick={onClearFilter}
                size="middle"
                type="text"
              >
                <TypographyUI font="Semibold" type="BodySm">
                  {t('common.button.clearFilter')}
                </TypographyUI>
              </Button>
            ) : null}
          </div>
        }
      />
      <TableUI
        columns={columns}
        current={current}
        dataSource={feeList}
        loading={isLoading}
        onChange={onTableChange}
        onRow={record => ({
          onDoubleClick: () => {
            onViewDetail(record.id);
          },
        })}
        pageSize={limit}
        size="small"
        total={meta?.total || 0}
      />
      {isShowCreateModal ? (
        <CreateServiceFeeModal
          onCancel={onCancelCreateFee}
          onSuccess={refetch}
          visible={isShowCreateModal}
        />
      ) : null}
      {serviceFeeSelected ? (
        <UpdateServiceFeeModal
          onCancel={onCancelUpdateFee}
          onSuccess={refetch}
          selectedFee={serviceFeeSelected}
          visible={!!serviceFeeSelected}
        />
      ) : null}
      {idDelete ? (
        <DeleteServiceFeeModal
          id={idDelete}
          onCancel={() => setIdDelete('')}
          onSuccess={() => {
            refetch();
            setIdDelete('');
          }}
          visible={!!idDelete}
        />
      ) : null}
    </>
  );
}
