import { ServiceFeeEntity } from '#/api/requests';
import BaseButton from '#/shared/components/Button/BaseButton';
import { FilterWrapperUI } from '#/shared/components/FilterWrapperUI';
import { FormEmployeeSelector } from '#/shared/components/selectors/Employee/EmployeeSelector';
import { FormEntitySelector } from '#/shared/components/selectors/FormEntitySelector';
import { ServiceSelector } from '#/shared/components/selectors/Service/ServiceSelector';
import { FormZoneSelector } from '#/shared/components/selectors/ZoneSelector/ZoneSelector';
import { TypographyUI } from '#/shared/components/TypographyUI';
import { Permission } from '#/shared/constants';
import { hasPermission } from '#/shared/utils';
import useAuthStore from '#/store/useAuthStore';
import { Button, Col, Row } from 'antd';
import { Add } from 'iconsax-react';
import { useTranslation } from 'react-i18next';
import { UpsertFixedFareModal } from '../modals/UpsertFixedFareModal';
import { useModal } from '#/shared/hooks/useModal';
import type { UseFormReturn } from 'react-hook-form';
import type { BaseFilter } from '#/shared/hooks';
import type { FixedFareFilter } from '../hooks/useFixedRateList';

interface Props {
  form: UseFormReturn<BaseFilter & FixedFareFilter>;
  onClearFilter: () => void;
  showClearFilter: boolean;
}

export default function FixedFareFilter({
  form,
  onClearFilter,
  showClearFilter,
}: Props) {
  const { t } = useTranslation();
  const { user } = useAuthStore();
  const { permissions } = user;
  const { closeModal, isVisible, openModal } = useModal();

  const hasCreateFare = hasPermission(
    permissions,
    Permission.CREATE_FIXED_FARE,
  );

  const onHandleChangeVehicleType = (value: string) => {
    form.setValue('vehicleType', value);
  };

  return (
    <>
      <FilterWrapperUI
        addOnActions={
          <BaseButton
            className={`${hasCreateFare ? 'flex' : 'hidden'}`}
            content={t('fixedFare.newFare')}
            icon={<Add size={20} />}
            onClick={openModal}
            type="primary"
          />
        }
        filters={
          <Row className="flex gap-2">
            <Col>
              <FormZoneSelector
                className="w-60"
                form={form}
                label={null}
                name="zones"
                placeholder={t('fixedFare.zoneSelects')}
              />
            </Col>
            <ServiceSelector
              className="text-sm w-60 h-8"
              form={form}
              name="vehicleType"
              onChange={(value: string) => {
                onHandleChangeVehicleType(value);
              }}
              placeholder={t('fixedFare.vehicleTypes')}
            />

            <FormEntitySelector
              allowClear
              className="w-40"
              form={form}
              label={null}
              name="status"
              options={[
                {
                  label: t('common.status.active'),
                  value: ServiceFeeEntity.status.ACTIVE,
                },
                {
                  label: t('common.status.inactive'),
                  value: ServiceFeeEntity.status.INACTIVE,
                },
              ]}
              placeholder={t('adminAndRole.user.table.filter.status')}
              size="middle"
            />

            <FormEmployeeSelector
              className="sm:w-66 w-[276px] h-8"
              form={form}
              isActive={undefined}
              isFilter={true}
              label={null}
              name="createdById"
              onClear={() => form.setValue('createdById', '')}
              placeholder={t('notification.table.createdBy')}
              showRequired
            />

            {showClearFilter ? (
              <Button
                className="bg-white"
                onClick={onClearFilter}
                size="middle"
                type="text"
              >
                <TypographyUI font="Semibold" type="BodySm">
                  {t('common.button.clearFilter')}
                </TypographyUI>
              </Button>
            ) : null}
          </Row>
        }
      />
      {isVisible ? (
        <UpsertFixedFareModal
          onCancel={closeModal}
          type="list"
          visible={isVisible}
        />
      ) : null}
    </>
  );
}
