import { TableUI } from '#/shared/components/tableUI/TableUI';
import useFixedRateList from '../hooks/useFixedRateList';
import { DeleteFixedFareModal } from '../modals/DeleteFixedFareModal';
import { UpsertFixedFareModal } from '../modals/UpsertFixedFareModal';
import FixedFareFilter from './FixedFareFilter';

export default function FixedFare() {
  const {
    columns,
    current,
    fareId,
    fareList,
    form,
    idDelete,
    isLoading,
    limit,
    meta,
    onTableChange,
    refetch,
    setIdDelete,
    onViewDetail,
    onClearFilter,
    showClearFilter,
    setFareId,
  } = useFixedRateList();

  return (
    <>
      <FixedFareFilter
        form={form}
        onClearFilter={onClearFilter}
        showClearFilter={showClearFilter}
      />
      <TableUI
        columns={columns}
        current={current}
        dataSource={fareList}
        loading={isLoading}
        onChange={onTableChange}
        onRow={record => ({
          onDoubleClick: () => {
            onViewDetail(record.id);
          },
        })}
        pageSize={limit}
        size="small"
        total={meta?.total || 0}
      />

      {fareId ? (
        <UpsertFixedFareModal
          fare={fareList.find(fare => fare.id === fareId)}
          fareId={fareId}
          onCancel={() => setFareId('')}
          type="list"
          visible={!!fareId}
        />
      ) : null}

      {idDelete ? (
        <DeleteFixedFareModal
          id={idDelete}
          onCancel={() => setIdDelete('')}
          onSuccess={() => {
            refetch();
            setIdDelete('');
          }}
          visible={!!idDelete}
        />
      ) : null}
    </>
  );
}
