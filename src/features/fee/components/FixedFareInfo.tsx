import { useFeeConfigurationServiceFeeConfigurationControllerUpdateStatusFixedFare } from '#/api/queries';
import { FixedFareEntity } from '#/api/requests';
import { DescriptionUI } from '#/shared/components/DescriptionUI/DescriptionUI';
import LabelGroup from '#/shared/components/LabelGroup';
import { TypographyUI } from '#/shared/components/TypographyUI';
import ZoneLabelGroup from '#/shared/components/ZoneLabelGroup';
import { formatter } from '#/shared/utils';
import { Button, Card, Divider, Switch, Tag, notification } from 'antd';
import { t } from 'i18next';
import { Edit2, Map1, Trash } from 'iconsax-react';

interface Props {
  fare: FixedFareEntity | undefined;
  hasUpdateFare: boolean;
  hasDeleteFare: boolean;
  onUpdate?: () => void;
  onUpdateStatusSuccess?: () => void;
  onDelete?: () => void;
  onOpenMapModal: () => void;
}

function FixedFareInfo({
  fare,
  onUpdate,
  onDelete,
  onUpdateStatusSuccess,
  hasUpdateFare,
  hasDeleteFare,
  onOpenMapModal,
}: Props) {
  const { isPending, mutate } =
    useFeeConfigurationServiceFeeConfigurationControllerUpdateStatusFixedFare({
      onSuccess: () => {
        onUpdateStatusSuccess?.();
        notification.success({
          description: t('fixedFare.update.notification.description'),
          message: t('fixedFare.update.notification.message'),
        });
      },
    });

  const onUpdateStatus = (id: string, status: FixedFareEntity.status) => {
    mutate({
      id,
      requestBody: {
        status,
      },
    });
  };

  const getApplyDate = (fare: FixedFareEntity) => {
    let applyDateContent;

    switch (fare?.applyDateType) {
      case FixedFareEntity.applyDateType.ALL:
        applyDateContent = t('rate.create.applyTime.all');
        break;
      case FixedFareEntity.applyDateType.SPECIFIC_DATE:
        applyDateContent = (
          <>
            {formatter.formatDateDefault(fare?.startApplyDate || '')} {'-'}
            {formatter.formatDateDefault(fare?.endApplyDate || '')}
            {' ('}
            {fare?.startTime?.hour?.toString().padStart(2, '0')}
            {':'}
            {fare?.startTime?.minute?.toString().padStart(2, '0')} {'-'}
            {fare?.endTime?.hour?.toString().padStart(2, '0')}
            {':'}
            {fare?.endTime?.minute?.toString().padStart(2, '0')}
            {')'}
          </>
        );
        break;
      default:
        applyDateContent = formatter.getRepeatDay(
          fare?.applyRepeatDay?.map(item => parseInt(item)),
        );
        applyDateContent = (
          <>
            {formatter.getRepeatDay(
              fare?.applyRepeatDay?.map(item => parseInt(item)),
            )}
            {' ('}
            {fare?.startTime?.hour?.toString().padStart(2, '0')}
            {':'}
            {fare?.startTime?.minute?.toString().padStart(2, '0')} {'-'}
            {fare?.endTime?.hour?.toString().padStart(2, '0')}
            {':'}
            {fare?.endTime?.minute?.toString().padStart(2, '0')}
            {')'}
          </>
        );
    }

    return <>{applyDateContent}</>;
  };

  return (
    <Card
      bodyStyle={{
        paddingBottom: 16,
        paddingLeft: 16,
        paddingRight: 16,
        paddingTop: 0,
      }}
      className="shadow-sm"
      headStyle={{
        borderBottom: 0,
      }}
      title={
        <div className="flex justify-between">
          <span className="text-lg">{t('fixedFare.information')}</span>
          <div className="flex items-center gap-2">
            <Button
              className="shadow-xl"
              icon={<Map1 size={22} />}
              onClick={onOpenMapModal}
              size="middle"
              type="text"
            />
            {hasUpdateFare ? (
              <>
                <Switch
                  checked={fare?.status === FixedFareEntity.status.ACTIVE}
                  loading={isPending}
                  onClick={() => {
                    onUpdateStatus(
                      fare?.id || '',
                      fare?.status === FixedFareEntity.status.ACTIVE
                        ? FixedFareEntity.status.INACTIVE
                        : FixedFareEntity.status.ACTIVE,
                    );
                  }}
                  size="small"
                />
                <Button
                  className="shadow-xl"
                  icon={<Edit2 size={22} />}
                  onClick={onUpdate}
                  size="middle"
                  type="text"
                />
              </>
            ) : null}
            <Button
              className={`${hasDeleteFare ? 'shadow-xl' : 'hidden'}`}
              icon={<Trash size={22} />}
              onClick={onDelete}
              size="middle"
              type="text"
            />
          </div>
        </div>
      }
    >
      <Divider className="mt-0" />
      {/* General information */}
      <div className="flex">
        <div className="w-[50%]">
          <DescriptionUI
            colSpan={24}
            items={[
              {
                title: (
                  <TypographyUI
                    className="min-w-[125px]"
                    font="Semibold"
                    type="BodySm"
                  >
                    {t('common.from')}
                  </TypographyUI>
                ),
                value: <ZoneLabelGroup group={fare?.fromZones || []} />,
              },
              {
                title: (
                  <TypographyUI
                    className="min-w-[125px]"
                    font="Semibold"
                    type="BodySm"
                  >
                    {t('common.to')}
                  </TypographyUI>
                ),
                value: <ZoneLabelGroup group={fare?.toZones || []} />,
              },
              {
                title: (
                  <TypographyUI
                    className="min-w-[125px]"
                    font="Semibold"
                    type="BodySm"
                  >
                    {t('rate.create.applyTime.title')}
                  </TypographyUI>
                ),
                value: getApplyDate(fare as FixedFareEntity),
              },
            ]}
          />
        </div>
        <div className="w-[50%]">
          <DescriptionUI
            colSpan={24}
            items={[
              {
                title: (
                  <TypographyUI
                    className="min-w-[125px]"
                    font="Semibold"
                    type="BodySm"
                  >
                    {t('fixedFare.title')}
                  </TypographyUI>
                ),
                value: fare?.amount
                  ? formatter.currencyFormat(fare.amount)
                  : '',
              },
              {
                title: (
                  <TypographyUI
                    className="min-w-[125px]"
                    font="Semibold"
                    type="BodySm"
                  >
                    {t('fee.vehicleType')}
                  </TypographyUI>
                ),
                value: (
                  <LabelGroup
                    group={fare?.vehicleTypes.map(item =>
                      formatter.normalizeEnumString(item),
                    )}
                  />
                ),
              },
              {
                title: (
                  <TypographyUI
                    className="min-w-[125px]"
                    font="Semibold"
                    type="BodySm"
                  >
                    {t('common.statusLabel')}
                  </TypographyUI>
                ),
                value:
                  fare?.status == FixedFareEntity.status.ACTIVE ? (
                    <Tag className="bg-success-color-soft text-success-color border-none font-semibold text-sm">
                      {t('common.status.active')}
                    </Tag>
                  ) : (
                    <Tag className="bg-error-color-soft text-error-color border-none font-semibold text-sm">
                      {t('common.status.inactive')}
                    </Tag>
                  ),
              },
            ]}
          />
        </div>
      </div>
    </Card>
  );
}

export default FixedFareInfo;
