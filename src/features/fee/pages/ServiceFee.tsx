import { TypographyUI } from '#/shared/components/TypographyUI';
import { PageLayout } from '#/shared/layouts/PageLayout';
import { Tabs } from 'antd';
import { useTranslation } from 'react-i18next';
import ServiceFee from '../components/ServiceFee';
import Rate from '../components/RateFee';
import { FeeTab } from '../constants';
import { useNavigate, useParams } from 'react-router-dom';
import { NavbarKey, Permission } from '#/shared/constants';
import { withPermission } from '#/shared/hocs/withPermission';
import useAuthStore from '#/store/useAuthStore';
import { useRefetchUser } from '#/shared/hooks/useRefetchUser';
import FixedFare from '../components/FixedFare';
import TipPosition from '../components/TipPosition';

function ServiceFeePage() {
  const { t } = useTranslation();
  const params = useParams();
  const navigate = useNavigate();
  const { user } = useAuthStore();
  const { permissions } = user;
  const { refetchUser } = useRefetchUser();

  const items = [
    {
      children: <ServiceFee />,
      key: FeeTab.Fee,
      label: (
        <TypographyUI font="Semibold" type="BodySm">
          {t('fee.serviceFee')}
        </TypographyUI>
      ),
      permission: Permission.VIEW_CONFIG_FEE,
    },
    {
      children: <Rate />,
      key: FeeTab.Rate,
      label: (
        <TypographyUI font="Semibold" type="BodySm">
          {t('fee.rate')}
        </TypographyUI>
      ),
      permission: Permission.VIEW_CONFIG_RATE,
    },
    {
      children: <FixedFare />,
      key: FeeTab.FixedFare,
      label: (
        <TypographyUI font="Semibold" type="BodySm">
          {t('fixedFare.title')}
        </TypographyUI>
      ),
      permission: Permission.VIEW_FIXED_FARE,
    },
    {
      children: <TipPosition />,
      key: FeeTab.TipPosition,
      label: (
        <TypographyUI font="Semibold" type="BodySm">
          {t('fee.tipPosition')}
        </TypographyUI>
      ),
      permission: Permission.VIEW_CONFIG_TIP,
    },
  ];

  return (
    <PageLayout
      header={
        <TypographyUI font="Semibold" type="H3">
          {t('fee.title')}
        </TypographyUI>
      }
    >
      <Tabs
        defaultActiveKey={params.tab || FeeTab.Fee}
        items={items.filter(item => permissions.includes(item.permission))}
        onChange={tab => {
          navigate(`/${NavbarKey.Service}/${tab}`);
          refetchUser();
        }}
      />
    </PageLayout>
  );
}

export default withPermission(ServiceFeePage, [
  Permission.VIEW_CONFIG_FEE,
  Permission.VIEW_CONFIG_RATE,
  Permission.VIEW_CONFIG_TIP,
  Permission.VIEW_FIXED_FARE,
]);
