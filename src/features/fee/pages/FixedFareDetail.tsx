import { useFeeConfigurationServiceFeeConfigurationControllerGetFixedFare } from '#/api/queries';
import type { FixedFareEntity, SimpleZoneEntity } from '#/api/requests';
import BreadcrumbCustom from '#/shared/components/Breadcrumb';
import { TypographyUI } from '#/shared/components/TypographyUI';
import { NavbarKey, Permission } from '#/shared/constants';
import { withPermission } from '#/shared/hocs/withPermission';
import { PageLayout } from '#/shared/layouts/PageLayout';
import { hasPermission } from '#/shared/utils';
import useAuthStore from '#/store/useAuthStore';
import { Spin } from 'antd';
import { ArrowLeft } from 'iconsax-react';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate, useParams } from 'react-router-dom';
import FixedFareInfo from '../components/FixedFareInfo';
import { DeleteFixedFareModal } from '../modals/DeleteFixedFareModal';
import { UpsertFixedFareModal } from '../modals/UpsertFixedFareModal';
import FixedFareMapModal from '../modals/FixedFareMapModal';

function FixedFareDetail() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { user } = useAuthStore();
  const { permissions } = user;

  const hasUpdateFare = hasPermission(
    permissions,
    Permission.UPDATE_FIXED_FARE,
  );
  const hasDeleteFare = hasPermission(
    permissions,
    Permission.DELETE_FIXED_FARE,
  );

  const params = useParams();
  const fareId = params.id || '';

  const [idDelete, setIdDelete] = useState('');
  const [fare, setFare] = useState<FixedFareEntity>();
  const [isShowUpdateModal, setIsShowUpdateModal] = useState<boolean>(false);
  const [isShowMapModal, setIsShowMapModal] = useState<boolean>(false);

  const { data, isLoading, refetch } =
    useFeeConfigurationServiceFeeConfigurationControllerGetFixedFare(
      {
        id: fareId,
      },
      [
        'useFeeConfigurationServiceFeeConfigurationControllerGetFixedFare',
        fareId,
      ],
    );

  const onOpenMapModal = () => {
    setIsShowMapModal(true);
  };

  const onCloseMapModal = () => {
    setIsShowMapModal(false);
  };

  const onHandleUpdateModal = () => {
    setIsShowUpdateModal(true);
  };

  const onCancelUpdateFee = () => {
    setIsShowUpdateModal(false);
  };

  const onShowDeleteModal = () => {
    setIdDelete(fare?.id as string);
  };

  const onDeleteSuccess = () => {
    refetch();
    setIdDelete('');
    navigate(-1);
  };

  const onBack = () => {
    navigate(`/${NavbarKey.Service}/${NavbarKey.FixedFare}`);
  };

  const formatBreadcrumb = () => [
    {
      path: `/${NavbarKey.Service}/${NavbarKey.FixedFare}`,
      title: t('fee.title'),
    },
    {
      title: t('fixedFare.title'),
    },
  ];

  useEffect(() => {
    if (data) setFare(data?.data as FixedFareEntity);
  }, [data]);

  return (
    <PageLayout
      header={
        <>
          <BreadcrumbCustom data={formatBreadcrumb()} />
          <div className="text-neutral-1">
            <TypographyUI font="Semibold" type="H3">
              <ArrowLeft className="cursor-pointer" onClick={onBack} />{' '}
              {t('fixedFare.detail')}
            </TypographyUI>
          </div>
        </>
      }
    >
      {isShowUpdateModal ? (
        <UpsertFixedFareModal
          fare={fare}
          fareId={fareId}
          onCancel={onCancelUpdateFee}
          type="detail"
          visible={!!isShowUpdateModal}
        />
      ) : null}
      {idDelete ? (
        <DeleteFixedFareModal
          id={idDelete}
          onCancel={() => setIdDelete('')}
          onSuccess={onDeleteSuccess}
          visible={!!idDelete}
        />
      ) : null}
      {isShowMapModal ? (
        <FixedFareMapModal
          fromZones={fare?.fromZones as SimpleZoneEntity[]}
          onHandleCloseMap={onCloseMapModal}
          toZones={fare?.toZones as SimpleZoneEntity[]}
        />
      ) : null}
      <Spin spinning={isLoading}>
        <FixedFareInfo
          fare={fare}
          hasDeleteFare={hasDeleteFare}
          hasUpdateFare={hasUpdateFare}
          onDelete={onShowDeleteModal}
          onOpenMapModal={onOpenMapModal}
          onUpdate={onHandleUpdateModal}
          onUpdateStatusSuccess={refetch}
        />
      </Spin>
    </PageLayout>
  );
}

export default withPermission(FixedFareDetail, Permission.VIEW_FIXED_FARE);
