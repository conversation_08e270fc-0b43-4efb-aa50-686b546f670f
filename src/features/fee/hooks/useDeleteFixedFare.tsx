import { useFeeConfigurationServiceFeeConfigurationControllerDeleteFixedFare } from '#/api/queries';
import type { ApiError } from '#/api/requests';
import { showErrorMessage } from '#/shared/utils';
import { notification } from 'antd';
import { useTranslation } from 'react-i18next';

interface Props {
  onCancel: () => void;
  onSuccess?: () => void;
  id: string;
}

export const useDeleteFixedFare = ({ onCancel, onSuccess, id }: Props) => {
  const { t } = useTranslation();

  const { mutate, isPending } =
    useFeeConfigurationServiceFeeConfigurationControllerDeleteFixedFare();

  const onDelete = () => {
    mutate(
      { id },
      {
        onError: error => showErrorMessage(error as ApiError),
        onSuccess: () => {
          onCancel();
          onSuccess?.();
          notification.success({
            description: t('fixedFare.delete.notification.description'),
            message: t('fixedFare.delete.notification.message'),
          });
        },
      },
    );
  };

  return {
    isLoading: isPending,
    onDelete,
  };
};
