/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  useFeeConfigurationServiceFeeConfigurationControllerCreateFixedFare,
  useFeeConfigurationServiceFeeConfigurationControllerGetFixedFare,
  useFeeConfigurationServiceFeeConfigurationControllerGetFixedFareKey,
  useFeeConfigurationServiceFeeConfigurationControllerUpdateFixedFare,
} from '#/api/queries';
import { showErrorMessage } from '#/shared/utils';
import { notification } from 'antd';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { fareSchema } from '../schemas/fare.schema';
import type { SpecificTimeType } from '#/api/requests';
import { CreateFixedFareDto, FixedFareEntity } from '#/api/requests';
import { refetchFareDetail, refetchListFare } from './useRefetchFare';
import { DEFAULT_UINT_DIVIDED_BY_CENT } from '#/shared/constants';
import useConfigFareStore from '#/store/useConfigFareStore';
import { isUndefined } from 'lodash-es';

interface Props {
  fareId?: string;
  onClose: () => void;
  type: 'list' | 'detail';
}

export const useUpsertFixedFare = ({ fareId, onClose, type }: Props) => {
  const { t } = useTranslation();
  const [applyDateType, setApplyDateType] = useState(
    CreateFixedFareDto.applyDateType.ALL,
  );

  const form = useForm({
    defaultValues: {
      applyDateType: FixedFareEntity.applyDateType.ALL,
      endApplyDate: '',
      endTime: undefined,
      fromZones: [],
      isAllowCoupon: false,
      isRoundTrip: false,
      startApplyDate: '',
      startTime: undefined,
      status: FixedFareEntity.status.INACTIVE,
      toZones: [],
      vehicleTypes: ['ALL'],
    },
    resolver: fareSchema(),
  });

  const { setIsShowErrorStore, isDisableUpdateStore, setDisableUpdateStore } =
    useConfigFareStore();

  const [isDisableUpdate, setIsDisableUpdate] = useState<boolean>(false);

  const { data: dataDetail, isLoading } =
    useFeeConfigurationServiceFeeConfigurationControllerGetFixedFare(
      {
        id: fareId || '',
      },
      [
        useFeeConfigurationServiceFeeConfigurationControllerGetFixedFareKey,
        fareId,
      ],
      {
        enabled: !!fareId,
      },
    );

  const { mutateAsync: createFixedFare, isPending: isPendingCreate } =
    useFeeConfigurationServiceFeeConfigurationControllerCreateFixedFare({
      onError: showErrorMessage,
    });

  const { mutateAsync: updateFixedFare, isPending: isPendingUpdate } =
    useFeeConfigurationServiceFeeConfigurationControllerUpdateFixedFare({
      onError: showErrorMessage,
    });

  // Check if start time equals end time
  const isStartTimeEqualsEndTime = () => {
    const currentStartTime = form.getValues('startTime') as SpecificTimeType;
    const currentEndTime = form.getValues('endTime') as SpecificTimeType;

    if (!currentStartTime || !currentEndTime) {
      return false;
    }

    const startHour = currentStartTime.hour;
    const startMinute = currentStartTime.minute;
    const endHour = currentEndTime.hour;
    const endMinute = currentEndTime.minute;

    if (
      startHour === undefined ||
      startMinute === undefined ||
      endHour === undefined ||
      endMinute === undefined
    ) {
      return false;
    }

    // Check if hours and minutes are equal
    return startHour === endHour && startMinute === endMinute;
  };

  const handleSubmit = async () => {
    setIsShowErrorStore(true);

    const {
      fromZones,
      toZones,
      isRoundTrip,
      isAllowCoupon,
      amount,
      vehicleTypes,
      applyDateType,
      applyRepeatDay,
      startApplyDate,
      endApplyDate,
      status,
      startTime,
      endTime,
    } = form.getValues();

    const requestBody = {
      amount: amount * DEFAULT_UINT_DIVIDED_BY_CENT,
      applyDateType: applyDateType as CreateFixedFareDto.applyDateType,
      applyRepeatDay: applyRepeatDay || [],
      endApplyDate: endApplyDate || '',
      endTime: endTime as SpecificTimeType,
      fromZones: fromZones as any[][],
      isAllowCoupon: isAllowCoupon as boolean,
      isRoundTrip: isRoundTrip as boolean,
      startApplyDate: startApplyDate || '',
      startTime: startTime as SpecificTimeType,
      status: status as CreateFixedFareDto.status,
      toZones: toZones as any[][],
      vehicleTypes: vehicleTypes || [],
    };

    if (!isDisableUpdate)
      await (!fareId
        ? createFixedFare({
            requestBody,
          })
        : updateFixedFare({
            id: fareId,
            requestBody,
          }));
    else return;

    notification.success({
      description: t(
        `fixedFare.${!fareId ? 'create' : 'update'}.notification.description`,
      ),
      message: t(
        `fixedFare.${!fareId ? 'create' : 'update'}.notification.message`,
      ),
    });

    form.reset(form.formState.defaultValues);
    onClose();
    setIsShowErrorStore(false);
    type === 'list' ? refetchListFare() : refetchFareDetail();
    setApplyDateType(CreateFixedFareDto.applyDateType.ALL);
  };

  useEffect(() => {
    if (dataDetail?.data) {
      const {
        amount,
        applyDateType,
        applyRepeatDay,
        endApplyDate,
        endTime,
        fromZones,
        isAllowCoupon,
        isRoundTrip,
        startApplyDate,
        startTime,
        status,
        toZones,
        vehicleTypes,
      } = (dataDetail.data || {}) as FixedFareEntity;
      form.reset({
        amount: amount / DEFAULT_UINT_DIVIDED_BY_CENT,
        applyDateType,
        applyRepeatDay,
        endApplyDate,
        endTime,
        fromZones: fromZones?.map(item => item.zoneId),
        isAllowCoupon,
        isRoundTrip,
        startApplyDate,
        startTime,
        status,
        toZones: toZones?.map(item => item.zoneId),
        vehicleTypes,
      });
    }
  }, [dataDetail]);

  useEffect(() => {
    if (
      isStartTimeEqualsEndTime() ||
      (isUndefined(form.getValues('startTime')) &&
        applyDateType !== CreateFixedFareDto.applyDateType.ALL) ||
      (!form.getValues('startApplyDate') &&
        applyDateType === CreateFixedFareDto.applyDateType.SPECIFIC_DATE)
    ) {
      setDisableUpdateStore(true);
    } else {
      setDisableUpdateStore(false);
    }
  }, [
    applyDateType,
    form.getValues('startTime'),
    form.getValues('endTime'),
    form.getValues('startApplyDate'),
  ]);

  useEffect(() => {
    setIsDisableUpdate(isDisableUpdateStore);
  }, [isDisableUpdateStore]);

  return {
    applyDateType,
    form,
    handleSubmit,
    isLoadingModal: isLoading || false,
    isLoadingSubmit: isPendingCreate || isPendingUpdate,
    isStartTimeEqualsEndTime,
    setApplyDateType,
  };
};
