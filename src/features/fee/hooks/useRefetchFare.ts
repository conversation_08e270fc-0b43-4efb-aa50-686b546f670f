import {
  useFeeConfigurationServiceFeeConfigurationControllerGetFixedFareKey,
  useFeeConfigurationServiceFeeConfigurationControllerGetFixedFaresKey,
} from '#/api/queries';
import { queryClient } from '#/configs/client';

export const refetchListFare = () => {
  queryClient.refetchQueries({
    queryKey: [
      useFeeConfigurationServiceFeeConfigurationControllerGetFixedFaresKey,
    ],
  });
};

export const refetchFareDetail = () => {
  queryClient.refetchQueries({
    queryKey: [
      useFeeConfigurationServiceFeeConfigurationControllerGetFixedFareKey,
    ],
  });
};
