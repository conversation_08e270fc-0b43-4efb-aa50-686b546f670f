import {
  useFeeConfigurationServiceFeeConfigurationControllerGetFixedFares,
  useFeeConfigurationServiceFeeConfigurationControllerGetFixedFaresKey,
} from '#/api/queries';
import type { SimpleZoneEntity } from '#/api/requests';
import { FixedFareEntity } from '#/api/requests';
import { ActionColumnTitle } from '#/shared/components/tableUI/ActionColumnTitle';
import { MoreTableAction } from '#/shared/components/tableUI/MoreTableAction';
import ZoneLabelGroup from '#/shared/components/ZoneLabelGroup';
import { DEFAULT_META, NavbarKey, Permission } from '#/shared/constants';
import { usePagination } from '#/shared/hooks';
import { Tool, formatter, hasPermission } from '#/shared/utils';
import useAuthStore from '#/store/useAuthStore';
import { Avatar, Button, Tag } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { Eye } from 'iconsax-react';
import { isEmpty, omitBy } from 'lodash-es';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

const getFieldProps = Tool.getFieldProps<FixedFareEntity>;

export interface FixedFareFilter {
  vehicleType: string[];
  status: string;
  createdById: string;
  perType: string;
  zones: string[];
}

export default function useFixedRateList() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { user } = useAuthStore();
  const { permissions } = user;

  const [idDelete, setIdDelete] = useState('');
  const [fareId, setFareId] = useState<string>('');
  const [fareList, setFareList] = useState<FixedFareEntity[]>([]);

  const {
    current,
    onTableChange,
    limit,
    offset,
    form,
    order,
    vehicleType,
    status,
    createdById,
    zones,
  } = usePagination<FixedFareFilter>();

  const hasUpdateFare = hasPermission(
    permissions,
    Permission.UPDATE_FIXED_FARE,
  );
  const hasDeleteFare = hasPermission(
    permissions,
    Permission.DELETE_FIXED_FARE,
  );

  const showClearFilter = !!zones || !!vehicleType || !!status || !!createdById;

  const onClearFilter = () => {
    form.reset();
  };

  const onViewDetail = (id: string) => {
    navigate(`/${NavbarKey.Service}/${NavbarKey.FixedFare}/${id}`);
  };

  const onHandleGetApplyTime = (fare: FixedFareEntity) => {
    if (fare?.applyDateType === FixedFareEntity.applyDateType.SPECIFIC_DATE)
      return (
        <div className="py-1">
          <div>
            {formatter.formatDateDefault(fare?.startApplyDate || '')} {'-'}
            {formatter.formatDateDefault(fare?.endApplyDate || '')}
          </div>
          <div className="mt-1 text-neutral-600">
            {fare?.startTime?.hour?.toString().padStart(2, '0')}
            {':'}
            {fare?.startTime?.minute?.toString().padStart(2, '0')} {'-'}{' '}
            {fare?.endTime?.hour?.toString().padStart(2, '0')}
            {':'}
            {fare?.endTime?.minute?.toString().padStart(2, '0')}
          </div>
        </div>
      );

    if (fare?.applyDateType === FixedFareEntity.applyDateType.DAY)
      return (
        <>
          {formatter.getRepeatDay(
            fare?.applyRepeatDay?.map(item => parseInt(item)),
          )}
          <div className="mt-1 text-neutral-600">
            {fare?.startTime?.hour?.toString().padStart(2, '0')}
            {':'}
            {fare?.startTime?.minute?.toString().padStart(2, '0')} {'-'}{' '}
            {fare?.endTime?.hour?.toString().padStart(2, '0')}
            {':'}
            {fare?.endTime?.minute?.toString().padStart(2, '0')}
          </div>
        </>
      );

    return t('rate.create.applyTime.all');
  };

  const columns: ColumnsType<FixedFareEntity> = [
    {
      dataIndex: getFieldProps('fromZones'),
      key: getFieldProps('fromZones'),
      render: (value: SimpleZoneEntity[]) => (
        <ZoneLabelGroup className="my-1" group={value} />
      ),
      title: t('common.from'),
      width: 200,
    },
    {
      dataIndex: getFieldProps('toZones'),
      key: getFieldProps('toZones'),
      render: (value: SimpleZoneEntity[]) => (
        <ZoneLabelGroup className="my-1" group={value} />
      ),
      title: t('common.to'),
      width: 200,
    },
    {
      dataIndex: 'amount',
      key: 'amount',
      render: (amount: number) => formatter.currencyFormat(amount),
      sorter: true,
      title: t('bookingHistory.detail.receipt.amountCharged'),
      width: 150,
    },
    {
      dataIndex: 'vehicleTypes',
      key: 'vehicleTypes',
      render: (vehicleTypes: string[]) =>
        formatter.separateVehicleType(vehicleTypes),
      sorter: true,
      title: t('fee.vehicleType'),
      width: 200,
    },
    {
      dataIndex: 'applyTime',
      key: 'applyTime',
      render: (_, record: FixedFareEntity) => onHandleGetApplyTime(record),
      title: t('rate.create.applyTime.title'),
      width: 200,
    },
    {
      dataIndex: 'status',
      key: 'status',
      render: (status: FixedFareEntity.status) => (
        <Tag
          className={`${
            status == FixedFareEntity.status.ACTIVE
              ? 'bg-success-color-soft text-success-color'
              : 'bg-error-color-soft text-error-color'
          } border-none font-semibold`}
        >
          {formatter.normalizeFirstEnumString(status)}
        </Tag>
      ),

      sorter: true,
      title: t('adminAndRole.user.table.status'),
      width: 100,
    },
    {
      dataIndex: 'createdBy.fullName',
      key: 'createdBy',
      render: (_, record: FixedFareEntity) => (
        <>
          {record?.createdBy?.fullName ? (
            <div className="flex gap-2">
              <Avatar
                className="text-base flex items-center w-8 h-8"
                src={record?.createdBy?.avatarUrl}
              >
                {record?.createdBy?.fullName?.slice(0, 1)}
              </Avatar>
              <div className="flex flex-col">
                <div className="text-xs text-neutral-1 truncate xxl:max-w-[180px] max-w-[224px]">
                  {record?.createdBy?.fullName}
                </div>
                <div className="text-xs text-neutral-2 block truncate xxl:w-48 xxxl:w-52 max-w-[244px]">
                  {record?.createdBy?.email}
                </div>
              </div>
            </div>
          ) : null}
        </>
      ),
      sorter: true,
      title: t('notification.table.createdBy'),
      width: 300,
    },
    {
      align: 'right',
      dataIndex: 'action',
      key: 'action',
      render: (_, record: FixedFareEntity) => (
        <div className="flex justify-center z-20">
          <Button
            icon={<Eye size={20} />}
            onClick={() => onViewDetail(record.id)}
            size="middle"
            type="text"
          />

          <MoreTableAction
            isHideAll={!hasUpdateFare && !hasDeleteFare}
            isHideDelete={!hasDeleteFare}
            isHideEdit={!hasUpdateFare}
            onDelete={() => {
              setIdDelete(record.id);
            }}
            onEdit={() => {
              setFareId(record.id);
            }}
          />
        </div>
      ),
      title: () => <ActionColumnTitle />,
      width: 100,
    },
  ];

  const createdByIds =
    createdById && createdById.length > 0
      ? JSON.stringify(createdById)
      : undefined;

  const vehicleTypes =
    vehicleType && vehicleType.length > 0 ? vehicleType.toString() : undefined;

  const { data, refetch, isLoading } =
    useFeeConfigurationServiceFeeConfigurationControllerGetFixedFares(
      {
        limit,
        offset,
        order,
        ...omitBy(
          {
            createdByIds,
            q: undefined,
            status,
            vehicleType: vehicleTypes,
            zones,
          },
          isEmpty,
        ),
      },
      [
        useFeeConfigurationServiceFeeConfigurationControllerGetFixedFaresKey,
        limit,
        current,
        offset,
        order,
        undefined,
        vehicleTypes,
        status,
        createdByIds,
        zones,
      ],
    );

  useEffect(() => {
    setFareList(data?.data?.items as FixedFareEntity[]);
  }, [data]);

  return {
    columns,
    current,
    fareId,
    fareList,
    form,
    idDelete,
    isLoading,
    limit,
    meta: data?.data.meta || DEFAULT_META,
    onClearFilter,
    onTableChange,
    onViewDetail,
    refetch,
    setFareId,
    setIdDelete,
    showClearFilter,
  };
}
