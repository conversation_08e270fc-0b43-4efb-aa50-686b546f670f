/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-unnecessary-condition */
import type { RatingResponse } from '#/api/loyalhub/requests';
import { BookingHistoryTab } from '#/features/booking-history/constants';
import CollapseText from '#/shared/components/CollapseText';
import { ActionColumnTitle } from '#/shared/components/tableUI/ActionColumnTitle';
import { TypographyUI } from '#/shared/components/TypographyUI';
import { NavbarKey } from '#/shared/constants';
import usePermissionCheck from '#/shared/hooks/usePermissionCheck';
import { Tool, formatter } from '#/shared/utils';
import { useIsMobile } from '#/store/useIsMobile';
import { StarFilled } from '@ant-design/icons';
import { Avatar, Button } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { Eye } from 'iconsax-react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

const getField = Tool.getFieldProps<RatingResponse>;

export const useRatingDriverColumn = () => {
  const { t } = useTranslation();
  const { hasViewPersonalBooking } = usePermissionCheck();

  const { isMobile } = useIsMobile();
  const navigate = useNavigate();

  const onViewBooking = (bookingId: string) => {
    navigate(
      `/${NavbarKey.BookingHistory}/${BookingHistoryTab.IndividualBookings}/${bookingId}`,
    );
  };

  const columns: ColumnsType<RatingResponse> = [
    {
      dataIndex: getField('bookingCode'),
      fixed: 'left',
      key: getField('bookingCode'),
      render: (bookingCode, record) => (
        <div
          className={`${
            hasViewPersonalBooking ? 'cursor-pointer' : ''
          } font-semibold text-info-color`}
          onClick={
            hasViewPersonalBooking
              ? () => onViewBooking(record?.bookingId)
              : undefined
          }
        >
          #{bookingCode}
        </div>
      ),
      sorter: true,
      title: t('rating.table.bookingId'),
      width: 150,
    },
    {
      dataIndex: 'user?.fullName',
      key: 'user?.fullName',
      render: (_, record) => (
        <div className="flex gap-2">
          <Avatar
            className="min-w-[32px] w-8 h-8 text-base flex items-center"
            src={record?.user?.avatarUrl}
          >
            {record?.user?.fullName?.slice(0, 1)}
          </Avatar>
          <div className="flex flex-col">
            <div className="text-xs text-neutral-1 truncate xxl:max-w-[180px] max-w-[265px]">
              {record?.user?.fullName}
            </div>
            <div className="text-xs text-neutral-2 block truncate xxl:w-48 xxxl:w-[265px] w-[285px]">
              {record?.user?.email}
            </div>
          </div>
        </div>
      ),
      sorter: true,
      title: t('rating.table.customerName'),
      width: 200,
    },

    {
      dataIndex: 'review.rating',
      key: 'review.rating',
      render: (_, record) => (
        <div className="flex flex-row items-center gap-1">
          <TypographyUI font="Regular" type="Caption1">
            {record?.review?.rating}
          </TypographyUI>
          <StarFilled className="text-sm text-alert-color" />
        </div>
      ),
      sorter: true,
      title: t('rating.table.rating'),
      width: 50,
    },
    {
      dataIndex: 'review.note',
      key: 'review.note',
      render: (_, record) => (
        <CollapseText text={record?.review?.note as string} />
      ),
      sorter: true,
      title: t('rating.table.noteDetail'),
      width: 200,
    },
    {
      dataIndex: 'review.ratingDate',
      key: getField('date'),
      render: value => (
        <TypographyUI type="Caption1">
          {formatter.formatDaytime(value?.ratingDate, 'DD/MM/YYYY, HH:mm')}
        </TypographyUI>
      ),
      sorter: true,
      title: t('rating.table.reviewDate'),
      width: 120,
    },
    ...(hasViewPersonalBooking
      ? [
          {
            align: 'right' as any,
            dataIndex: 'key',
            fixed: (!isMobile ? 'right' : undefined) as any,
            key: 'key',
            render: (_, record) => (
              <div className="flex justify-center">
                <Button
                  icon={<Eye size={20} />}
                  onClick={() => onViewBooking(record?.bookingId)}
                  size="middle"
                  type="text"
                />
              </div>
            ),
            title: () => <ActionColumnTitle />,
            width: 50,
          },
        ]
      : []),
  ];
  return {
    columns,
    hasViewPersonalBooking,
    onViewBooking,
  };
};
