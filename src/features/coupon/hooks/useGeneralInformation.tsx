import { useCouponsServiceCouponControllerCheckCoupon } from '#/api/queries';
import { BookingEntity } from '#/api/requests';
import useCouponStore from '#/store/useCouponStore';
import { t } from 'i18next';
import { useEffect, useState } from 'react';
import { useDebounce } from 'use-debounce';

interface Props {
  defaultVehicles?: string[];
}

export function useGeneralInformation({ defaultVehicles }: Props) {
  const [search, setSearch] = useState<string>('');
  const [searchValue] = useDebounce(search, 350);
  const [vehicles, setVehicles] = useState<string[]>(
    defaultVehicles || [BookingEntity.vehicleType.ALL],
  );

  const vehicleList = [
    {
      label: t('coupon.create.vehicleTypes.all'),
      value: BookingEntity.vehicleType.ALL,
    },
    {
      disabled: vehicles?.includes(BookingEntity.vehicleType.ALL),
      label: t('coupon.create.vehicleTypes.typical'),
      value: BookingEntity.vehicleType.TYPICAL_CAR,
    },
    {
      disabled: vehicles?.includes(BookingEntity.vehicleType.ALL),
      label: t('coupon.create.vehicleTypes.van'),
      value: BookingEntity.vehicleType.VAN,
    },
    {
      disabled: vehicles?.includes(BookingEntity.vehicleType.ALL),
      label: t('coupon.create.vehicleTypes.accessible'),
      value: BookingEntity.vehicleType.ACCESSIBLE_VAN,
    },
    {
      disabled: vehicles?.includes(BookingEntity.vehicleType.ALL),
      label: t('coupon.create.vehicleTypes.electric'),
      value: BookingEntity.vehicleType.ELECTRIC_CAR,
    },
  ];

  const ALL_PAYMENT_METHOD = [
    {
      label: t('common.paymentMethod.cash'),
      value: BookingEntity.paymentMethodType.CASH,
    },
    {
      label: t('common.paymentMethod.card'),
      value: BookingEntity.paymentMethodType.CARD,
    },
  ];

  const paymentMethodDefault = [
    BookingEntity.paymentMethodType.CASH,
    BookingEntity.paymentMethodType.CARD,
  ];

  const paymentMethodTreeData = [
    {
      children: ALL_PAYMENT_METHOD,
      label: t('coupon.create.vehicleTypes.all'),
      value: paymentMethodDefault.toString(),
    },
  ];

  const {
    hasAvailableStore,
    isShowErrorStore,
    setIsCheckMaxDiscountStore,
    setIsCheckMinSpendStore,
    setIsExistedCodeStore,
    isExistedCodeStore,
  } = useCouponStore();

  const { data, refetch } = useCouponsServiceCouponControllerCheckCoupon(
    {
      code: searchValue,
    },
    ['useCouponsServiceCouponControllerCheckCoupon'],
    {
      enabled: false,
    },
  );

  useEffect(() => {
    if (searchValue) {
      refetch();
    } else {
      setIsExistedCodeStore(false);
    }
  }, [searchValue]);

  return {
    data,
    hasAvailableStore,
    isExistedCodeStore,
    isShowErrorStore,
    paymentMethodTreeData,
    search,
    setIsCheckMaxDiscountStore,
    setIsCheckMinSpendStore,
    setIsExistedCodeStore,
    setSearch,
    setVehicles,
    vehicleList,
    vehicles,
  };
}
