/* eslint-disable @typescript-eslint/no-unnecessary-condition */
import { FormInput } from '#/shared/components/formField/FormInput';
import type { BaseFilter } from '#/shared/hooks';
import { Button, Col, Row, Spin } from 'antd';
import { type UseFormReturn } from 'react-hook-form';
import type { HistoryBookingOfCustomerFilter } from '../../hooks/business-history-bookings/useHistoryBookingOfCustomerTable';
import { FormRangeDatePicker } from '#/shared/components/formField/FormRangeDatePicker';
import { FilterWrapperUI } from '#/shared/components/FilterWrapperUI';
import { useTranslation } from 'react-i18next';
import { FormBusinessSelector } from '#/shared/components/selectors/Business/BusinessSelector';
import { TypographyUI } from '#/shared/components/TypographyUI';
import { BookingStatusSelector } from '#/shared/components/selectors/BookingStatus/BookingStatusSelector';
import { useEffect, useState } from 'react';
import dayjs from 'dayjs';
import useExportBooking from '../../hooks/business-history-bookings/useExportBooking';
import { ServiceSelector } from '#/shared/components/selectors/Service/ServiceSelector';
import { useDebounce } from 'use-debounce';
import { isUndefined } from 'lodash-es';
import { CollapseMenu } from '#/shared/components/CollapseMenu';
import { CancelReason } from '#/api/requests';
import { FormEntitySelector } from '#/shared/components/selectors/FormEntitySelector';
import './styles.scss';
import { ExportButton } from '#/shared/components/Button/ExportButton';
import { useZoneBookingFilter } from '../../hooks/individual-history-booking/useZoneBookingFilter';

interface Props {
  form: UseFormReturn<BaseFilter & HistoryBookingOfCustomerFilter>;
}

export function BookingOfAllBusinessFilter({ form }: Props) {
  const { t } = useTranslation();
  const { refetch, isLoading } = useExportBooking({
    type: 'BUSINESS',
  });

  const { zoneOptions } = useZoneBookingFilter();
  const savedData = localStorage.getItem('businessFilter');

  const [formData, setFormData] = useState(
    savedData ? JSON.parse(savedData) : undefined,
  );
  const [filterValue] = useDebounce(formData, 350);

  const q = formData?.q || '';
  const rangeDate = formData?.rangeDate || [];
  const statuses = formData?.statuses || '';
  const businessIds = formData?.businessIds || [];
  const vehicleTypes = formData?.vehicleTypes || [];
  const reason = formData?.reason || [];
  const zones = formData?.zones || [];

  const showClearFilter =
    q ||
    rangeDate.length > 0 ||
    statuses ||
    businessIds.length > 0 ||
    vehicleTypes?.length > 0 ||
    reason?.length > 0 ||
    zones?.length > 0;

  const onClearFilter = () => {
    setFormData({});
    form.setValue('q', '');
    form.setValue('rangeDate', '');
    form.setValue('statuses', undefined);
    form.setValue('businessIds', []);
    form.setValue('vehicleTypes', []);
    form.setValue('reason', []);
    form.setValue('zones', []);
    localStorage.removeItem('businessFilter');
  };

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleChange = (fieldName: string, value: any) => {
    const updatedData = { ...formData, [fieldName]: value };
    setFormData(updatedData);
  };

  const onHandleExport = () => {
    refetch();
  };

  useEffect(() => {
    if (!isUndefined(filterValue))
      localStorage.setItem('businessFilter', JSON.stringify(filterValue));
  }, [filterValue]);

  return (
    <Spin spinning={isLoading}>
      <FilterWrapperUI
        addOnActions={<ExportButton onExport={onHandleExport} />}
        filters={
          <Row className="flex gap-2">
            <Col>
              <FormInput
                className="w-60 h-8"
                defaultValue={formData?.q}
                form={form}
                label={null}
                name="q"
                onChange={event => {
                  handleChange('q', event.target.value);
                }}
                onKeyDown={(e: React.KeyboardEvent<HTMLInputElement>) => {
                  if (e.key === ' ' && q.length === 0) {
                    e.preventDefault();
                  }
                }}
                placeholder={t('bookingHistory.searchCustomer')}
                size="middle"
              />
            </Col>

            <FormBusinessSelector
              className="w-60"
              defaultValue={formData?.businessIds}
              form={form}
              label={null}
              maxTagCount={'responsive'}
              mode="multiple"
              name="businessIds"
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
              onChange={(_: string, option: any) => {
                handleChange(
                  'businessIds',
                  option.map((item: { label: string; value: string }) => item),
                );
              }}
              placeholder="Business"
            />

            <FormRangeDatePicker
              className="sm:w-60 w-[276px]"
              defaultValue={[
                formData?.rangeDate?.[0]
                  ? dayjs(formData?.rangeDate[0])
                      .tz()
                      .startOf('day')
                  : null,
                formData?.rangeDate
                  ? dayjs(formData?.rangeDate[1])
                      .tz()
                      .endOf('day')
                  : null,
              ]}
              form={form}
              label={null}
              name="rangeDate"
              onCalendarChange={values => {
                if (values?.[0] && values?.[1]) {
                  handleChange('rangeDate', [
                    dayjs(values[0]).tz().startOf('day').format().toString(),
                    dayjs(values[1]).tz().endOf('day').format().toString(),
                  ]);
                }
              }}
              onClear={() => {
                form.setValue('rangeDate', '');
                handleChange('rangeDate', undefined);
              }}
            />
            <BookingStatusSelector
              className="min-w-[180px] w-60 h-8"
              defaultValue={formData?.statuses}
              form={form}
              name="statuses"
              onChange={(value: string) => {
                handleChange('statuses', value);
              }}
            />

            <CollapseMenu>
              <ServiceSelector
                className="text-sm w-60 h-8"
                defaultValue={formData?.vehicleTypes}
                form={form}
                hasAnyCar={true}
                mode="multiple"
                name="vehicleTypes"
                onChange={(value: string) => {
                  handleChange('vehicleTypes', value);
                }}
              />
              <FormEntitySelector
                allowClear
                className="w-[300px] xl:mt-2 mt-0"
                defaultValue={formData?.reason}
                form={form}
                label={null}
                maxTagCount={1}
                mode="multiple"
                name="reason"
                onChange={(value: string) => {
                  handleChange('reason', value);
                }}
                options={[
                  {
                    label: t('cancel.DRIVER_ARRIVED_EARLY'),
                    value: CancelReason.reason.DRIVER_ARRIVED_EARLY,
                  },
                  {
                    label: t('cancel.DRIVER_ASKED_CANCEL'),
                    value: CancelReason.reason.DRIVER_ASKED_CANCEL,
                  },
                  {
                    label: t('cancel.DRIVER_NOT_GETTING_CLOSER'),
                    value: CancelReason.reason.DRIVER_NOT_GETTING_CLOSER,
                  },
                  {
                    label: t('cancel.COULD_NOT_FIND_DRIVER'),
                    value: CancelReason.reason.COULD_NOT_FIND_DRIVER,
                  },
                  {
                    label: t('cancel.WAIT_TIME_TOO_LONG'),
                    value: CancelReason.reason.WAIT_TIME_TOO_LONG,
                  },
                  {
                    label: t('cancel.DRIVER_DELAYED'),
                    value: CancelReason.reason.DRIVER_DELAYED,
                  },
                  {
                    label: t('cancel.DRIVER_NOT_MOVING'),
                    value: CancelReason.reason.DRIVER_NOT_MOVING,
                  },
                  {
                    label: t('cancel.INCORRECT_ADDRESS'),
                    value: CancelReason.reason.INCORRECT_ADDRESS,
                  },
                  {
                    label: t('cancel.CANCELLED_BY_CENTER'),
                    value: CancelReason.reason.CANCELLED_BY_CENTER,
                  },
                  {
                    label: t('cancel.AUTO_CANCELLED'),
                    value: CancelReason.reason.AUTO_CANCELLED,
                  },
                  {
                    label: t('cancel.OTHER'),
                    value: CancelReason.reason.OTHER,
                  },
                ]}
                placeholder={t('bookingHistory.table.cancelReason')}
                showSearch
                size="middle"
              />

              <FormEntitySelector
                allowClear
                className="w-[290px]"
                defaultValue={formData?.zones}
                form={form}
                maxTagCount={2}
                mode="multiple"
                name="zones"
                onChange={value => {
                  handleChange('zones', value);
                }}
                options={zoneOptions}
                placeholder={t('bookingHistory.table.filter.zone')}
                size="middle"
              />
            </CollapseMenu>
            {showClearFilter ? (
              <Button
                className="px-2 ml-2 bg-white"
                onClick={onClearFilter}
                size="middle"
                type="text"
              >
                <TypographyUI font="Semibold" type="BodySm">
                  {t('common.button.clearFilter')}
                </TypographyUI>
              </Button>
            ) : null}
          </Row>
        }
      />
    </Spin>
  );
}
