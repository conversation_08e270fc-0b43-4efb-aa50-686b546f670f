/* eslint-disable no-nested-ternary */
import { useSupportTicketServiceSupportTicketAdminControllerGetListSupportProgressOfBooking } from '#/api/queries';
import { SupportProgressEntity } from '#/api/requests';
import BaseButton from '#/shared/components/Button/BaseButton';
import { formatter } from '#/shared/utils';
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  QuestionCircleOutlined,
} from '@ant-design/icons';
import { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { NoDataIcon } from '#/assets/svg';
import useBookingHistoryStore from '#/store/useBookingHistoryStore';
import { Avatar } from 'antd';
import { useTranslation } from 'react-i18next';

interface SupportProgressItemProps {
  item: SupportProgressEntity;
  supportStatusFormatted: {
    label: string;
    status: SupportProgressEntity.progressType;
  }[];
}

function SupportProgressItem({
  item,
  supportStatusFormatted,
}: SupportProgressItemProps) {
  const { i18n, t } = useTranslation();

  const {
    ticket,
    description,
    descriptionFr,
    refund,
    request,
    progressType,
    userResolvedBy,
    createdAt,
    booking,
  } = item;

  return (
    <div className="flex gap-4 py-4 items-start">
      <div className="mt-1">
        {progressType === SupportProgressEntity.progressType.NEW ? (
          <QuestionCircleOutlined style={{ color: '#FFCD36' }} />
        ) : progressType === SupportProgressEntity.progressType.SOLVED ||
          progressType === SupportProgressEntity.progressType.SUCCESSFULLY ? (
          <CheckCircleOutlined style={{ color: '#007AFF' }} />
        ) : (
          <CloseCircleOutlined style={{ color: '#fe353d' }} />
        )}
      </div>
      <div className="flex flex-col">
        <div className="text-sm font-medium">
          {ticket?.ticketCode ? (
            <>
              {`[${t('support.ticket')}`}
              <span className="text-info-color ml-1">{`#${ticket?.ticketCode}`}</span>
              <span className="mr-1">{`]`}</span>
            </>
          ) : null}
          {
            supportStatusFormatted.find(item => item.status === progressType)
              ?.label
          }
        </div>
        <div className="text-xs text-neutral-875 mt-2">
          {i18n.language === 'en' ? description : descriptionFr}
        </div>
        <div>
          {request ? (
            <div className="text-neutral-875 text-xs">
              <div>
                {request.issueType ? (
                  <div className="mt-1">
                    {t('support.type')}:{' '}
                    {t(`support.supportType.${request.issueType}`)}
                  </div>
                ) : null}
              </div>
              <div>
                {request.note ? (
                  <div className="mt-1">
                    {t('support.note')}:{' '}
                    {formatter.normalizeFirstEnumString(request.note)}
                  </div>
                ) : null}
              </div>
            </div>
          ) : null}
        </div>
        <div>
          {refund ? (
            <div className="text-neutral-875 text-xs">
              <div className="mt-1">
                {t('support.type')}:{' '}
                {t(`support.reasonType.${refund.reasonType}`)}
              </div>
              {refund.reasonNote ? (
                <div className="mt-1">
                  {t('support.reason')}:{' '}
                  {formatter.normalizeFirstEnumString(refund.reasonNote)}
                </div>
              ) : null}
              <BaseButton
                className="p-0 h-6 text-xs mb-[-4px]"
                content={t('common.button.viewMore')}
                onClick={() => {
                  window.open(
                    formatter.getPaymentUrl(booking.bookingPaymentId),
                    '_blank',
                  );
                }}
                type="link"
              />
            </div>
          ) : null}
        </div>
        <div className="text-neutral-600 text-xs flex items-center mt-1">
          {formatter.formatDaytime(createdAt, 'DD/MM/YYYY, HH:mm')}
          {userResolvedBy ? (
            <div className="flex items-center ml-1">
              {t('support.by')}
              <div>
                {userResolvedBy.avatarUrl ? (
                  <Avatar
                    alt="avatar"
                    className="w-6 h-6 ml-1"
                    src={userResolvedBy.avatarUrl}
                  >
                    <span className="ml-1">
                      {userResolvedBy.fullname.slice(0, 1)}
                    </span>
                  </Avatar>
                ) : null}
              </div>
              <span className="ml-1 font-semibold">
                {userResolvedBy.fullname}
              </span>
            </div>
          ) : null}
        </div>
      </div>
    </div>
  );
}

export default function SupportProgress() {
  const params = useParams();
  const { t } = useTranslation();

  const supportStatusFormatted = [
    {
      label: t('support.newRequest'),
      status: SupportProgressEntity.progressType.NEW,
    },
    {
      label: t('support.refundPending'),
      status: SupportProgressEntity.progressType.PENDING,
    },
    {
      label: t('support.solved'),
      status: SupportProgressEntity.progressType.SOLVED,
    },
    {
      label: t('support.successfullyRefunded'),
      status: SupportProgressEntity.progressType.SUCCESSFULLY,
    },
    {
      label: t('support.refundFailed'),
      status: SupportProgressEntity.progressType.FAILED,
    },
    {
      label: t('support.refundCanceled'),
      status: SupportProgressEntity.progressType.CANCELED,
    },
  ];

  const { isRefetchSupportProgress, setIsRefetchSupportProgress } =
    useBookingHistoryStore();
  const bookingId = params.id || '';
  const [supportProgress, setSupportProgress] = useState<
    SupportProgressEntity[]
  >([]);

  const { data, refetch } =
    useSupportTicketServiceSupportTicketAdminControllerGetListSupportProgressOfBooking(
      {
        bookingId,
        limit: 20,
        offset: 0,
      },
    );

  useEffect(() => {
    setSupportProgress((data?.data?.items as SupportProgressEntity[]) || []);
  }, [data]);

  useEffect(() => {
    if (isRefetchSupportProgress) {
      refetch();
      setIsRefetchSupportProgress(false);
    }
  }, [isRefetchSupportProgress]);

  return (
    <>
      <div className="text-lg font-semibold">
        {t('support.supportProgress')}
      </div>
      <span>
        {supportProgress?.length > 0 ? (
          supportProgress
            ?.sort((a, b) => (a.createdAt < b.createdAt ? 1 : -1))
            ?.map((item, index) => (
              <div key={index}>
                <SupportProgressItem
                  item={item}
                  supportStatusFormatted={supportStatusFormatted}
                />
              </div>
            ))
        ) : (
          <div className="flex flex-col mt-20 px-4 justify-center items-center">
            <img alt="" className="w-[20%]" src={NoDataIcon} />
            <span className="mt-5 text-base">
              {t('support.noSupportTicket')}
            </span>
          </div>
        )}
      </span>
    </>
  );
}
