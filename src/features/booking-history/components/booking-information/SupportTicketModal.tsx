import type { SupportTicketEntity } from '#/api/requests';
import BaseButton from '#/shared/components/Button/BaseButton';
import { DescriptionUI } from '#/shared/components/DescriptionUI/DescriptionUI';
import { ModalUI } from '#/shared/components/modalUI/ModalUI';
import { TypographyUI } from '#/shared/components/TypographyUI';
import { formatter } from '#/shared/utils';
import { Spin, Tag } from 'antd';
import { Note } from 'iconsax-react';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ConfirmSolveTicketModal } from './ConfirmSolveTicketModal';
import usePermissionCheck from '#/shared/hooks/usePermissionCheck';

interface Props {
  visible: boolean;
  onCancel: () => void;
  ticketList: SupportTicketEntity[];
  loading: boolean;
  refetchTicketList: () => void;
}

interface SupportTicketItemProps {
  ticket: SupportTicketEntity;
  onOpenConfirmModal?: () => void;
  isShowSolveButton?: boolean;
}

export function SupportTicketItem({
  ticket,
  onOpenConfirmModal,
  isShowSolveButton = true,
}: SupportTicketItemProps) {
  const { t } = useTranslation();
  const { hasSolveSupportTicket } = usePermissionCheck();

  const {
    ticketCode,
    issueType,
    createdAt,
    supportNote,
    isSolved,
    resolvedNote,
  } = ticket;

  return (
    <div className="border border-dashed rounded border-[#D4D3D0] p-4 bg-neutral-50">
      <div className="flex justify-between mb-4">
        <div className="flex gap-2">
          <p className="text-sm text-[#3371FF] font-semibold">#{ticketCode}</p>
          <p className="text-sm font-semibold">
            {t(`support.supportType.${issueType}`)}
          </p>
          {'-'}
          <p className="text-sm font-semibold">
            {formatter.formatDaytime(createdAt, 'DD/MM/YYYY, HH:mm')}
          </p>
        </div>
        {isSolved ? (
          <Tag className="text-success-color" color="#EAF9EE">
            {t('bookingHistory.detail.supportTicket.solved')}
          </Tag>
        ) : (
          <BaseButton
            className={`${
              isShowSolveButton && hasSolveSupportTicket ? 'block' : 'hidden'
            }`}
            content={t('bookingHistory.detail.supportTicket.markAsSolved')}
            onClick={onOpenConfirmModal}
          />
        )}
      </div>
      <DescriptionUI
        items={[
          {
            icon: <Note className="text-neutral-3 min-w-[22px]" size={20} />,
            title: (
              <TypographyUI font="Semibold" type="BodySm">
                {t('bookingHistory.detail.supportTicket.supportNote')}
              </TypographyUI>
            ),
            value: <span>{supportNote}</span>,
          },
          ...(isSolved
            ? [
                {
                  icon: (
                    <Note className="text-neutral-3 min-w-[22px]" size={20} />
                  ),
                  title: (
                    <TypographyUI font="Semibold" type="BodySm">
                      {t('bookingHistory.detail.supportTicket.solvedNote')}
                    </TypographyUI>
                  ),
                  value: <span>{resolvedNote}</span>,
                },
              ]
            : []),
        ]}
      />
    </div>
  );
}

export function SupportTicketModal({
  visible,
  onCancel,
  ticketList,
  loading,
  refetchTicketList,
}: Props) {
  const { t } = useTranslation();
  const [isOpenConfirmModal, setIsOpenConfirmModal] = useState(false);
  const [ticketId, setTicketId] = useState('');

  const onOpenConfirmModal = (id: string) => {
    setTicketId(id);
    setIsOpenConfirmModal(true);
  };

  return (
    <>
      <ModalUI
        className="w-[680px]"
        footer={null}
        onCancel={onCancel}
        open={visible}
        title={t('bookingHistory.detail.supportTicket.title')}
      >
        <Spin spinning={loading}>
          {ticketList?.map(item => (
            <div className="py-2" key={item.id}>
              <SupportTicketItem
                onOpenConfirmModal={() => {
                  onOpenConfirmModal(item.id);
                }}
                ticket={item}
              />
            </div>
          ))}
        </Spin>
      </ModalUI>
      <ConfirmSolveTicketModal
        onCancel={() => setIsOpenConfirmModal(false)}
        onSuccess={refetchTicketList}
        ticketId={ticketId}
        visible={isOpenConfirmModal}
      />
    </>
  );
}
