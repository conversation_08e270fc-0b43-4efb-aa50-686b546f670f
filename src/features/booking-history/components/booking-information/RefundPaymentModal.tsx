/* eslint-disable @typescript-eslint/naming-convention */
import { useSupportTicketServiceSupportTicketAdminControllerCreateRefundPayment } from '#/api/queries';
import { BookingEntity, CreateRefundPaymentDto } from '#/api/requests';
import { ErrorMessage } from '#/shared/components/formField/ErrorMessage';
import { FormInputArea } from '#/shared/components/formField/FormInputArea';
import { FormInputNumber } from '#/shared/components/formField/FormInputNumber';
import { ModalUI } from '#/shared/components/modalUI/ModalUI';
import { FormEntitySelector } from '#/shared/components/selectors/FormEntitySelector';
import { TypographyUI } from '#/shared/components/TypographyUI';
import { DEFAULT_UINT_DIVIDED_BY_CENT } from '#/shared/constants';
import { formatter, showErrorMessage } from '#/shared/utils';
import { yupResolver } from '@hookform/resolvers/yup';
import { notification } from 'antd';
import { type valueType } from 'antd/es/statistic/utils';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import type { ObjectSchema } from 'yup';
import { refundSchema } from '../../schemas/refund.schema';
import useBookingHistoryStore from '#/store/useBookingHistoryStore';

interface Props {
  bookingInfo: BookingEntity;
  visible: boolean;
  onCancel: () => void;
  refetch: () => void;
}

export function RefundPaymentModal({
  visible,
  onCancel,
  bookingInfo,
  refetch,
}: Props) {
  const { t } = useTranslation();
  const { setIsRefetchSupportProgress } = useBookingHistoryStore();

  const [amount, setAmount] = useState<number | undefined>();
  const [hasAmountError, setHasAmountError] = useState<boolean>(false);
  const [isShowReasonNote, setIsShowReasonNote] = useState<boolean>(false);
  const [invalidRefundError, setInvalidRefundError] = useState<string>('');
  const maxRefundAmount =
    bookingInfo.amount +
    (bookingInfo?.tipAmount ?? 0) -
    (bookingInfo?.refundAmount ?? 0);

  const form = useForm<CreateRefundPaymentDto>({
    resolver: yupResolver(
      refundSchema as unknown as ObjectSchema<CreateRefundPaymentDto>,
    ),
  });

  const reasonTypeOption = [
    {
      label: t(`support.reasonType.DUPLICATE`),
      value: CreateRefundPaymentDto.reasonType.DUPLICATE,
    },
    {
      label: t(`support.reasonType.FRAUDULENT`),
      value: CreateRefundPaymentDto.reasonType.FRAUDULENT,
    },
    {
      label: t(`support.reasonType.REQUESTED_BY_CUSTOMER`),
      value: CreateRefundPaymentDto.reasonType.REQUESTED_BY_CUSTOMER,
    },
    {
      label: t(`support.reasonType.OTHER`),
      value: CreateRefundPaymentDto.reasonType.OTHER,
    },
  ];

  const onHandleCancelRefund = () => {
    onCancel();
    form.reset();
    setHasAmountError(false);
    setIsShowReasonNote(false);
  };

  const { isPending, mutate } =
    useSupportTicketServiceSupportTicketAdminControllerCreateRefundPayment({
      onError: showErrorMessage,
      onSuccess: () => {
        notification.success({
          description: t(
            'bookingHistory.detail.refundPayment.notification.description',
          ),
          message: t(
            'bookingHistory.detail.refundPayment.notification.message',
          ),
        });
        onHandleCancelRefund();
        setTimeout(() => {
          refetch();
          setIsRefetchSupportProgress(true);
        }, 2000);
      },
    });

  const onCreateRefund = () => {
    setHasAmountError(true);
    form.handleSubmit(formData => {
      if (formData.amount === 0) {
        setInvalidRefundError(
          t('bookingHistory.detail.refundPayment.refundAmountLessThanZero'),
        );
        return;
      }

      if (formData.amount * 100 > maxRefundAmount) {
        setInvalidRefundError(
          t('bookingHistory.detail.refundPayment.invalidRefundError', {
            value: formatter.currencyFormat(maxRefundAmount),
          }),
        );
        return;
      }

      mutate({
        bookingId: bookingInfo?.id,
        requestBody: {
          ...formData,
          amount:
            Math.round(formData.amount * DEFAULT_UINT_DIVIDED_BY_CENT * 100) /
            100,
        },
      });
    })();
  };

  return (
    <ModalUI
      destroyOnClose
      okButtonProps={{
        loading: isPending,
      }}
      okText={t('bookingHistory.detail.refund.title')}
      onCancel={onHandleCancelRefund}
      onOk={onCreateRefund}
      open={visible}
      title={t('bookingHistory.detail.refundPayment.title')}
    >
      <div className="mb-2">
        <span
          className="text-xs"
          dangerouslySetInnerHTML={{
            __html: t('bookingHistory.detail.refund.tripCost', {
              value: formatter.currencyFormat(bookingInfo.amount),
            }),
          }}
        />{' '}
        {bookingInfo?.tipAmount && bookingInfo.tipAmount > 0 ? (
          <span
            className="text-xs"
            dangerouslySetInnerHTML={{
              __html: t('bookingHistory.detail.refund.tipAmount', {
                value: formatter.currencyFormat(bookingInfo.tipAmount),
              }),
            }}
          />
        ) : null}
      </div>
      <FormInputNumber
        className="w-full"
        form={form}
        label={t('bookingHistory.detail.refund.title')}
        name="amount"
        onChange={(value: valueType | null) => {
          setAmount(value as unknown as number);
          setInvalidRefundError('');
          if (!value) setHasAmountError(true);
        }}
        onKeyDown={evt =>
          ['e', 'E', '+', '-'].includes(evt.key) && evt.preventDefault()
        }
        placeholder={t('bookingHistory.detail.refundPayment.amountPlaceholder')}
        showRequired
        type="number"
      />
      {invalidRefundError ? (
        <ErrorMessage>{invalidRefundError}</ErrorMessage>
      ) : null}
      {bookingInfo?.refundAmount ? (
        <p
          className={`bg-warning-color-soft text-warning-color h-8 flex items-center px-2 rounded text-xs ${
            (!amount && hasAmountError) || invalidRefundError ? 'mt-0' : 'mt-2'
          }`}
        >
          <TypographyUI className="mr-1" font="Bold" type="Caption1">
            {t('bookingHistory.detail.refundPayment.note')}:
          </TypographyUI>
          {bookingInfo?.refundTag === BookingEntity.refundTag.REFUNDED ? (
            <>{t('bookingHistory.detail.refundPayment.fullyRefunded')}</>
          ) : (
            <>
              {t('bookingHistory.detail.refundPayment.partiallyRefunded', {
                value: formatter.currencyFormat(maxRefundAmount),
              })}
            </>
          )}
        </p>
      ) : (
        <></>
      )}
      <div className="mt-3">
        <FormEntitySelector
          allowClear={true}
          className="w-full"
          form={form}
          label={t('bookingHistory.detail.refundPayment.reason')}
          name="reasonType"
          onChange={() => {
            setIsShowReasonNote(true);
            form.clearErrors('reasonNote');
          }}
          options={reasonTypeOption}
          placeholder={t(
            'bookingHistory.detail.refundPayment.reasonPlaceholder',
          )}
          showRequired
        />
      </div>
      {isShowReasonNote ? (
        <div className="mt-3">
          <FormInputArea
            form={form}
            label={null}
            name="reasonNote"
            placeholder={t('bookingHistory.detail.refundPayment.refundNote')}
          />
        </div>
      ) : (
        <></>
      )}
    </ModalUI>
  );
}
