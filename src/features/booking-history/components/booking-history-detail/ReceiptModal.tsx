import { BookingEntity } from '#/api/requests';
import { ModalUI } from '#/shared/components/modalUI/ModalUI';
import { IconKey } from '#/shared/constants/iconKey';
import { formatter } from '#/shared/utils';
import { Icon } from '@iconify/react';
import { Button, Divider } from 'antd';
import { Card as CardIcon } from 'iconsax-react';
import { useTranslation } from 'react-i18next';
import { ReceiptDescription } from './ReceiptDescription';
import { ReceiptModalTitle } from './ReceiptModalTitle';

interface Props {
  bookingInfo: BookingEntity;
  subTotal: number;
  total: number;
  tpsValue: number;
  tvqValue: number;
  handleGeneratePdf: () => void;
  isShowReceipt: boolean;
  onHandleClickPDF: () => void;
  setIsShowReceipt: (value: boolean) => void;
}

export function ReceiptModal({
  bookingInfo,
  subTotal,
  total,
  tpsValue,
  tvqValue,
  handleGeneratePdf,
  isShowReceipt,
  onHandleClickPDF,
  setIsShowReceipt,
}: Readonly<Props>) {
  const { t } = useTranslation();

  const renderFooter = () => (
    <div className="flex justify-between">
      <div
        className={`${
          bookingInfo.status === BookingEntity.status.COMPLETED
            ? 'block'
            : 'invisible'
        }`}
      >
        <Button
          className="bg-error-color-soft text-error-color"
          icon={<Icon icon={IconKey.Pdf} width={20} />}
          onClick={onHandleClickPDF}
          size="middle"
          type="text"
        />
        <Button
          className="bg-warning-color-soft text-warning-color"
          icon={<Icon icon={IconKey.Printer} width={20} />}
          onClick={handleGeneratePdf}
          size="middle"
          type="text"
        />
      </div>
      <Button
        className="font-semibold"
        onClick={() => setIsShowReceipt(false)}
        type="text"
      >
        {t('common.button.close')}
      </Button>
    </div>
  );

  const renderRefundSection = () => {
    if (
      bookingInfo?.paymentMethodType !== BookingEntity.paymentMethodType.CARD ||
      bookingInfo?.status !== BookingEntity.status.COMPLETED
    ) {
      return null;
    }

    return (
      <>
        <div className="-m-2">
          <Divider className="my-3" />
          {bookingInfo?.refundAmount !== 0 ? (
            <div
              className={`${
                bookingInfo?.refundTag === BookingEntity.refundTag.REFUNDED
                  ? 'bg-success-color-soft'
                  : 'bg-warning-color-soft'
              } p-2`}
            >
              <div className="flex items-center rounded text-xs">
                {bookingInfo?.refundTag === BookingEntity.refundTag.REFUNDED ? (
                  <div className="text-success-color">
                    {t('bookingHistory.detail.refund.fullyRefunded')}
                  </div>
                ) : (
                  <div className="text-warning-color">
                    {t('bookingHistory.detail.refund.partiallyRefunded')}
                  </div>
                )}
              </div>
              <div className="py-2 font-semibold flex justify-between">
                {t('bookingHistory.table.refundAmount')}
                <div>{formatter.currencyFormat(bookingInfo?.refundAmount)}</div>
              </div>
            </div>
          ) : null}
        </div>
        <div className="mt-3">
          <ReceiptDescription
            bookingStatus={bookingInfo.status}
            name={t('bookingHistory.detail.receipt.stripeFee')}
            value={`-${formatter.currencyFormat(bookingInfo.stripeFees)}`}
          />
        </div>
      </>
    );
  };

  const renderCancelledStripeFees = () => {
    if (
      bookingInfo.status !== BookingEntity.status.CANCELLED ||
      bookingInfo?.paymentMethodType !== BookingEntity.paymentMethodType.CARD ||
      !bookingInfo.stripeFees
    ) {
      return null;
    }

    return (
      <>
        <Divider className="mt-3 mb-0" />
        <ReceiptDescription
          bookingStatus={BookingEntity.status.COMPLETED}
          name={t('bookingHistory.detail.receipt.penaltyFee')}
          value={`-${formatter.currencyFormat(bookingInfo.stripeFees)}`}
        />
      </>
    );
  };

  return (
    <ModalUI
      className="xxl:w-[60%] w-[40%] custom-receipt-modal"
      closable={false}
      footer={renderFooter()}
      hideCloseIcon={true}
      open={isShowReceipt}
      title={<ReceiptModalTitle bookingInfo={bookingInfo} />}
    >
      <ReceiptDescription
        bookingStatus={bookingInfo.status}
        name={`${t('bookingHistory.detail.receipt.tripFare')} (${
          bookingInfo?.fixedFare
            ? t('fixedFare.title')
            : bookingInfo?.tripFare?.name
        })`}
        value={
          bookingInfo?.fixedFare
            ? bookingInfo?.fixedFare?.amount
            : bookingInfo?.tripFareFee
        }
      />
      {!bookingInfo?.fixedFare &&
        bookingInfo?.serviceFees?.map((serviceFee, index) => (
          <ReceiptDescription
            bookingStatus={bookingInfo.status}
            key={index}
            name={formatter.normalizeFirstEnumString(serviceFee.name)}
            value={serviceFee.price}
          />
        ))}
      <Divider className="mt-2 mb-1" />
      {/* Sub total */}
      <ReceiptDescription
        bookingStatus={bookingInfo.status}
        highlight
        name={t('bookingHistory.detail.receipt.subTotal')}
        value={
          bookingInfo?.fixedFare ? bookingInfo?.fixedFare?.amount : subTotal
        }
      />
      <ReceiptDescription
        bookingStatus={bookingInfo.status}
        name={t('bookingHistory.detail.receipt.tps')}
        value={bookingInfo?.fixedFare ? bookingInfo?.tps : tpsValue}
      />
      <ReceiptDescription
        bookingStatus={bookingInfo.status}
        name={t('bookingHistory.detail.receipt.tvq')}
        value={bookingInfo?.fixedFare ? bookingInfo?.tvq : tvqValue}
      />
      <Divider className="mt-3 mb-0" />
      {/* Total */}
      <ReceiptDescription
        bookingStatus={bookingInfo.status}
        highlight
        name={t('bookingHistory.detail.receipt.total')}
        value={total}
      />
      {/* Promo code */}
      {bookingInfo?.couponAmount ? (
        <ReceiptDescription
          bookingStatus={bookingInfo.status}
          className={`${
            bookingInfo?.status === BookingEntity.status.CANCELLED
              ? 'text-neutral-400'
              : 'text-[#088752]'
          }`}
          name={
            <div className="flex gap-2">
              <span>{t('coupon.create.promotionCode.title')}</span>
              <span className="text-info-color">
                {bookingInfo?.coupon?.code || bookingInfo?.coupon?.title}
              </span>
            </div>
          }
          value={`-${formatter.currencyFormat(bookingInfo?.couponAmount)}`}
        />
      ) : null}
      {/* Amount charged */}
      <div className={`flex justify-between pt-2`}>
        <div className="flex gap-2">
          <span>{t('bookingHistory.detail.receipt.amountCharged')}</span>
          {bookingInfo?.status == BookingEntity.status.CANCELLED && (
            <span className="text-[#F7BA1E] font-medium">
              {bookingInfo.isCancelAfterChargeTime
                ? t('bookingHistory.table.cancelledAfter5Mins')
                : t(
                    'bookingHistory.detail.bookingInformation.freeCancellation',
                  )}
            </span>
          )}
        </div>
        <div className="flex gap-1 font-semibold">
          <div
            className={`${
              bookingInfo?.status === BookingEntity.status.CANCELLED
                ? 'text-neutral-400'
                : ''
            }`}
          >
            {formatter.currencyFormat(
              (total ?? 0) - (bookingInfo?.couponAmount ?? 0),
            )}
          </div>
          {bookingInfo?.status === BookingEntity.status.CANCELLED && (
            <div>
              {bookingInfo?.isCancelAfterChargeTime
                ? formatter.currencyFormat(bookingInfo?.amount)
                : formatter.currencyFormat(0)}
            </div>
          )}
        </div>
      </div>
      <div className="mt-2">
        {bookingInfo.paymentMethodType ===
        BookingEntity.paymentMethodType.CARD ? (
          <div className="flex items-center gap-2">
            <CardIcon />
            {t('common.paymentMethod.card')}
          </div>
        ) : (
          <div className="flex items-center gap-2">
            <Icon height={16} icon={IconKey.Cash} width={28} />
            {t('common.paymentMethod.cash')}
          </div>
        )}
      </div>
      {bookingInfo.tipAmount ? (
        <ReceiptDescription
          bookingStatus={bookingInfo.status}
          name={
            <>
              {t('bookingHistory.detail.receipt.tipAmount')}{' '}
              {`(${
                bookingInfo?.tipPaymentId
                  ? t('common.paymentMethod.card')
                  : t('common.paymentMethod.cash')
              })`}
            </>
          }
          value={bookingInfo.tipAmount}
        />
      ) : null}
      {/* Taxi loyal split */}
      {bookingInfo?.topUpAmount && bookingInfo.topUpAmount > 0 ? (
        <ReceiptDescription
          bookingStatus={bookingInfo.status}
          name={
            <div>
              {t('coupon.create.taxiLoyalSplit.title')}
              {` (${bookingInfo?.coupon?.topUpPercent}%)`}
            </div>
          }
          value={bookingInfo?.topUpAmount}
        />
      ) : null}
      {/* Refund amount */}
      {renderRefundSection()}

      {bookingInfo.tipStripeFees ? (
        <ReceiptDescription
          bookingStatus={bookingInfo.status}
          name={t('bookingHistory.detail.receipt.tipStripeFee')}
          value={`-${formatter.currencyFormat(bookingInfo.tipStripeFees)}`}
        />
      ) : null}
      {renderCancelledStripeFees()}
      <Divider className="mt-3 mb-0" />
      <div className="font-semibold flex justify-between pt-2">
        <div>{t('bookingHistory.detail.receipt.amountForDriver')}</div>
        <div>
          {bookingInfo?.amountDriver && bookingInfo.amountDriver < 0
            ? 0
            : formatter.currencyFormat(bookingInfo?.amountDriver)}
        </div>
      </div>
    </ModalUI>
  );
}
