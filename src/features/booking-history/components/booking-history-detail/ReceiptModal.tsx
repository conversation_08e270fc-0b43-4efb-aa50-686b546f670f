import { BookingEntity } from '#/api/requests';
import { ModalUI } from '#/shared/components/modalUI/ModalUI';
import { TypographyUI } from '#/shared/components/TypographyUI';
import { IconKey } from '#/shared/constants/iconKey';
import { formatter } from '#/shared/utils';
import { Icon } from '@iconify/react';
import { Button, Divider, notification } from 'antd';
import copy from 'copy-to-clipboard';
import { Card as CardIcon, Copy } from 'iconsax-react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { ReceiptDescription } from './ReceiptDescription';

interface Props {
  bookingInfo: BookingEntity;
  subTotal: number;
  total: number;
  tpsValue: number;
  tvqValue: number;
  handleGeneratePdf: () => void;
  isShowReceipt: boolean;
  onHandleClickPDF: () => void;
  setIsShowReceipt: (value: boolean) => void;
}

export function ReceiptModal({
  bookingInfo,
  subTotal,
  total,
  tpsValue,
  tvqValue,
  handleGeneratePdf,
  isShowReceipt,
  onHandleClickPDF,
  setIsShowReceipt,
}: Readonly<Props>) {
  const { t, i18n } = useTranslation();

  return (
    <ModalUI
      className="xxl:w-[60%] w-[40%] custom-receipt-modal"
      closable={false}
      footer={
        <div className="flex justify-between">
          <div
            className={`${
              bookingInfo.status === BookingEntity.status.COMPLETED
                ? 'block'
                : 'invisible'
            }`}
          >
            <Button
              className="bg-error-color-soft text-error-color"
              icon={<Icon icon={IconKey.Pdf} width={20} />}
              onClick={onHandleClickPDF}
              size="middle"
              type="text"
            />
            <Button
              className="bg-warning-color-soft text-warning-color"
              icon={<Icon icon={IconKey.Printer} width={20} />}
              onClick={handleGeneratePdf}
              size="middle"
              type="text"
            />
          </div>
          <Button
            className="font-semibold"
            onClick={() => setIsShowReceipt(false)}
            type="text"
          >
            {t('common.button.close')}
          </Button>
        </div>
      }
      hideCloseIcon={true}
      open={isShowReceipt}
      title={
        <>
          <div className="text-lg">
            {t('bookingHistory.detail.receipt.title')}
          </div>
          <div className="flex items-center justify-end gap-6">
            {bookingInfo.detailPayment ? (
              <TypographyUI className="xs:text-sm" font="Medium" type="BodyMd">
                <TypographyUI
                  className="xs:text-sm"
                  font="Regular"
                  type="BodySm"
                >
                  {t('bookingHistory.detail.receipt.bookingPaymentId')}:
                </TypographyUI>
                <Link
                  className="cursor-pointer text-info-color xs:text-xs truncate w-[120px]"
                  target="_blank"
                  to={
                    import.meta.env.VITE_STRIPE_PAYMENT_URL +
                    bookingInfo.detailPayment.id
                  }
                  type="BodyMd"
                >
                  {bookingInfo.detailPayment.id}
                </Link>
                <Copy
                  className="xs:w-5 xs:h-5 cursor-pointer"
                  onClick={() => {
                    copy(bookingInfo.detailPayment.id);
                    notification.success({
                      description: t('common.success'),
                      message: t('common.copiedToClipboard'),
                    });
                  }}
                  size={16}
                  variant="Outline"
                />
              </TypographyUI>
            ) : null}
            {bookingInfo.tipPaymentId ? (
              <TypographyUI className="xs:text-sm" font="Medium" type="BodyMd">
                <TypographyUI
                  className="xs:text-sm"
                  font="Regular"
                  type="BodySm"
                >
                  {t('bookingHistory.detail.receipt.tipPaymentId')}:
                </TypographyUI>
                <Link
                  className="cursor-pointer text-info-color xs:text-xs truncate w-[120px]"
                  target="_blank"
                  to={
                    import.meta.env.VITE_STRIPE_PAYMENT_URL +
                    bookingInfo.tipPaymentId
                  }
                  type="BodyMd"
                >
                  {bookingInfo.tipPaymentId}
                </Link>
                <Copy
                  className="xs:w-5 xs:h-5 cursor-pointer"
                  onClick={() => {
                    copy(bookingInfo.tipPaymentId as string);
                    notification.success({
                      description: t('common.success'),
                      message: t('common.copiedToClipboard'),
                    });
                  }}
                  size={16}
                  variant="Outline"
                />
              </TypographyUI>
            ) : null}
          </div>
        </>
      }
    >
      <ReceiptDescription
        bookingStatus={bookingInfo.status}
        name={`${t('bookingHistory.detail.receipt.tripFare')} (${
          i18n.language === 'fr'
            ? bookingInfo?.tripFare?.nameFr
            : bookingInfo?.tripFare?.name
        })`}
        value={bookingInfo?.tripFareFee}
      />
      {bookingInfo?.serviceFees?.map((serviceFee, index) => (
        <ReceiptDescription
          bookingStatus={bookingInfo.status}
          key={index}
          name={i18n.language === 'fr' ? serviceFee.nameFr : serviceFee.name}
          value={serviceFee.price}
        />
      ))}
      <Divider className="mt-2 mb-1" />
      {/* Sub total */}
      <ReceiptDescription
        bookingStatus={bookingInfo.status}
        highlight
        name={t('bookingHistory.detail.receipt.subTotal')}
        value={subTotal}
      />
      <ReceiptDescription
        bookingStatus={bookingInfo.status}
        name={t('bookingHistory.detail.receipt.tps')}
        value={tpsValue}
      />
      <ReceiptDescription
        bookingStatus={bookingInfo.status}
        name={t('bookingHistory.detail.receipt.tvq')}
        value={tvqValue}
      />
      <Divider className="mt-3 mb-0" />
      {/* Total */}
      <ReceiptDescription
        bookingStatus={bookingInfo.status}
        highlight
        name={t('bookingHistory.detail.receipt.total')}
        value={total}
      />
      {/* Promo code */}
      {bookingInfo?.couponAmount ? (
        <ReceiptDescription
          bookingStatus={bookingInfo.status}
          className={`${
            bookingInfo?.status === BookingEntity.status.CANCELLED
              ? 'text-neutral-400'
              : 'text-[#088752]'
          }`}
          name={
            <div className="flex gap-2">
              <span>{t('bookingHistory.detail.receipt.promotionCode')}</span>
              <span className="text-info-color">
                {bookingInfo?.coupon?.code || bookingInfo?.coupon?.title}
              </span>
            </div>
          }
          value={`-${formatter.currencyFormat(bookingInfo?.couponAmount)}`}
        />
      ) : null}
      {/* Amount charged */}
      <div className={`flex justify-between pt-2`}>
        <div className="flex gap-2">
          <span>{t('bookingHistory.detail.receipt.amountCharged')}</span>
          {bookingInfo?.status == BookingEntity.status.CANCELLED && (
            <span className="text-[#F7BA1E] font-medium">
              {bookingInfo.isCancelAfterChargeTime ? (
                <>{t('bookingHistory.table.cancelledAfter5Mins')}</>
              ) : (
                <>
                  {t(
                    'bookingHistory.detail.bookingInformation.freeCancellation',
                  )}
                </>
              )}
            </span>
          )}
        </div>
        <div className="flex gap-1 font-semibold">
          <div
            className={`${
              bookingInfo?.status === BookingEntity.status.CANCELLED
                ? 'text-neutral-400'
                : ''
            } `}
          >
            {formatter.currencyFormat(
              (total ?? 0) - (bookingInfo?.couponAmount ?? 0),
            )}
          </div>
          {bookingInfo?.status === BookingEntity.status.CANCELLED && (
            <div>
              {bookingInfo?.isCancelAfterChargeTime
                ? formatter.currencyFormat(bookingInfo?.amount)
                : formatter.currencyFormat(0)}
            </div>
          )}
        </div>
      </div>
      <div className="mt-2">
        {bookingInfo.paymentMethodType ===
        BookingEntity.paymentMethodType.CARD ? (
          <div className="flex items-center gap-2">
            <CardIcon />
            {t('common.paymentMethod.card')}
          </div>
        ) : (
          <div className="flex items-center gap-2">
            <Icon height={16} icon={IconKey.Cash} width={28} />
            {t('common.paymentMethod.cash')}
          </div>
        )}
      </div>
      {bookingInfo.tipAmount ? (
        <ReceiptDescription
          bookingStatus={bookingInfo.status}
          name={
            <>
              {t('bookingHistory.detail.receipt.tipAmount')}{' '}
              {`(${
                bookingInfo?.tipPaymentId
                  ? t('common.paymentMethod.card')
                  : t('common.paymentMethod.cash')
              })`}
            </>
          }
          value={bookingInfo.tipAmount}
        />
      ) : null}
      {/* Taxi loyal split */}
      {bookingInfo?.topUpAmount && bookingInfo.topUpAmount > 0 ? (
        <ReceiptDescription
          bookingStatus={bookingInfo.status}
          name={
            <div>
              {t('bookingHistory.detail.receipt.taxiLoyalSplit')}
              {` (${bookingInfo?.coupon?.topUpPercent}%)`}
            </div>
          }
          value={bookingInfo?.topUpAmount}
        />
      ) : null}
      {/* Refund amount */}
      {bookingInfo?.paymentMethodType ===
        BookingEntity.paymentMethodType.CARD &&
      bookingInfo?.status === BookingEntity.status.COMPLETED ? (
        <>
          <div className="-m-2">
            <Divider className="my-3" />
            {bookingInfo?.refundAmount !== 0 ? (
              <div
                className={`${
                  bookingInfo?.refundTag === BookingEntity.refundTag.REFUNDED
                    ? 'bg-success-color-soft'
                    : 'bg-warning-color-soft'
                } p-2`}
              >
                <div className="flex items-center rounded text-xs">
                  {bookingInfo?.refundTag ===
                  BookingEntity.refundTag.REFUNDED ? (
                    <div className="text-success-color">
                      {t('bookingHistory.detail.refund.fullyRefunded')}
                    </div>
                  ) : (
                    <div className="text-warning-color">
                      {t('bookingHistory.detail.refund.partiallyRefunded')}
                    </div>
                  )}
                </div>
                <div className="py-2 font-semibold flex justify-between">
                  {t('bookingHistory.table.refundAmount')}
                  <div>
                    {formatter.currencyFormat(bookingInfo?.refundAmount)}
                  </div>
                </div>
              </div>
            ) : null}
          </div>
          {/* Stripe fee */}
          <div className="mt-3">
            <ReceiptDescription
              bookingStatus={bookingInfo.status}
              name={t('bookingHistory.detail.receipt.stripeFee')}
              value={`-${formatter.currencyFormat(bookingInfo.stripeFees)}`}
            />
          </div>
        </>
      ) : null}

      {bookingInfo.tipStripeFees ? (
        <ReceiptDescription
          bookingStatus={bookingInfo.status}
          name={t('bookingHistory.detail.receipt.tipStripeFee')}
          value={`-${formatter.currencyFormat(bookingInfo.tipStripeFees)}`}
        />
      ) : null}
      {bookingInfo.status === BookingEntity.status.CANCELLED &&
      bookingInfo?.paymentMethodType === BookingEntity.paymentMethodType.CARD &&
      bookingInfo.stripeFees ? (
        <>
          <Divider className="mt-3 mb-0" />
          <ReceiptDescription
            bookingStatus={BookingEntity.status.COMPLETED}
            name={t('bookingHistory.detail.receipt.penaltyFee')}
            value={`-${formatter.currencyFormat(bookingInfo.stripeFees)}`}
          />
        </>
      ) : null}
      <Divider className="mt-3 mb-0" />
      <div className="font-semibold flex justify-between pt-2">
        <div>{t('bookingHistory.detail.receipt.amountForDriver')}</div>
        <div>
          {bookingInfo?.amountDriver && bookingInfo.amountDriver < 0
            ? 0
            : formatter.currencyFormat(bookingInfo?.amountDriver)}
        </div>
      </div>
    </ModalUI>
  );
}
