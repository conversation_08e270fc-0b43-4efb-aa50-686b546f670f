import { TypographyUI } from '#/shared/components/TypographyUI';
import { getImageUrl } from '#/shared/utils';
import { DescriptionUI } from '#/shared/components/DescriptionUI/DescriptionUI';
import { IconKey } from '#/shared/constants/iconKey';
import { Icon } from '@iconify/react';
import { Avatar, Card, Col, Rate, Row, Tag } from 'antd';
import { Call, Personalcard, ReceiptDiscount } from 'iconsax-react';
import { useTranslation } from 'react-i18next';
import { useEffect, useState } from 'react';
import { useDriverServiceDriverControllerGetDriver } from '#/api/queries';
import { type DriverEntity } from '#/api/requests';

interface Props {
  driverId: string;
}

export function DriverInformation({ driverId }: Readonly<Props>) {
  const { t } = useTranslation();
  const [driver, setDriver] = useState<DriverEntity>();

  const { data } = useDriverServiceDriverControllerGetDriver(
    {
      id: driverId,
    },
    ['useDriverServiceDriverControllerGetDriver'],
  );

  useEffect(() => {
    if (data) setDriver(data?.data);
  }, [data]);

  return (
    <Card
      bodyStyle={{
        paddingBottom: 12,
        paddingLeft: 16,
        paddingRight: 16,
        paddingTop: 12,
      }}
      title={
        <TypographyUI font="Semibold" type="BodyLg">
          {t('bookingHistory.detail.driverInformation.title')}
        </TypographyUI>
      }
    >
      <Row gutter={16}>
        <Col xl={8} xs={24}>
          <div className="flex items-start justify-between">
            <div className="flex gap-2 items-center">
              <Avatar
                className="lg:w-10 lg:h-10 w-20 h-20 flex items-center lg:text-base text-xl"
                src={getImageUrl(driver?.avatar)}
              >
                {driver?.fullName?.slice(0, 1)}
              </Avatar>
              <div className="flex flex-col">
                <div className="flex flex-wrap font-bold lg:text-base text-xl">
                  {driver?.fullName}
                </div>
                <div className="flex">
                  {driver?.avgRating ? (
                    <>
                      <Rate
                        allowHalf
                        defaultValue={Math.round(driver?.avgRating * 10) / 10}
                        disabled
                        style={{ color: '#ED1C24' }}
                      />
                      <div className="text-neutral-1 text-sm font-semibold ml-2">
                        {Math.round(driver?.avgRating * 10) / 10}
                      </div>
                    </>
                  ) : (
                    <></>
                  )}
                </div>
                <div>
                  <Tag
                    bordered={false}
                    className="font-semibold"
                    color="success"
                  >
                    Active
                  </Tag>
                </div>
              </div>
            </div>
          </div>
        </Col>
        <Col xl={8} xs={24}>
          <div className="xl:pt-4">
            <div className="pb-3 text-neutral-1 font-semibold text-base leading-6">
              <TypographyUI font="Semibold" type="BodyMd">
                {t('common.generalInformation')}
              </TypographyUI>
            </div>

            <DescriptionUI
              colSpan={24}
              items={[
                {
                  icon: (
                    <Personalcard
                      className="text-neutral-3 min-w-[22px]"
                      size={22}
                    />
                  ),
                  title: (
                    <TypographyUI font="Semibold" type="BodySm">
                      {t('bookingHistory.detail.driverInformation.driverId')}
                    </TypographyUI>
                  ),
                  value: (
                    <span className="text-info-color">#{driver?.driverId}</span>
                  ),
                },
                {
                  icon: (
                    <Icon
                      className="text-neutral-3 min-w-[22px]"
                      icon={IconKey.Email}
                      width={22}
                    />
                  ),
                  isMultipleLine: true,
                  title: (
                    <TypographyUI font="Semibold" type="BodySm">
                      {t('bookingHistory.detail.generalInformation.email')}
                    </TypographyUI>
                  ),
                  value: (
                    <span className="truncate hover:overflow-visible">
                      {driver?.email}
                    </span>
                  ),
                },
                {
                  icon: (
                    <Call className="text-neutral-3 min-w-[22px]" size={22} />
                  ),
                  title: (
                    <TypographyUI font="Semibold" type="BodySm">
                      {t(
                        'bookingHistory.detail.generalInformation.phoneNumber',
                      )}
                    </TypographyUI>
                  ),
                  value: (
                    <span className="block truncate xs:w-[140px]">
                      {driver?.phoneNumber}
                    </span>
                  ),
                },
              ]}
            />
          </div>
        </Col>

        <Col xl={8} xs={24}>
          <div className="xl:pt-4">
            <div className="pb-3 text-neutral-1 font-semibold text-base leading-6">
              <TypographyUI font="Semibold" type="BodyMd">
                {t('bookingHistory.detail.driverInformation.taxInformation')}
              </TypographyUI>
            </div>

            <DescriptionUI
              colSpan={24}
              items={[
                {
                  icon: (
                    <ReceiptDiscount
                      className="text-neutral-3 min-w-[22px]"
                      size={22}
                    />
                  ),
                  isMultipleLine: true,
                  title: (
                    <TypographyUI font="Semibold" type="BodySm">
                      {t('bookingHistory.detail.driverInformation.tps')}
                    </TypographyUI>
                  ),
                  value: (
                    <span className="truncate hover:overflow-visible">
                      {driver?.tps}
                    </span>
                  ),
                },
                {
                  icon: (
                    <ReceiptDiscount
                      className="text-neutral-3 min-w-[22px]"
                      size={22}
                    />
                  ),
                  title: (
                    <TypographyUI font="Semibold" type="BodySm">
                      {t('bookingHistory.detail.driverInformation.tvq')}
                    </TypographyUI>
                  ),
                  value: (
                    <span className="block truncate xs:w-[140px]">
                      {driver?.tvq}
                    </span>
                  ),
                },
              ]}
            />
          </div>
        </Col>
      </Row>
    </Card>
  );
}
