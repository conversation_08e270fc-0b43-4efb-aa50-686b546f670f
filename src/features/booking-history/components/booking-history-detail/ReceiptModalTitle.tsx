import type { BookingEntity } from '#/api/requests';
import { TypographyUI } from '#/shared/components/TypographyUI';
import { notification } from 'antd';
import copy from 'copy-to-clipboard';
import { Copy } from 'iconsax-react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';

interface Props {
  bookingInfo: BookingEntity;
}

export function ReceiptModalTitle({ bookingInfo }: Readonly<Props>) {
  const { t } = useTranslation();

  return (
    <>
      <div className="text-lg">{t('bookingHistory.detail.receipt.title')}</div>
      <div className="flex items-center justify-end gap-6">
        {bookingInfo.detailPayment ? (
          <TypographyUI className="xs:text-sm" font="Medium" type="BodyMd">
            <TypographyUI className="xs:text-sm" font="Regular" type="BodySm">
              {t('bookingHistory.detail.receipt.bookingPaymentId')}:
            </TypographyUI>
            <Link
              className="cursor-pointer text-info-color xs:text-xs truncate w-[120px]"
              target="_blank"
              to={
                import.meta.env.VITE_STRIPE_PAYMENT_URL +
                bookingInfo.detailPayment.id
              }
              type="BodyMd"
            >
              {bookingInfo.detailPayment.id}
            </Link>
            <Copy
              className="xs:w-5 xs:h-5 cursor-pointer"
              onClick={() => {
                copy(bookingInfo.detailPayment.id);
                notification.success({
                  description: t('common.success'),
                  message: t('common.copiedToClipboard'),
                });
              }}
              size={16}
              variant="Outline"
            />
          </TypographyUI>
        ) : null}
        {bookingInfo.tipPaymentId ? (
          <TypographyUI className="xs:text-sm" font="Medium" type="BodyMd">
            <TypographyUI className="xs:text-sm" font="Regular" type="BodySm">
              {t('bookingHistory.detail.receipt.tipPaymentId')}:
            </TypographyUI>
            <Link
              className="cursor-pointer text-info-color xs:text-xs truncate w-[120px]"
              target="_blank"
              to={
                import.meta.env.VITE_STRIPE_PAYMENT_URL +
                bookingInfo.tipPaymentId
              }
              type="BodyMd"
            >
              {bookingInfo.tipPaymentId}
            </Link>
            <Copy
              className="xs:w-5 xs:h-5 cursor-pointer"
              onClick={() => {
                copy(bookingInfo.tipPaymentId as string);
                notification.success({
                  description: t('common.success'),
                  message: t('common.copiedToClipboard'),
                });
              }}
              size={16}
              variant="Outline"
            />
          </TypographyUI>
        ) : null}
      </div>
    </>
  );
}
