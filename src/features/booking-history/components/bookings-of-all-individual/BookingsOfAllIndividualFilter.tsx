/* eslint-disable @typescript-eslint/no-unnecessary-condition */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unnecessary-condition */
import { FilterWrapperUI } from '#/shared/components/FilterWrapperUI';
import { TypographyUI } from '#/shared/components/TypographyUI';
import { FormInput } from '#/shared/components/formField/FormInput';
import { FormRangeDatePicker } from '#/shared/components/formField/FormRangeDatePicker';
import { BookingStatusSelector } from '#/shared/components/selectors/BookingStatus/BookingStatusSelector';
import { PaymentMethodSelector } from '#/shared/components/selectors/PaymentMethod/PaymentMethodSelector';
import { type BaseFilter } from '#/shared/hooks';
import { Button, Col, Row, Spin } from 'antd';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import { type UseFormReturn } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import type { HistoryBookingOfCustomerFilter } from '../../hooks/business-history-bookings/useHistoryBookingOfCustomerTable';
import useExportBooking from '../../hooks/business-history-bookings/useExportBooking';
import { ServiceSelector } from '#/shared/components/selectors/Service/ServiceSelector';
import { useDebounce } from 'use-debounce';
import { isUndefined } from 'lodash-es';
import { FormEntitySelector } from '#/shared/components/selectors/FormEntitySelector';
import { BookingEntity, CancelReason } from '#/api/requests';
import { formatter } from '#/shared/utils';
import { CollapseMenu } from '#/shared/components/CollapseMenu';
import './styles.scss';
import { ExportButton } from '#/shared/components/Button/ExportButton';
import { useZoneBookingFilter } from '../../hooks/individual-history-booking/useZoneBookingFilter';

interface Props {
  form: UseFormReturn<BaseFilter & HistoryBookingOfCustomerFilter>;
  fleetId?: string;
}

export function BookingsOfAllIndividualFilter({ form, fleetId }: Props) {
  const { t } = useTranslation();
  const { refetch, isLoading } = useExportBooking({
    type: 'PERSONAL',
  });

  const { zoneOptions } = useZoneBookingFilter();
  const savedData = localStorage.getItem('individualFilter');

  const [formData, setFormData] = useState(
    savedData ? JSON.parse(savedData) : undefined,
  );
  const [filterValue] = useDebounce(formData, 350);

  const q = formData?.q || '';
  const rangeDate = formData?.rangeDate || [];
  const statuses = formData?.statuses || '';
  const paymentMethodType = formData?.paymentMethodType || '';
  const vehicleTypes = formData?.vehicleTypes || '';
  const refundTag = formData?.refundTag || undefined;
  const reason = formData?.reason || [];
  const zones = formData?.zones || [];

  const showClearFilter =
    q ||
    rangeDate.length > 0 ||
    statuses ||
    paymentMethodType ||
    vehicleTypes?.length > 0 ||
    refundTag ||
    reason?.length > 0 ||
    zones?.length > 0;

  const onClearFilter = () => {
    setFormData({});
    form.setValue('q', '');
    form.setValue('rangeDate', '');
    form.setValue('statuses', undefined);
    form.setValue('paymentMethodType', undefined);
    form.setValue('vehicleTypes', undefined);
    form.setValue('refundTag', undefined);
    form.setValue('reason', []);
    form.setValue('zones', []);
    localStorage.removeItem('individualFilter');
  };

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleChange = (fieldName: string, value: any) => {
    if (Array.isArray(value) && value.length === 0) {
      setFormData({ ...formData, [fieldName]: undefined });
      return;
    }

    const updatedData = { ...formData, [fieldName]: value };
    setFormData(updatedData);
  };

  const onHandleExport = () => {
    refetch();
  };

  useEffect(() => {
    if (!isUndefined(filterValue))
      localStorage.setItem('individualFilter', JSON.stringify(filterValue));
  }, [filterValue]);

  return (
    <Spin spinning={isLoading}>
      <FilterWrapperUI
        addOnActions={
          fleetId ? undefined : <ExportButton onExport={onHandleExport} />
        }
        filters={
          <Row className="flex gap-2">
            <Col>
              <FormInput
                className="w-60"
                defaultValue={formData?.q}
                form={form}
                label={null}
                name="q"
                onChange={event => {
                  handleChange('q', event.target.value);
                }}
                onKeyDown={(e: React.KeyboardEvent<HTMLInputElement>) => {
                  if (e.key === ' ' && q.length === 0) {
                    e.preventDefault();
                  }
                }}
                placeholder={t('bookingHistory.searchCustomer')}
                size="middle"
              />
            </Col>

            <FormRangeDatePicker
              allowClear={true}
              className="sm:w-60 w-[276px]"
              defaultValue={[
                formData?.rangeDate?.[0]
                  ? dayjs(formData?.rangeDate[0]).startOf('day')
                  : null,
                formData?.rangeDate
                  ? dayjs(formData?.rangeDate[1])
                      .tz()
                      .endOf('day')
                  : null,
              ]}
              form={form}
              label={null}
              name="rangeDate"
              onCalendarChange={values => {
                if (values?.[0] && values?.[1]) {
                  handleChange('rangeDate', [
                    dayjs(values[0]).startOf('day').format().toString(),
                    dayjs(values[1]).endOf('day').format().toString(),
                  ]);
                }
              }}
              onClear={() => {
                form.setValue('rangeDate', '');
                handleChange('rangeDate', undefined);
              }}
            />

            <BookingStatusSelector
              className="w-60 h-8"
              defaultValue={formData?.statuses}
              form={form}
              name="statuses"
              onChange={(value: string) => {
                handleChange('statuses', value);
              }}
            />
            <PaymentMethodSelector
              className="text-sm w-60 h-8"
              defaultValue={formData?.paymentMethodType}
              form={form}
              name="paymentMethodType"
              onChange={(value: string) => {
                handleChange('paymentMethodType', value);
              }}
            />
            <CollapseMenu>
              <ServiceSelector
                className="text-sm w-60 h-8 service-filter-custom"
                defaultValue={formData?.vehicleTypes}
                form={form}
                hasAnyCar={true}
                mode="multiple"
                name="vehicleTypes"
                onChange={(value: string) => {
                  handleChange('vehicleTypes', value);
                }}
              />

              <FormEntitySelector
                allowClear
                className="w-60 xl:mt-2 mt-0"
                defaultValue={formData?.refundTag}
                form={form}
                label={null}
                name="refundTag"
                onChange={(value: string) => {
                  handleChange('refundTag', value);
                }}
                options={[
                  {
                    label: formatter.normalizeEnumString(
                      BookingEntity.refundTag.PARTIAL_REFUND,
                    ),
                    value: BookingEntity.refundTag.PARTIAL_REFUND,
                  },
                  {
                    label: formatter.normalizeEnumString(
                      BookingEntity.refundTag.REFUNDED,
                    ),
                    value: BookingEntity.refundTag.REFUNDED,
                  },
                ]}
                placeholder={t('bookingHistory.table.refundTag')}
                size="middle"
              />

              <FormEntitySelector
                allowClear
                className="w-[300px] xl:mt-2 mt-0"
                defaultValue={formData?.reason}
                form={form}
                label={null}
                maxTagCount={1}
                mode="multiple"
                name="reason"
                onChange={(value: string) => {
                  handleChange('reason', value);
                }}
                options={[
                  {
                    label: t('cancel.DRIVER_ARRIVED_EARLY'),
                    value: CancelReason.reason.DRIVER_ARRIVED_EARLY,
                  },
                  {
                    label: t('cancel.DRIVER_ASKED_CANCEL'),
                    value: CancelReason.reason.DRIVER_ASKED_CANCEL,
                  },
                  {
                    label: t('cancel.DRIVER_NOT_GETTING_CLOSER'),
                    value: CancelReason.reason.DRIVER_NOT_GETTING_CLOSER,
                  },
                  {
                    label: t('cancel.COULD_NOT_FIND_DRIVER'),
                    value: CancelReason.reason.COULD_NOT_FIND_DRIVER,
                  },
                  {
                    label: t('cancel.WAIT_TIME_TOO_LONG'),
                    value: CancelReason.reason.WAIT_TIME_TOO_LONG,
                  },
                  {
                    label: t('cancel.DRIVER_DELAYED'),
                    value: CancelReason.reason.DRIVER_DELAYED,
                  },
                  {
                    label: t('cancel.DRIVER_NOT_MOVING'),
                    value: CancelReason.reason.DRIVER_NOT_MOVING,
                  },
                  {
                    label: t('cancel.INCORRECT_ADDRESS'),
                    value: CancelReason.reason.INCORRECT_ADDRESS,
                  },
                  {
                    label: t('cancel.CANCELLED_BY_CENTER'),
                    value: CancelReason.reason.CANCELLED_BY_CENTER,
                  },
                  {
                    label: t('cancel.AUTO_CANCELLED'),
                    value: CancelReason.reason.AUTO_CANCELLED,
                  },
                  {
                    label: t('cancel.OTHER'),
                    value: CancelReason.reason.OTHER,
                  },
                ]}
                placeholder={t('bookingHistory.table.cancelReason')}
                size="middle"
              />

              <FormEntitySelector
                allowClear
                className="w-[290px]"
                defaultValue={formData?.zones}
                form={form}
                maxTagCount={2}
                mode="multiple"
                name="zones"
                onChange={value => {
                  handleChange('zones', value);
                }}
                options={zoneOptions}
                placeholder={t('bookingHistory.table.filter.zone')}
                size="middle"
              />
            </CollapseMenu>

            {showClearFilter ? (
              <Button
                className="px-2 bg-white"
                onClick={onClearFilter}
                size="middle"
                type="text"
              >
                <TypographyUI font="Semibold" type="BodySm">
                  {t('common.button.clearFilter')}
                </TypographyUI>
              </Button>
            ) : null}
          </Row>
        }
      />
    </Spin>
  );
}
