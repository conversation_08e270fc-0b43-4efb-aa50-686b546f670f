import { useBookingsServiceBookingSuperAdminControllerGetBookingProgresses } from '#/api/queries';
import { BookingProgressEntity } from '#/api/requests';
import { formatter } from '#/shared/utils';
import { CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';
import { Spin } from 'antd';
import { t } from 'i18next';
import { useEffect, useState } from 'react';

interface BookingProgressItemProps {
  item: BookingProgressEntity;
}

interface BookingProgressProps {
  bookingId: string;
}

const bookingStatusFormatted = [
  {
    label: t('bookingHistory.detail.bookingProgress.bookingCreated'),
    status: BookingProgressEntity.status.DEMAND_CREATION,
  },
  {
    label: t('bookingHistory.detail.bookingProgress.lookingForADriver'),
    status: BookingProgressEntity.status.AWAITING_CONFIRMED_VEHICLE,
  },
  {
    label: t('bookingHistory.detail.bookingProgress.driverIsOnTheWay'),
    status: BookingProgressEntity.status.VEHICLE_CONFIRMED,
  },
  {
    label: t('bookingHistory.detail.bookingProgress.driverJustArrived'),
    status: BookingProgressEntity.status.ARRIVAL_AT_CLIENT,
  },
  {
    label: t('bookingHistory.detail.bookingProgress.youAreOnTheRoad'),
    status: BookingProgressEntity.status.CLIENT_ON_BOARD,
  },
  {
    label: t('bookingHistory.detail.bookingProgress.bookingCompleted'),
    status: BookingProgressEntity.status.COMPLETED,
  },
  {
    label: t('bookingHistory.detail.bookingProgress.bookingCanceled'),
    status: BookingProgressEntity.status.CANCELLED,
  },
];

function BookingProgressItem({ item }: BookingProgressItemProps) {
  const { status, date, cancelReason } = item;

  return (
    <div className="flex gap-4 py-4 items-start">
      <div className="mt-1">
        {status === BookingProgressEntity.status.CANCELLED ? (
          <CloseCircleOutlined style={{ color: '#fe353d' }} />
        ) : (
          <CheckCircleOutlined style={{ color: '#007AFF' }} />
        )}
      </div>
      <div className="flex flex-col space-y-1">
        <div className="text-sm font-medium">
          {bookingStatusFormatted.find(item => item.status === status)?.label}
        </div>
        <div>
          {cancelReason ? (
            <div className="text-neutral-875 text-xs">
              {t('bookingHistory.detail.bookingInformation.cancelReason')}:{' '}
              {formatter.normalizeFirstEnumString(cancelReason.reason)}
            </div>
          ) : null}
        </div>
        <div className="text-neutral-600 text-xs">
          {formatter.formatDaytime(date, 'DD/MM/YYYY, HH:mm')}
        </div>
      </div>
    </div>
  );
}

export default function BookingProgress({ bookingId }: BookingProgressProps) {
  const [bookingProgress, setBookingProgress] = useState<
    BookingProgressEntity[]
  >([]);

  const { data, isLoading } =
    useBookingsServiceBookingSuperAdminControllerGetBookingProgresses({
      id: bookingId,
    });

  useEffect(() => {
    setBookingProgress((data?.data as BookingProgressEntity[]) || []);
  }, [data]);

  return (
    <>
      <div className="text-lg font-semibold">
        {t('bookingHistory.detail.bookingProgress.title')}
      </div>
      <Spin spinning={isLoading}>
        {bookingProgress
          ?.filter(
            item =>
              bookingStatusFormatted
                ?.map(item => item.status)
                ?.includes(item.status),
          )
          ?.sort((a, b) => (a.date < b.date ? 1 : -1))
          ?.map((item, index) => (
            <div key={index}>
              <BookingProgressItem item={item} />
            </div>
          ))}
      </Spin>
    </>
  );
}
