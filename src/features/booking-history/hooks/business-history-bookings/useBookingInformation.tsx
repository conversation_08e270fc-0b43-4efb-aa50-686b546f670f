/* eslint-disable @typescript-eslint/no-unnecessary-condition */
/* eslint-disable @typescript-eslint/no-unsafe-call */
/* eslint-disable @typescript-eslint/no-unsafe-argument */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import { useSupportTicketServiceSupportTicketAdminControllerGetListSupportTicket } from '#/api/queries';
import {
  BookingEntity,
  CancelReason,
  SupportTicketEntity,
} from '#/api/requests';
import PaymentMethod from '#/shared/components/PaymentMethod/PaymentMethod';
import { TypographyUI } from '#/shared/components/TypographyUI';
import { IconKey } from '#/shared/constants/iconKey';
import { formatter } from '#/shared/utils';
import { Icon } from '@iconify/react';
import { Button } from 'antd';
import {
  Calendar,
  CalendarTick,
  Car,
  Card,
  LocationAdd,
  LocationTick,
  Map,
  Note,
  Ruler,
  Star1,
  Timer1,
} from 'iconsax-react';
import { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router-dom';

interface Props {
  bookingInfo: BookingEntity;
}

export const useBookingInformation = ({ bookingInfo }: Readonly<Props>) => {
  const params = useParams();
  const { t } = useTranslation();

  const bookingId = params.id || '';

  const [isOpenSendModal, setIsOpenSendModal] = useState(false);
  const [isOpenTicketModal, setIsOpenTicketModal] = useState(false);
  const [isAllTicketResolved, setIsAllTicketResolved] = useState(true);

  const [isOpenMapModal, setIsOpenMapModal] = useState(false);
  const [isShowViewAll, setIsShowViewAll] = useState(false);

  const [isOpenRefundModal, setIsOpenRefundModal] = useState(false);
  const [ticketList, setTicketList] = useState<SupportTicketEntity[]>([]);

  const parentRef = useRef<HTMLDivElement | null>(null);
  const contentRef = useRef<HTMLDivElement | null>(null);

  const onOpenTicketModal = () => {
    setIsOpenTicketModal(true);
  };

  const onCloseTicketModal = () => {
    setIsOpenTicketModal(false);
  };

  const onOpenRefundModal = () => {
    setIsOpenRefundModal(true);
  };

  const onCloseRefundModal = () => {
    setIsOpenRefundModal(false);
  };

  const onOpenSendModal = () => {
    setIsOpenSendModal(true);
  };

  const onCloseSendModal = () => {
    setIsOpenSendModal(false);
  };

  const onOpenMapModal = () => {
    setIsOpenMapModal(true);
  };

  const onCloseMapModal = () => {
    setIsOpenMapModal(false);
  };

  const onHandleClickViewAll = () => {
    setIsShowViewAll(false);
  };

  const { data, isLoading, refetch } =
    useSupportTicketServiceSupportTicketAdminControllerGetListSupportTicket({
      bookingId,
      offset: 0,
    });

  useEffect(() => {
    const checkOverflow = () => {
      if (parentRef.current && contentRef.current) {
        const parentWidth = parentRef.current.getBoundingClientRect().width;
        const contentWidth = contentRef.current.getBoundingClientRect().width;
        // If the content width exceeds the parent width, show the button
        setIsShowViewAll(contentWidth >= parentWidth);
      }
    };

    checkOverflow(); // Initial check
    window.addEventListener('resize', checkOverflow); // Recheck on window resize

    return () => {
      window.removeEventListener('resize', checkOverflow); // Cleanup event listener
    };
  }, []);

  useEffect(() => {
    if (data) {
      setTicketList(data?.data?.items as SupportTicketEntity[]);
      const allResolved = data?.data?.items?.find(
        (item: SupportTicketEntity) =>
          item.status == SupportTicketEntity.status.OPEN,
      );

      if (!allResolved) {
        setIsAllTicketResolved(true);
      } else {
        setIsAllTicketResolved(false);
      }
    }
  }, [data]);

  const infoBooking = [
    // auto cancel after 5m
    ...(bookingInfo?.cancelReason
      ? [
          {
            colSpan: 24,
            icon: <Note className="text-neutral-3" size={22} />,
            isMultipleLine: true,
            title: (
              <TypographyUI font="Semibold" type="BodySm">
                {t('bookingHistory.detail.bookingInformation.cancelReason')}
              </TypographyUI>
            ),
            value: (
              <TypographyUI font="Regular" type="BodySm">
                {bookingInfo.cancelReason.reason == CancelReason.reason.OTHER &&
                bookingInfo.cancelReason?.note
                  ? bookingInfo.cancelReason.note
                  : formatter.normalizeFirstEnumString(
                      t(`cancel.${bookingInfo.cancelReason.reason}`),
                    )}{' '}
                {bookingInfo.cancelReason?.isAutoCancelled === true &&
                  t(
                    'bookingHistory.detail.bookingInformation.autoCancellation',
                  )}
              </TypographyUI>
            ),
          },
        ]
      : []),
    ...(bookingInfo?.scheduledDate
      ? [
          {
            colSpan: 24,
            icon: <Timer1 className="text-neutral-3 min-w-[22px]" size={22} />,
            title: (
              <TypographyUI font="Semibold" type="BodySm">
                {t('bookingHistory.detail.bookingInformation.scheduledTime')}
              </TypographyUI>
            ),
            value: (
              <span className="block truncate xs:w-[124px]">
                {formatter.formatDaytime(
                  bookingInfo.scheduledDate,
                  'DD/MM/YYYY, HH:mm',
                )}
              </span>
            ),
          },
        ]
      : []),
    {
      icon: <Calendar className="text-neutral-3 min-w-[22px]" size={22} />,
      title: (
        <TypographyUI font="Semibold" type="BodySm">
          {t('bookingHistory.detail.bookingInformation.start')}
        </TypographyUI>
      ),
      value: (
        <span className="block truncate xs:w-[124px]">
          {formatter.formatDaytime(bookingInfo.date, 'DD/MM/YYYY, HH:mm')}
        </span>
      ),
    },
    {
      icon: <LocationAdd className="text-neutral-3 min-w-[22px]" size={22} />,
      isMultipleLine: true,
      title: (
        <TypographyUI font="Semibold" type="BodySm">
          {t('bookingHistory.detail.bookingInformation.from')}
        </TypographyUI>
      ),
      value: bookingInfo.originAddress.address,
    },
    {
      icon: <CalendarTick className="text-neutral-3 min-w-[22px]" size={22} />,
      title: (
        <TypographyUI font="Semibold" type="BodySm">
          {t('bookingHistory.detail.bookingInformation.end')}
        </TypographyUI>
      ),
      value: (
        <span className="block truncate xs:w-[124px]">
          {bookingInfo.endDate
            ? formatter.formatDaytime(bookingInfo.endDate, 'DD/MM/YYYY, HH:mm')
            : '______________'}
        </span>
      ),
    },
    {
      icon: <LocationTick className="text-neutral-3" size={22} />,
      isMultipleLine: true,
      title: (
        <TypographyUI font="Semibold" type="BodySm">
          {t('bookingHistory.detail.bookingInformation.to')}
        </TypographyUI>
      ),
      value: bookingInfo.destinationAddress.address,
    },
    {
      icon: <Icon icon={IconKey.LoyalPointDetail} width={22} />,
      title: (
        <TypographyUI font="Semibold" type="BodySm">
          {t('bookingHistory.detail.bookingInformation.loyalPoints')}
        </TypographyUI>
      ),
      value: (
        <span
          className={`${
            bookingInfo.status === BookingEntity.status.CANCELLED
              ? 'opacity-30 line-through'
              : ''
          }`}
        >
          {bookingInfo?.awardedFirstRide
            ? bookingInfo?.awardedPoint + bookingInfo?.awardedFirstRide
            : bookingInfo?.awardedPoint}
        </span>
      ),
    },
    ...(bookingInfo?.type === 'PERSONAL'
      ? [
          {
            icon: <Card className="text-neutral-3 min-w-[22px]" size={22} />,
            // isMultipleLine: true,
            title: (
              <TypographyUI font="Semibold" type="BodySm">
                {t('bookingHistory.detail.bookingInformation.paymentMethod')}
              </TypographyUI>
            ),
            value: <PaymentMethod method={bookingInfo.paymentMethodType} />,
          },
        ]
      : [
          {
            icon: null,
            title: null,
            value: null,
          },
        ]),
    {
      icon: <Ruler className="text-neutral-3 min-w-[22px]" size={22} />,
      title: (
        <TypographyUI font="Semibold" type="BodySm">
          {t('bookingHistory.detail.bookingInformation.distance')}
        </TypographyUI>
      ),
      value: (
        <span className="block truncate xs:w-[124px]">
          {formatter.distanceFormat(bookingInfo.distance)}
        </span>
      ),
    },

    {
      icon: <Car className="text-neutral-3 min-w-[22px]" size={22} />,
      title: (
        <TypographyUI font="Semibold" type="BodySm">
          {t('bookingHistory.detail.bookingInformation.service')}
        </TypographyUI>
      ),
      value: (
        <span className="block truncate xs:w-[124px]">
          {t(`serviceType.${bookingInfo.vehicleType}`)}
        </span>
      ),
    },
    {
      icon: <Map className="text-neutral-3 min-w-[22px]" size={22} />,
      title: (
        <TypographyUI font="Semibold" type="BodySm">
          {t('bookingHistory.detail.bookingInformation.mappingRoute')}
        </TypographyUI>
      ),
      value: (
        <Button
          className={`${
            bookingInfo.status === BookingEntity.status.COMPLETED
              ? 'block'
              : 'hidden'
          } text-info-color border-none font-semibold text-sm bg-none p-0 h-5 shadow-none`}
          onClick={() => {
            onOpenMapModal();
          }}
        >
          {t('bookingHistory.detail.bookingInformation.viewOnTheMap')}
        </Button>
      ),
    },
    {
      icon: <Note className="text-neutral-3 min-w-[22px]" size={22} />,
      isMultipleLine: true,
      title: (
        <TypographyUI font="Semibold" type="BodySm">
          {t('bookingHistory.detail.bookingInformation.noteToDriver')}
        </TypographyUI>
      ),
      value: (
        <>
          <div
            className={`${
              isShowViewAll && 'overflow-hidden'
            } sm:w-[160px] md:w-[220px] lg:w-[450px] xl:w-[120px] xxl:w-[380px] w-[520px] flex`}
            ref={parentRef}
          >
            <span
              className={`${isShowViewAll && 'block truncate'}`}
              ref={contentRef}
            >
              {bookingInfo?.note}
            </span>
          </div>
          <Button
            className={`${
              isShowViewAll
                ? 'block text-info-color border-none shadow-none p-0 text-sm h-5'
                : 'hidden'
            }`}
            onClick={onHandleClickViewAll}
          >
            <span className="underline">View all</span>
          </Button>
        </>
      ),
    },
    {
      icon: <Star1 className="text-neutral-3 min-w-[22px]" size={22} />,
      title: (
        <TypographyUI font="Semibold" type="BodySm">
          {t('bookingHistory.detail.bookingInformation.rating')}
        </TypographyUI>
      ),
      // TO DO
      value: (
        <>
          {bookingInfo?.review?.rating ? `${bookingInfo.review.rating}/5` : ''}
        </>
      ),
    },
    {
      icon: <Note className="text-neutral-3 min-w-[22px]" size={22} />,
      isMultipleLine: true,
      title: (
        <TypographyUI font="Semibold" type="BodySm">
          {t('bookingHistory.detail.bookingInformation.ratingDetail')}
        </TypographyUI>
      ),
      value: <span>{bookingInfo?.review?.note}</span>,
    },
    {
      icon: <Map className="text-neutral-3 min-w-[22px]" size={22} />,
      isMultipleLine: true,
      title: (
        <TypographyUI font="Semibold" type="BodySm">
          {t('bookingHistory.table.zone')}
        </TypographyUI>
      ),
      value: (
        <span>
          <div className="flex flex-row flex-wrap items-center gap-2">
            {bookingInfo?.zones?.map((item, index) => (
              <div
                className="bg-neutral-200 px-2 py-1 rounded-md font-medium text-xs"
                key={index}
              >
                {item}
              </div>
            ))}
          </div>
        </span>
      ),
    },
  ];

  return {
    infoBooking,
    isAllTicketResolved,
    isLoading,
    isOpenMapModal,
    isOpenRefundModal,
    isOpenSendModal,
    isOpenTicketModal,
    onCloseMapModal,
    onCloseRefundModal,
    onCloseSendModal,
    onCloseTicketModal,
    onOpenRefundModal,
    onOpenSendModal,
    onOpenTicketModal,
    refetchTicketList: refetch,
    setIsOpenRefundModal,
    ticketList,
  };
};
