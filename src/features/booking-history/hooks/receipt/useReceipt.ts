import { useBookingsServiceBookingControllerGetBookingReceiptAdmin } from '#/api/queries';
import type { BookingEntity } from '#/api/requests';
import html2pdf from 'html2pdf.js';
import { useEffect, useRef, useState } from 'react';

interface Props {
  bookingInfo: BookingEntity;
}

export function useReceipt({ bookingInfo }: Readonly<Props>) {
  const contentRef = useRef<string>('');

  const [pdfUrl, setPdfUrl] = useState<string>('');
  const [pdfContent, setPdfContent] = useState<string>('');
  const [isShowReceipt, setIsShowReceipt] = useState<boolean>(false);

  const totalServiceFee = bookingInfo?.serviceFees?.reduce(
    (acc, serviceFee) => acc + serviceFee.price,
    0,
  );

  const subTotal = totalServiceFee + bookingInfo.tripFareFee;
  const tpsValue = (subTotal * 5) / 100;
  const tvqValue = (subTotal * 9.975) / 100;
  const total = bookingInfo.price || 0;

  const { data, refetch } =
    useBookingsServiceBookingControllerGetBookingReceiptAdmin({
      id: bookingInfo.id,
    });

  const opt = {
    html2canvas: { scale: 2 },
    image: { quality: 0.95, type: 'jpeg' },
  };

  const onViewDetail = () => {
    setIsShowReceipt(true);
  };

  const onHandleClickPDF = async () => {
    await refetch();

    if (pdfContent) {
      html2pdf().from(pdfContent).set(opt).save('TaxiLoyal_Receipt');
    }
  };

  const handleGeneratePdf = async () => {
    const pdfBlob = await html2pdf().from(pdfContent).set(opt).output('blob');
    const pdfUrl = URL.createObjectURL(pdfBlob);
    setPdfUrl(pdfUrl);
  };

  const handlePrint = () => {
    const iframe = contentRef.current;

    if (iframe) {
      iframe.focus();
      iframe.contentWindow.print();
    }
  };

  useEffect(() => {
    if (data) {
      contentRef.current = data?.data as unknown as string;
      setPdfContent(contentRef.current);
    }
  }, [data]);

  return {
    contentRef,
    data,
    handleGeneratePdf,
    handlePrint,
    isShowReceipt,
    onHandleClickPDF,
    onViewDetail,
    pdfContent,
    pdfUrl,
    setIsShowReceipt,
    subTotal,
    total,
    tpsValue,
    tvqValue,
  };
}
