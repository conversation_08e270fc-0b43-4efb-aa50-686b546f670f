import { useBookingsServiceBookingControllerGetBookingReceiptAdmin } from '#/api/queries';
import type { BookingEntity } from '#/api/requests';
import html2pdf from 'html2pdf.js';
import { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

interface Props {
  bookingInfo: BookingEntity;
}

export function useReceipt({ bookingInfo }: Readonly<Props>) {
  const contentRef = useRef<string>('');
  const { i18n } = useTranslation();

  const [pdfUrl, setPdfUrl] = useState<string>('');
  const [pdfContent, setPdfContent] = useState<string>('');
  const [isShowReceipt, setIsShowReceipt] = useState<boolean>(false);

  const totalServiceFee = bookingInfo?.serviceFees?.reduce(
    (acc, serviceFee) => acc + serviceFee.price,
    0,
  );

  const subTotal = totalServiceFee + bookingInfo.tripFareFee;
  const tpsValue = (subTotal * 5) / 100;
  const tvqValue = (subTotal * 9.975) / 100;
  const total = bookingInfo.price || 0;

  const { data, refetch } =
    useBookingsServiceBookingControllerGetBookingReceiptAdmin({
      id: bookingInfo.id,
    });

  const opt = {
    html2canvas: { scale: 2 },
    image: { quality: 0.95, type: 'jpeg' },
  };

  const onViewDetail = () => {
    setIsShowReceipt(true);
  };

  const onHandleClickPDF = async (language?: 'en' | 'fr') => {
    // Temporarily change language if specified for the API request
    const originalLanguage = i18n.language;

    if (language && language !== originalLanguage) {
      await i18n.changeLanguage(language);
    }

    // Refetch the receipt data with the new language
    const { data: newData } = await refetch();

    // Restore original language if it was changed
    if (language && language !== originalLanguage) {
      await i18n.changeLanguage(originalLanguage);
    }

    // Use the fresh data for PDF generation
    const freshPdfContent = newData?.data as unknown as string;

    if (freshPdfContent) {
      const filename = language
        ? `TaxiLoyal_Receipt_${language}`
        : 'TaxiLoyal_Receipt';
      html2pdf().from(freshPdfContent).set(opt).save(filename);
    }
  };

  const handleGeneratePdf = async () => {
    const pdfBlob = await html2pdf().from(pdfContent).set(opt).output('blob');
    const pdfUrl = URL.createObjectURL(pdfBlob);
    setPdfUrl(pdfUrl);
  };

  const handlePrint = () => {
    const iframe = contentRef.current;

    if (iframe) {
      iframe.focus();
      iframe.contentWindow.print();
    }
  };

  useEffect(() => {
    if (data) {
      contentRef.current = data?.data as unknown as string;
      setPdfContent(contentRef.current);
    }
  }, [data]);

  return {
    contentRef,
    data,
    handleGeneratePdf,
    handlePrint,
    isShowReceipt,
    onHandleClickPDF,
    onViewDetail,
    pdfContent,
    pdfUrl,
    setIsShowReceipt,
    subTotal,
    total,
    tpsValue,
    tvqValue,
  };
}
