import { IPermission } from '#/api/loyalhub/requests';
import { EActions } from '../../Actions';
import { createPermissionGroup, createPermissionItem } from '../utils';

export const feeConfigurationPermissions = createPermissionGroup(
  `${IPermission.entity.CONFIG_FEE},${IPermission.entity.CONFIG_RATE}, ${IPermission.entity.CONFIG_TIP}, ${IPermission.entity.CONFIG_FIXED_FARE}`,
  'permission.key.feeConfiguration.title',
  [
    createPermissionItem(
      'permission.key.feeConfiguration.fee.view',
      EActions.View,
      IPermission.entity.CONFIG_FEE,
    ),
    createPermissionItem(
      'permission.key.feeConfiguration.fee.action',
      `${EActions.Create},${EActions.Update},${EActions.Delete}`,
      IPermission.entity.CONFIG_FEE,
    ),
    createPermissionItem(
      'permission.key.feeConfiguration.rate.view',
      EActions.View,
      IPermission.entity.CONFIG_RATE,
    ),
    createPermissionItem(
      'permission.key.feeConfiguration.rate.action',
      `${EActions.Create},${EActions.Update},${EActions.Delete}`,
      IPermission.entity.CONFIG_RATE,
    ),
    createPermissionItem(
      'permission.key.feeConfiguration.tip.view',
      EActions.View,
      IPermission.entity.CONFIG_TIP,
    ),
    createPermissionItem(
      'permission.key.feeConfiguration.tip.action',
      EActions.Update,
      IPermission.entity.CONFIG_TIP,
    ),
    createPermissionItem(
      'permission.key.feeConfiguration.fixedFare.view',
      EActions.View,
      IPermission.entity.CONFIG_FIXED_FARE,
    ),
    createPermissionItem(
      'permission.key.feeConfiguration.fixedFare.action',
      `${EActions.Create},${EActions.Update},${EActions.Delete}`,
      IPermission.entity.CONFIG_FIXED_FARE,
    ),
  ],
);
