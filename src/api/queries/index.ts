// generated with @7nohe/openapi-react-query-codegen@0.5.2
import type {
  UseMutationOptions,
  UseQueryOptions,
  UseQueryResult,
} from '@tanstack/react-query';
import { useMutation, useQuery } from '@tanstack/react-query';
import type { ArticleParamDto } from '../requests/models/ArticleParamDto';
import type { BannerParamDto } from '../requests/models/BannerParamDto';
import type { CancelBookingDto } from '../requests/models/CancelBookingDto';
import type { CancelSubscriptionDto } from '../requests/models/CancelSubscriptionDto';
import type { CheckWorkingAreaDto } from '../requests/models/CheckWorkingAreaDto';
import type { ConfigAnyCarDto } from '../requests/models/ConfigAnyCarDto';
import type { ConfigAnyCarStatusDto } from '../requests/models/ConfigAnyCarStatusDto';
import type { ConfigTipPositionDto } from '../requests/models/ConfigTipPositionDto';
import type { ConfirmOtpDto } from '../requests/models/ConfirmOtpDto';
import type { ConfirmRegisterDto } from '../requests/models/ConfirmRegisterDto';
import type { CreateAdminOfBusinessDto } from '../requests/models/CreateAdminOfBusinessDto';
import type { CreateArticleDto } from '../requests/models/CreateArticleDto';
import type { CreateBookingWithCodeDto } from '../requests/models/CreateBookingWithCodeDto';
import type { CreateBookingWithPaymentDto } from '../requests/models/CreateBookingWithPaymentDto';
import type { CreateBusinessBillToDto } from '../requests/models/CreateBusinessBillToDto';
import type { CreateBusinessDto } from '../requests/models/CreateBusinessDto';
import type { CreateCodeDto } from '../requests/models/CreateCodeDto';
import type { CreateCouponDto } from '../requests/models/CreateCouponDto';
import type { CreateDepartmentDto } from '../requests/models/CreateDepartmentDto';
import type { CreateDepartmentGroupDto } from '../requests/models/CreateDepartmentGroupDto';
import type { CreateDepartmentRoleDto } from '../requests/models/CreateDepartmentRoleDto';
import type { CreateDiscountDto } from '../requests/models/CreateDiscountDto';
import type { CreateEmployeeDto } from '../requests/models/CreateEmployeeDto';
import type { CreateEmployeeOfBusinessDto } from '../requests/models/CreateEmployeeOfBusinessDto';
import type { CreateEstimationDto } from '../requests/models/CreateEstimationDto';
import type { CreateFixedFareDto } from '../requests/models/CreateFixedFareDto';
import type { CreateLocationDto } from '../requests/models/CreateLocationDto';
import type { CreateNotificationDto } from '../requests/models/CreateNotificationDto';
import type { CreateNotificationTokenDto } from '../requests/models/CreateNotificationTokenDto';
import type { CreateRateFeeDto } from '../requests/models/CreateRateFeeDto';
import type { CreateRefundPaymentDto } from '../requests/models/CreateRefundPaymentDto';
import type { CreateReviewDto } from '../requests/models/CreateReviewDto';
import type { CreateRoleDto } from '../requests/models/CreateRoleDto';
import type { CreateServiceFeeDto } from '../requests/models/CreateServiceFeeDto';
import type { CreateSubscriptionCouponDto } from '../requests/models/CreateSubscriptionCouponDto';
import type { CreateSubscriptionDto } from '../requests/models/CreateSubscriptionDto';
import type { CreateSubscriptionV2Dto } from '../requests/models/CreateSubscriptionV2Dto';
import type { CreateSupportTicketDto } from '../requests/models/CreateSupportTicketDto';
import type { CreateUploadExcelPresignedUrlDto } from '../requests/models/CreateUploadExcelPresignedUrlDto';
import type { CreateUploadPresignedUrlDto } from '../requests/models/CreateUploadPresignedUrlDto';
import type { DisplayBannerDto } from '../requests/models/DisplayBannerDto';
import type { ForgotPasswordInputDto } from '../requests/models/ForgotPasswordInputDto';
import type { ImportEmailDto } from '../requests/models/ImportEmailDto';
import type { JoinBusinessDto } from '../requests/models/JoinBusinessDto';
import type { LoginAppleDto } from '../requests/models/LoginAppleDto';
import type { LoginGoogleDto } from '../requests/models/LoginGoogleDto';
import type { LoginInputDto } from '../requests/models/LoginInputDto';
import type { RecentLocationDto } from '../requests/models/RecentLocationDto';
import type { RedeemCouponDto } from '../requests/models/RedeemCouponDto';
import type { RefreshTokenInputDto } from '../requests/models/RefreshTokenInputDto';
import type { RegisterInputDto } from '../requests/models/RegisterInputDto';
import type { RequestOtpDto } from '../requests/models/RequestOtpDto';
import type { ResendCodeInputDto } from '../requests/models/ResendCodeInputDto';
import type { ResetPasswordInputDto } from '../requests/models/ResetPasswordInputDto';
import type { ResolveTicketDto } from '../requests/models/ResolveTicketDto';
import type { SavedLocationDto } from '../requests/models/SavedLocationDto';
import type { SeenAllNotificationsDto } from '../requests/models/SeenAllNotificationsDto';
import type { SubscribeSubscriptionDto } from '../requests/models/SubscribeSubscriptionDto';
import type { UpdateAdminOfBusinessDto } from '../requests/models/UpdateAdminOfBusinessDto';
import type { UpdateAvailableCarDto } from '../requests/models/UpdateAvailableCarDto';
import type { UpdateBusinessDto } from '../requests/models/UpdateBusinessDto';
import type { UpdateBusinessStatusDto } from '../requests/models/UpdateBusinessStatusDto';
import type { UpdateCodeStatusDto } from '../requests/models/UpdateCodeStatusDto';
import type { UpdateCouponDto } from '../requests/models/UpdateCouponDto';
import type { UpdateCouponStatusDto } from '../requests/models/UpdateCouponStatusDto';
import type { UpdateDefaultPaymentMethodDto } from '../requests/models/UpdateDefaultPaymentMethodDto';
import type { UpdateDepartmentDto } from '../requests/models/UpdateDepartmentDto';
import type { UpdateDepartmentGroupDto } from '../requests/models/UpdateDepartmentGroupDto';
import type { UpdateDepartmentRoleDto } from '../requests/models/UpdateDepartmentRoleDto';
import type { UpdateDepartmentStatusDto } from '../requests/models/UpdateDepartmentStatusDto';
import type { UpdateDisplayArticleDto } from '../requests/models/UpdateDisplayArticleDto';
import type { UpdateEmployeeDto } from '../requests/models/UpdateEmployeeDto';
import type { UpdateEmployeeOfBusinessDto } from '../requests/models/UpdateEmployeeOfBusinessDto';
import type { UpdateEmployeeStatusDto } from '../requests/models/UpdateEmployeeStatusDto';
import type { UpdateFixedFareDto } from '../requests/models/UpdateFixedFareDto';
import type { UpdateFixedFareStatusDto } from '../requests/models/UpdateFixedFareStatusDto';
import type { UpdateLocaleDto } from '../requests/models/UpdateLocaleDto';
import type { UpdateLocationDto } from '../requests/models/UpdateLocationDto';
import type { UpdateLocationStatusDto } from '../requests/models/UpdateLocationStatusDto';
import type { UpdateLoyalPointConfigDto } from '../requests/models/UpdateLoyalPointConfigDto';
import type { UpdateNotificationDto } from '../requests/models/UpdateNotificationDto';
import type { UpdatePositionsCouponDto } from '../requests/models/UpdatePositionsCouponDto';
import type { UpdateReferredUserDto } from '../requests/models/UpdateReferredUserDto';
import type { UpdateRoleDto } from '../requests/models/UpdateRoleDto';
import type { UpdateServiceFeeStatusDto } from '../requests/models/UpdateServiceFeeStatusDto';
import type { UpdateStatusArticleDto } from '../requests/models/UpdateStatusArticleDto';
import type { UpdateStatusBannerDto } from '../requests/models/UpdateStatusBannerDto';
import type { UpdateStatusNotificationDto } from '../requests/models/UpdateStatusNotificationDto';
import type { UpdateSubscriptionCardDto } from '../requests/models/UpdateSubscriptionCardDto';
import type { UpdateSubscriptionCouponDto } from '../requests/models/UpdateSubscriptionCouponDto';
import type { UpdateSubscriptionDto } from '../requests/models/UpdateSubscriptionDto';
import type { UpdateSubscriptionStatusDto } from '../requests/models/UpdateSubscriptionStatusDto';
import type { UpdateSubscriptionV2Dto } from '../requests/models/UpdateSubscriptionV2Dto';
import type { UpdateUserDto } from '../requests/models/UpdateUserDto';
import type { UpdateUserPasswordDto } from '../requests/models/UpdateUserPasswordDto';
import type { UpdateUserStatusDto } from '../requests/models/UpdateUserStatusDto';
import type { UpsertUserDto } from '../requests/models/UpsertUserDto';
import type { VerifyCodeInputDto } from '../requests/models/VerifyCodeInputDto';
import { AdminService } from '../requests/services/AdminService';
import { AppService } from '../requests/services/AppService';
import { ArticlesService } from '../requests/services/ArticlesService';
import { AuthService } from '../requests/services/AuthService';
import { AvailableCarsService } from '../requests/services/AvailableCarsService';
import { BannersService } from '../requests/services/BannersService';
import { BookingConfigsService } from '../requests/services/BookingConfigsService';
import { BookingsService } from '../requests/services/BookingsService';
import { BusinessesService } from '../requests/services/BusinessesService';
import { CodesService } from '../requests/services/CodesService';
import { ConfigsService } from '../requests/services/ConfigsService';
import { CouponsService } from '../requests/services/CouponsService';
import { DashboardService } from '../requests/services/DashboardService';
import { DefaultService } from '../requests/services/DefaultService';
import { DepartmentGroupsService } from '../requests/services/DepartmentGroupsService';
import { DepartmentRolesService } from '../requests/services/DepartmentRolesService';
import { DepartmentsService } from '../requests/services/DepartmentsService';
import { DriverService } from '../requests/services/DriverService';
import { EmployeesService } from '../requests/services/EmployeesService';
import { FeeConfigurationService } from '../requests/services/FeeConfigurationService';
import { LocationsService } from '../requests/services/LocationsService';
import { LoyalPointService } from '../requests/services/LoyalPointService';
import { NotificationsService } from '../requests/services/NotificationsService';
import { NotificationTokensService } from '../requests/services/NotificationTokensService';
import { PermissionsService } from '../requests/services/PermissionsService';
import { RolesService } from '../requests/services/RolesService';
import { StoragesService } from '../requests/services/StoragesService';
import { SubscriptionsService } from '../requests/services/SubscriptionsService';
import { SupportTicketService } from '../requests/services/SupportTicketService';
import { UsersService } from '../requests/services/UsersService';
import { VersionAppService } from '../requests/services/VersionAppService';

export type VersionAppServiceVersionAppControllerCheckVersionDefaultResponse =
  Awaited<
    ReturnType<typeof VersionAppService.versionAppControllerCheckVersion>
  >;

export type VersionAppServiceVersionAppControllerCheckVersionQueryResult<
  TData = VersionAppServiceVersionAppControllerCheckVersionDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useVersionAppServiceVersionAppControllerCheckVersionKey =
  'VersionAppServiceVersionAppControllerCheckVersion';

/**
 * Check version info
 */
export const useVersionAppServiceVersionAppControllerCheckVersion = <
  TData = VersionAppServiceVersionAppControllerCheckVersionDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    version,
    platform,
  }: {
    version: string;
    platform: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      VersionAppService.versionAppControllerCheckVersion(
        version,
        platform,
      ) as TData,
    queryKey: [
      useVersionAppServiceVersionAppControllerCheckVersionKey,
      ...(queryKey ?? [{ platform, version }]),
    ],
    ...options,
  });

export type UsersServiceUserControllerGetUserDefaultResponse = Awaited<
  ReturnType<typeof UsersService.userControllerGetUser>
>;

export type UsersServiceUserControllerGetUserQueryResult<
  TData = UsersServiceUserControllerGetUserDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useUsersServiceUserControllerGetUserKey =
  'UsersServiceUserControllerGetUser';

/**
 * Get current user information
 */
export const useUsersServiceUserControllerGetUser = <
  TData = UsersServiceUserControllerGetUserDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () => UsersService.userControllerGetUser() as TData,
    queryKey: [useUsersServiceUserControllerGetUserKey, ...(queryKey ?? [])],
    ...options,
  });

export type UsersServiceuserControllerUpdateMeMutationResult = Awaited<
  ReturnType<typeof UsersService.userControllerUpdateMe>
>;

/**
 * Update me
 */
export const useUsersServiceUserControllerUpdateMe = <
  TData = UsersServiceuserControllerUpdateMeMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: UpdateUserDto;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: UpdateUserDto;
    },
    TContext
  >({
    mutationFn: ({ requestBody }) =>
      UsersService.userControllerUpdateMe(
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type UsersServiceuserControllerDeleteMeMutationResult = Awaited<
  ReturnType<typeof UsersService.userControllerDeleteMe>
>;

/**
 * Delete me
 */
export const useUsersServiceUserControllerDeleteMe = <
  TData = UsersServiceuserControllerDeleteMeMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<TData, TError, void, TContext>,
    'mutationFn'
  >,
) =>
  useMutation<TData, TError, void, TContext>({
    mutationFn: () =>
      UsersService.userControllerDeleteMe() as unknown as Promise<TData>,
    ...options,
  });

export type UsersServiceuserControllerUpdatePassMutationResult = Awaited<
  ReturnType<typeof UsersService.userControllerUpdatePass>
>;

/**
 * Update a new password
 */
export const useUsersServiceUserControllerUpdatePass = <
  TData = UsersServiceuserControllerUpdatePassMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: UpdateUserPasswordDto;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: UpdateUserPasswordDto;
    },
    TContext
  >({
    mutationFn: ({ requestBody }) =>
      UsersService.userControllerUpdatePass(
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type UsersServiceuserControllerUpdateLocaleMutationResult = Awaited<
  ReturnType<typeof UsersService.userControllerUpdateLocale>
>;

/**
 * Update a locale of user
 */
export const useUsersServiceUserControllerUpdateLocale = <
  TData = UsersServiceuserControllerUpdateLocaleMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: UpdateLocaleDto;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: UpdateLocaleDto;
    },
    TContext
  >({
    mutationFn: ({ requestBody }) =>
      UsersService.userControllerUpdateLocale(
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type UsersServiceuserControllerJoinBusinessMutationResult = Awaited<
  ReturnType<typeof UsersService.userControllerJoinBusiness>
>;

/**
 * User join a business
 */
export const useUsersServiceUserControllerJoinBusiness = <
  TData = UsersServiceuserControllerJoinBusinessMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: JoinBusinessDto;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: JoinBusinessDto;
    },
    TContext
  >({
    mutationFn: ({ requestBody }) =>
      UsersService.userControllerJoinBusiness(
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type UsersServiceuserControllerAddSavedLocationMutationResult = Awaited<
  ReturnType<typeof UsersService.userControllerAddSavedLocation>
>;

/**
 * Add saved location
 */
export const useUsersServiceUserControllerAddSavedLocation = <
  TData = UsersServiceuserControllerAddSavedLocationMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: SavedLocationDto;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: SavedLocationDto;
    },
    TContext
  >({
    mutationFn: ({ requestBody }) =>
      UsersService.userControllerAddSavedLocation(
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type UsersServiceUserControllerGetSavedLocationDefaultResponse = Awaited<
  ReturnType<typeof UsersService.userControllerGetSavedLocation>
>;

export type UsersServiceUserControllerGetSavedLocationQueryResult<
  TData = UsersServiceUserControllerGetSavedLocationDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useUsersServiceUserControllerGetSavedLocationKey =
  'UsersServiceUserControllerGetSavedLocation';

/**
 * Get saved location of user
 */
export const useUsersServiceUserControllerGetSavedLocation = <
  TData = UsersServiceUserControllerGetSavedLocationDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () => UsersService.userControllerGetSavedLocation() as TData,
    queryKey: [
      useUsersServiceUserControllerGetSavedLocationKey,
      ...(queryKey ?? []),
    ],
    ...options,
  });

export type UsersServiceuserControllerUpdateSavedLocationMutationResult =
  Awaited<ReturnType<typeof UsersService.userControllerUpdateSavedLocation>>;

/**
 * Add saved location
 */
export const useUsersServiceUserControllerUpdateSavedLocation = <
  TData = UsersServiceuserControllerUpdateSavedLocationMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        id: string;
        requestBody: SavedLocationDto;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      id: string;
      requestBody: SavedLocationDto;
    },
    TContext
  >({
    mutationFn: ({ id, requestBody }) =>
      UsersService.userControllerUpdateSavedLocation(
        id,
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type UsersServiceuserControllerDeleteDepartmentGroupMutationResult =
  Awaited<ReturnType<typeof UsersService.userControllerDeleteDepartmentGroup>>;

/**
 * Delete a saved location
 */
export const useUsersServiceUserControllerDeleteDepartmentGroup = <
  TData = UsersServiceuserControllerDeleteDepartmentGroupMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        id: string;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      id: string;
    },
    TContext
  >({
    mutationFn: ({ id }) =>
      UsersService.userControllerDeleteDepartmentGroup(
        id,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type UsersServiceUserControllerListCardsDefaultResponse = Awaited<
  ReturnType<typeof UsersService.userControllerListCards>
>;

export type UsersServiceUserControllerListCardsQueryResult<
  TData = UsersServiceUserControllerListCardsDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useUsersServiceUserControllerListCardsKey =
  'UsersServiceUserControllerListCards';

/**
 * Get list user credit cards
 */
export const useUsersServiceUserControllerListCards = <
  TData = UsersServiceUserControllerListCardsDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () => UsersService.userControllerListCards() as TData,
    queryKey: [useUsersServiceUserControllerListCardsKey, ...(queryKey ?? [])],
    ...options,
  });

export type UsersServiceuserControllerCreateUserCreditCardMutationResult =
  Awaited<ReturnType<typeof UsersService.userControllerCreateUserCreditCard>>;

/**
 * Add user credit card
 */
export const useUsersServiceUserControllerCreateUserCreditCard = <
  TData = UsersServiceuserControllerCreateUserCreditCardMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<TData, TError, void, TContext>,
    'mutationFn'
  >,
) =>
  useMutation<TData, TError, void, TContext>({
    mutationFn: () =>
      UsersService.userControllerCreateUserCreditCard() as unknown as Promise<TData>,
    ...options,
  });

export type UsersServiceuserControllerSetDefaultCardMutationResult = Awaited<
  ReturnType<typeof UsersService.userControllerSetDefaultCard>
>;

/**
 * Set default card
 */
export const useUsersServiceUserControllerSetDefaultCard = <
  TData = UsersServiceuserControllerSetDefaultCardMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        id: string;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      id: string;
    },
    TContext
  >({
    mutationFn: ({ id }) =>
      UsersService.userControllerSetDefaultCard(
        id,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type UsersServiceuserControllerDeleteUserCreditCardMutationResult =
  Awaited<ReturnType<typeof UsersService.userControllerDeleteUserCreditCard>>;

/**
 * Delete a card
 */
export const useUsersServiceUserControllerDeleteUserCreditCard = <
  TData = UsersServiceuserControllerDeleteUserCreditCardMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        id: string;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      id: string;
    },
    TContext
  >({
    mutationFn: ({ id }) =>
      UsersService.userControllerDeleteUserCreditCard(
        id,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type UsersServiceUserControllerGetRecentLocationDefaultResponse =
  Awaited<ReturnType<typeof UsersService.userControllerGetRecentLocation>>;

export type UsersServiceUserControllerGetRecentLocationQueryResult<
  TData = UsersServiceUserControllerGetRecentLocationDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useUsersServiceUserControllerGetRecentLocationKey =
  'UsersServiceUserControllerGetRecentLocation';

/**
 * Get list if recent location of a user
 */
export const useUsersServiceUserControllerGetRecentLocation = <
  TData = UsersServiceUserControllerGetRecentLocationDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () => UsersService.userControllerGetRecentLocation() as TData,
    queryKey: [
      useUsersServiceUserControllerGetRecentLocationKey,
      ...(queryKey ?? []),
    ],
    ...options,
  });

export type UsersServiceuserControllerAddRecentLocationMutationResult = Awaited<
  ReturnType<typeof UsersService.userControllerAddRecentLocation>
>;

/**
 * Add recent location
 */
export const useUsersServiceUserControllerAddRecentLocation = <
  TData = UsersServiceuserControllerAddRecentLocationMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: RecentLocationDto;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: RecentLocationDto;
    },
    TContext
  >({
    mutationFn: ({ requestBody }) =>
      UsersService.userControllerAddRecentLocation(
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type UsersServiceUserControllerGetReferralSummaryDefaultResponse =
  Awaited<ReturnType<typeof UsersService.userControllerGetReferralSummary>>;

export type UsersServiceUserControllerGetReferralSummaryQueryResult<
  TData = UsersServiceUserControllerGetReferralSummaryDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useUsersServiceUserControllerGetReferralSummaryKey =
  'UsersServiceUserControllerGetReferralSummary';

/**
 * Get static summary of referral
 */
export const useUsersServiceUserControllerGetReferralSummary = <
  TData = UsersServiceUserControllerGetReferralSummaryDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () => UsersService.userControllerGetReferralSummary() as TData,
    queryKey: [
      useUsersServiceUserControllerGetReferralSummaryKey,
      ...(queryKey ?? []),
    ],
    ...options,
  });

export type UsersServiceUserControllerGetSuccessfulReferralsDefaultResponse =
  Awaited<ReturnType<typeof UsersService.userControllerGetSuccessfulReferrals>>;

export type UsersServiceUserControllerGetSuccessfulReferralsQueryResult<
  TData = UsersServiceUserControllerGetSuccessfulReferralsDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useUsersServiceUserControllerGetSuccessfulReferralsKey =
  'UsersServiceUserControllerGetSuccessfulReferrals';

/**
 * Get list of successful referral users
 */
export const useUsersServiceUserControllerGetSuccessfulReferrals = <
  TData = UsersServiceUserControllerGetSuccessfulReferralsDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    limit,
    offset,
  }: {
    limit?: number;
    offset: number;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      UsersService.userControllerGetSuccessfulReferrals(limit, offset) as TData,
    queryKey: [
      useUsersServiceUserControllerGetSuccessfulReferralsKey,
      ...(queryKey ?? [{ limit, offset }]),
    ],
    ...options,
  });

export type UsersServiceuserControllerUpdateReferredUserMutationResult =
  Awaited<ReturnType<typeof UsersService.userControllerUpdateReferredUser>>;

/**
 * Update referral user
 */
export const useUsersServiceUserControllerUpdateReferredUser = <
  TData = UsersServiceuserControllerUpdateReferredUserMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: UpdateReferredUserDto;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: UpdateReferredUserDto;
    },
    TContext
  >({
    mutationFn: ({ requestBody }) =>
      UsersService.userControllerUpdateReferredUser(
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type UsersServiceuserControllerSendVerificationCodeMutationResult =
  Awaited<ReturnType<typeof UsersService.userControllerSendVerificationCode>>;

/**
 * Send a verify code
 */
export const useUsersServiceUserControllerSendVerificationCode = <
  TData = UsersServiceuserControllerSendVerificationCodeMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: RequestOtpDto;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: RequestOtpDto;
    },
    TContext
  >({
    mutationFn: ({ requestBody }) =>
      UsersService.userControllerSendVerificationCode(
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type UsersServiceuserControllerResendVerificationCodeMutationResult =
  Awaited<ReturnType<typeof UsersService.userControllerResendVerificationCode>>;

/**
 * Send a verify code
 */
export const useUsersServiceUserControllerResendVerificationCode = <
  TData = UsersServiceuserControllerResendVerificationCodeMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: RequestOtpDto;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: RequestOtpDto;
    },
    TContext
  >({
    mutationFn: ({ requestBody }) =>
      UsersService.userControllerResendVerificationCode(
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type UsersServiceuserControllerVerifyCodeMutationResult = Awaited<
  ReturnType<typeof UsersService.userControllerVerifyCode>
>;

/**
 * Verify code
 */
export const useUsersServiceUserControllerVerifyCode = <
  TData = UsersServiceuserControllerVerifyCodeMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: ConfirmOtpDto;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: ConfirmOtpDto;
    },
    TContext
  >({
    mutationFn: ({ requestBody }) =>
      UsersService.userControllerVerifyCode(
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type UsersServiceUserControllerIsVerifyPhoneNumberDefaultResponse =
  Awaited<ReturnType<typeof UsersService.userControllerIsVerifyPhoneNumber>>;

export type UsersServiceUserControllerIsVerifyPhoneNumberQueryResult<
  TData = UsersServiceUserControllerIsVerifyPhoneNumberDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useUsersServiceUserControllerIsVerifyPhoneNumberKey =
  'UsersServiceUserControllerIsVerifyPhoneNumber';

/**
 * Check user phone number is verified
 */
export const useUsersServiceUserControllerIsVerifyPhoneNumber = <
  TData = UsersServiceUserControllerIsVerifyPhoneNumberDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () => UsersService.userControllerIsVerifyPhoneNumber() as TData,
    queryKey: [
      useUsersServiceUserControllerIsVerifyPhoneNumberKey,
      ...(queryKey ?? []),
    ],
    ...options,
  });

export type UsersServiceuserControllerImportEmailsMutationResult = Awaited<
  ReturnType<typeof UsersService.userControllerImportEmails>
>;

/**
 * Import user email from excel
 */
export const useUsersServiceUserControllerImportEmails = <
  TData = UsersServiceuserControllerImportEmailsMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: ImportEmailDto;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: ImportEmailDto;
    },
    TContext
  >({
    mutationFn: ({ requestBody }) =>
      UsersService.userControllerImportEmails(
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type UsersServiceUserSuperAdminControllerGetIndividualsDefaultResponse =
  Awaited<
    ReturnType<typeof UsersService.userSuperAdminControllerGetIndividuals>
  >;

export type UsersServiceUserSuperAdminControllerGetIndividualsQueryResult<
  TData = UsersServiceUserSuperAdminControllerGetIndividualsDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useUsersServiceUserSuperAdminControllerGetIndividualsKey =
  'UsersServiceUserSuperAdminControllerGetIndividuals';

/**
 * Get list of users
 */
export const useUsersServiceUserSuperAdminControllerGetIndividuals = <
  TData = UsersServiceUserSuperAdminControllerGetIndividualsDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    limit,
    offset,
    q,
    subscriptionId,
    createdFrom,
    createdTo,
    userId,
    order,
  }: {
    limit?: number;
    offset: number;
    q?: string | null;
    subscriptionId?: string | null;
    createdFrom?: string | null;
    createdTo?: string | null;
    userId?: string;
    order?: string | null;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      UsersService.userSuperAdminControllerGetIndividuals(
        limit,
        offset,
        q,
        subscriptionId,
        createdFrom,
        createdTo,
        userId,
        order,
      ) as TData,
    queryKey: [
      useUsersServiceUserSuperAdminControllerGetIndividualsKey,
      ...(queryKey ?? [
        {
          createdFrom,
          createdTo,
          limit,
          offset,
          order,
          q,
          subscriptionId,
          userId,
        },
      ]),
    ],
    ...options,
  });

export type UsersServiceUserSuperAdminControllerGetIndividualsFilterDefaultResponse =
  Awaited<
    ReturnType<typeof UsersService.userSuperAdminControllerGetIndividualsFilter>
  >;

export type UsersServiceUserSuperAdminControllerGetIndividualsFilterQueryResult<
  TData = UsersServiceUserSuperAdminControllerGetIndividualsFilterDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useUsersServiceUserSuperAdminControllerGetIndividualsFilterKey =
  'UsersServiceUserSuperAdminControllerGetIndividualsFilter';

/**
 * Get list filter of users
 */
export const useUsersServiceUserSuperAdminControllerGetIndividualsFilter = <
  TData = UsersServiceUserSuperAdminControllerGetIndividualsFilterDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    limit,
    offset,
    q,
    subscriptionId,
    createdFrom,
    createdTo,
    userId,
    order,
  }: {
    limit?: number;
    offset: number;
    q?: string | null;
    subscriptionId?: string | null;
    createdFrom?: string | null;
    createdTo?: string | null;
    userId?: string;
    order?: string | null;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      UsersService.userSuperAdminControllerGetIndividualsFilter(
        limit,
        offset,
        q,
        subscriptionId,
        createdFrom,
        createdTo,
        userId,
        order,
      ) as TData,
    queryKey: [
      useUsersServiceUserSuperAdminControllerGetIndividualsFilterKey,
      ...(queryKey ?? [
        {
          createdFrom,
          createdTo,
          limit,
          offset,
          order,
          q,
          subscriptionId,
          userId,
        },
      ]),
    ],
    ...options,
  });

export type UsersServiceUserSuperAdminControllerGetUserDefaultResponse =
  Awaited<ReturnType<typeof UsersService.userSuperAdminControllerGetUser>>;

export type UsersServiceUserSuperAdminControllerGetUserQueryResult<
  TData = UsersServiceUserSuperAdminControllerGetUserDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useUsersServiceUserSuperAdminControllerGetUserKey =
  'UsersServiceUserSuperAdminControllerGetUser';

/**
 * Get a user
 */
export const useUsersServiceUserSuperAdminControllerGetUser = <
  TData = UsersServiceUserSuperAdminControllerGetUserDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    id,
  }: {
    id: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () => UsersService.userSuperAdminControllerGetUser(id) as TData,
    queryKey: [
      useUsersServiceUserSuperAdminControllerGetUserKey,
      ...(queryKey ?? [{ id }]),
    ],
    ...options,
  });

export type UsersServiceuserSuperAdminControllerDeleteAdminOfBusinessMutationResult =
  Awaited<
    ReturnType<
      typeof UsersService.userSuperAdminControllerDeleteAdminOfBusiness
    >
  >;

/**
 * Delete an user
 */
export const useUsersServiceUserSuperAdminControllerDeleteAdminOfBusiness = <
  TData = UsersServiceuserSuperAdminControllerDeleteAdminOfBusinessMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        id: string;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      id: string;
    },
    TContext
  >({
    mutationFn: ({ id }) =>
      UsersService.userSuperAdminControllerDeleteAdminOfBusiness(
        id,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type UsersServiceuserSuperAdminControllerUpdateUserStatusMutationResult =
  Awaited<
    ReturnType<typeof UsersService.userSuperAdminControllerUpdateUserStatus>
  >;

/**
 * Update status of a user
 */
export const useUsersServiceUserSuperAdminControllerUpdateUserStatus = <
  TData = UsersServiceuserSuperAdminControllerUpdateUserStatusMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        id: string;
        requestBody: UpdateUserStatusDto;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      id: string;
      requestBody: UpdateUserStatusDto;
    },
    TContext
  >({
    mutationFn: ({ id, requestBody }) =>
      UsersService.userSuperAdminControllerUpdateUserStatus(
        id,
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type UsersServiceUserSuperAdminControllerExportPersonalCustomersDefaultResponse =
  Awaited<
    ReturnType<
      typeof UsersService.userSuperAdminControllerExportPersonalCustomers
    >
  >;

export type UsersServiceUserSuperAdminControllerExportPersonalCustomersQueryResult<
  TData = UsersServiceUserSuperAdminControllerExportPersonalCustomersDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useUsersServiceUserSuperAdminControllerExportPersonalCustomersKey =
  'UsersServiceUserSuperAdminControllerExportPersonalCustomers';

/**
 * Export individuals history
 */
export const useUsersServiceUserSuperAdminControllerExportPersonalCustomers = <
  TData = UsersServiceUserSuperAdminControllerExportPersonalCustomersDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    q,
    subscriptionId,
    createdFrom,
    createdTo,
    userId,
    order,
  }: {
    q?: string | null;
    subscriptionId?: string | null;
    createdFrom?: string | null;
    createdTo?: string | null;
    userId?: string;
    order?: string | null;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      UsersService.userSuperAdminControllerExportPersonalCustomers(
        q,
        subscriptionId,
        createdFrom,
        createdTo,
        userId,
        order,
      ) as TData,
    queryKey: [
      useUsersServiceUserSuperAdminControllerExportPersonalCustomersKey,
      ...(queryKey ?? [
        { createdFrom, createdTo, order, q, subscriptionId, userId },
      ]),
    ],
    ...options,
  });

export type UsersServiceUserV2ControllerListCardsDefaultResponse = Awaited<
  ReturnType<typeof UsersService.userV2ControllerListCards>
>;

export type UsersServiceUserV2ControllerListCardsQueryResult<
  TData = UsersServiceUserV2ControllerListCardsDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useUsersServiceUserV2ControllerListCardsKey =
  'UsersServiceUserV2ControllerListCards';

/**
 * Get list user credit cards
 */
export const useUsersServiceUserV2ControllerListCards = <
  TData = UsersServiceUserV2ControllerListCardsDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () => UsersService.userV2ControllerListCards() as TData,
    queryKey: [
      useUsersServiceUserV2ControllerListCardsKey,
      ...(queryKey ?? []),
    ],
    ...options,
  });

export type UsersServiceuserV2ControllerSetDefaultCardMutationResult = Awaited<
  ReturnType<typeof UsersService.userV2ControllerSetDefaultCard>
>;

/**
 * Set default card
 */
export const useUsersServiceUserV2ControllerSetDefaultCard = <
  TData = UsersServiceuserV2ControllerSetDefaultCardMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: UpdateDefaultPaymentMethodDto;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: UpdateDefaultPaymentMethodDto;
    },
    TContext
  >({
    mutationFn: ({ requestBody }) =>
      UsersService.userV2ControllerSetDefaultCard(
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type UsersServiceEmployeeCodeControllerGetCodesOfUserDefaultResponse =
  Awaited<ReturnType<typeof UsersService.employeeCodeControllerGetCodesOfUser>>;

export type UsersServiceEmployeeCodeControllerGetCodesOfUserQueryResult<
  TData = UsersServiceEmployeeCodeControllerGetCodesOfUserDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useUsersServiceEmployeeCodeControllerGetCodesOfUserKey =
  'UsersServiceEmployeeCodeControllerGetCodesOfUser';

/**
 * Get list of code of a user
 */
export const useUsersServiceEmployeeCodeControllerGetCodesOfUser = <
  TData = UsersServiceEmployeeCodeControllerGetCodesOfUserDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    limit,
    offset,
    order,
  }: {
    limit?: number;
    offset: number;
    order?: string | null;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      UsersService.employeeCodeControllerGetCodesOfUser(
        limit,
        offset,
        order,
      ) as TData,
    queryKey: [
      useUsersServiceEmployeeCodeControllerGetCodesOfUserKey,
      ...(queryKey ?? [{ limit, offset, order }]),
    ],
    ...options,
  });

export type UsersServiceemployeeCodeControllerDeleteEmployeeCodeMutationResult =
  Awaited<
    ReturnType<typeof UsersService.employeeCodeControllerDeleteEmployeeCode>
  >;

/**
 * Delete a code by user
 */
export const useUsersServiceEmployeeCodeControllerDeleteEmployeeCode = <
  TData = UsersServiceemployeeCodeControllerDeleteEmployeeCodeMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        id: string;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      id: string;
    },
    TContext
  >({
    mutationFn: ({ id }) =>
      UsersService.employeeCodeControllerDeleteEmployeeCode(
        id,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type UsersServiceEmployeeCodeControllerGetEmployeesOfCodeDefaultResponse =
  Awaited<
    ReturnType<typeof UsersService.employeeCodeControllerGetEmployeesOfCode>
  >;

export type UsersServiceEmployeeCodeControllerGetEmployeesOfCodeQueryResult<
  TData = UsersServiceEmployeeCodeControllerGetEmployeesOfCodeDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useUsersServiceEmployeeCodeControllerGetEmployeesOfCodeKey =
  'UsersServiceEmployeeCodeControllerGetEmployeesOfCode';

/**
 * Get list of employee of a business
 */
export const useUsersServiceEmployeeCodeControllerGetEmployeesOfCode = <
  TData = UsersServiceEmployeeCodeControllerGetEmployeesOfCodeDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    id,
    limit,
    offset,
    employeeQ,
    order,
  }: {
    id: string;
    limit?: number;
    offset: number;
    employeeQ?: string | null;
    order?: string | null;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      UsersService.employeeCodeControllerGetEmployeesOfCode(
        id,
        limit,
        offset,
        employeeQ,
        order,
      ) as TData,
    queryKey: [
      useUsersServiceEmployeeCodeControllerGetEmployeesOfCodeKey,
      ...(queryKey ?? [{ employeeQ, id, limit, offset, order }]),
    ],
    ...options,
  });

export type UsersServiceUserCouponControllerGetCouponListsDefaultResponse =
  Awaited<ReturnType<typeof UsersService.userCouponControllerGetCouponLists>>;

export type UsersServiceUserCouponControllerGetCouponListsQueryResult<
  TData = UsersServiceUserCouponControllerGetCouponListsDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useUsersServiceUserCouponControllerGetCouponListsKey =
  'UsersServiceUserCouponControllerGetCouponLists';

/**
 * Get list of coupon on setting
 */
export const useUsersServiceUserCouponControllerGetCouponLists = <
  TData = UsersServiceUserCouponControllerGetCouponListsDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () => UsersService.userCouponControllerGetCouponLists() as TData,
    queryKey: [
      useUsersServiceUserCouponControllerGetCouponListsKey,
      ...(queryKey ?? []),
    ],
    ...options,
  });

export type UsersServiceUserCouponControllerGetMyCouponsDefaultResponse =
  Awaited<ReturnType<typeof UsersService.userCouponControllerGetMyCoupons>>;

export type UsersServiceUserCouponControllerGetMyCouponsQueryResult<
  TData = UsersServiceUserCouponControllerGetMyCouponsDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useUsersServiceUserCouponControllerGetMyCouponsKey =
  'UsersServiceUserCouponControllerGetMyCoupons';

/**
 * Get list of my coupon
 */
export const useUsersServiceUserCouponControllerGetMyCoupons = <
  TData = UsersServiceUserCouponControllerGetMyCouponsDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    bookingType,
    vehicleType,
    amount,
    date,
    originLatitude,
    originLongitude,
    destinationLatitude,
    destinationLongitude,
    paymentMethod,
  }: {
    bookingType: 'PERSONAL' | 'BUSINESS';
    vehicleType:
      | 'ALL'
      | 'TYPICAL_CAR'
      | 'ELECTRIC_CAR'
      | 'VAN'
      | 'ACCESSIBLE_VAN'
      | 'ANY_CAR';
    amount: number;
    date: string;
    originLatitude: number;
    originLongitude: number;
    destinationLatitude: number;
    destinationLongitude: number;
    paymentMethod?: 'ALL' | 'CARD' | 'CASH' | null;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      UsersService.userCouponControllerGetMyCoupons(
        bookingType,
        vehicleType,
        amount,
        date,
        originLatitude,
        originLongitude,
        destinationLatitude,
        destinationLongitude,
        paymentMethod,
      ) as TData,
    queryKey: [
      useUsersServiceUserCouponControllerGetMyCouponsKey,
      ...(queryKey ?? [
        {
          amount,
          bookingType,
          date,
          destinationLatitude,
          destinationLongitude,
          originLatitude,
          originLongitude,
          paymentMethod,
          vehicleType,
        },
      ]),
    ],
    ...options,
  });

export type UsersServiceUserCouponControllerSearchCouponOnListDefaultResponse =
  Awaited<
    ReturnType<typeof UsersService.userCouponControllerSearchCouponOnList>
  >;

export type UsersServiceUserCouponControllerSearchCouponOnListQueryResult<
  TData = UsersServiceUserCouponControllerSearchCouponOnListDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useUsersServiceUserCouponControllerSearchCouponOnListKey =
  'UsersServiceUserCouponControllerSearchCouponOnList';

/**
 * Get a coupon by code on coupon list
 */
export const useUsersServiceUserCouponControllerSearchCouponOnList = <
  TData = UsersServiceUserCouponControllerSearchCouponOnListDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    code,
  }: {
    code: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      UsersService.userCouponControllerSearchCouponOnList(code) as TData,
    queryKey: [
      useUsersServiceUserCouponControllerSearchCouponOnListKey,
      ...(queryKey ?? [{ code }]),
    ],
    ...options,
  });

export type UsersServiceUserCouponControllerSearchManualCouponDefaultResponse =
  Awaited<
    ReturnType<typeof UsersService.userCouponControllerSearchManualCoupon>
  >;

export type UsersServiceUserCouponControllerSearchManualCouponQueryResult<
  TData = UsersServiceUserCouponControllerSearchManualCouponDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useUsersServiceUserCouponControllerSearchManualCouponKey =
  'UsersServiceUserCouponControllerSearchManualCoupon';

/**
 * Get a coupon by code
 */
export const useUsersServiceUserCouponControllerSearchManualCoupon = <
  TData = UsersServiceUserCouponControllerSearchManualCouponDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    bookingType,
    vehicleType,
    amount,
    date,
    originLatitude,
    originLongitude,
    destinationLatitude,
    destinationLongitude,
    code,
    paymentMethod,
  }: {
    bookingType: 'PERSONAL' | 'BUSINESS';
    vehicleType:
      | 'ALL'
      | 'TYPICAL_CAR'
      | 'ELECTRIC_CAR'
      | 'VAN'
      | 'ACCESSIBLE_VAN'
      | 'ANY_CAR';
    amount: number;
    date: string;
    originLatitude: number;
    originLongitude: number;
    destinationLatitude: number;
    destinationLongitude: number;
    code: string;
    paymentMethod?: 'ALL' | 'CARD' | 'CASH' | null;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      UsersService.userCouponControllerSearchManualCoupon(
        bookingType,
        vehicleType,
        amount,
        date,
        originLatitude,
        originLongitude,
        destinationLatitude,
        destinationLongitude,
        code,
        paymentMethod,
      ) as TData,
    queryKey: [
      useUsersServiceUserCouponControllerSearchManualCouponKey,
      ...(queryKey ?? [
        {
          amount,
          bookingType,
          code,
          date,
          destinationLatitude,
          destinationLongitude,
          originLatitude,
          originLongitude,
          paymentMethod,
          vehicleType,
        },
      ]),
    ],
    ...options,
  });

export type UsersServiceuserCouponControllerRedeemCouponsStoreMutationResult =
  Awaited<
    ReturnType<typeof UsersService.userCouponControllerRedeemCouponsStore>
  >;

/**
 * Redeem coupon in coupon store
 */
export const useUsersServiceUserCouponControllerRedeemCouponsStore = <
  TData = UsersServiceuserCouponControllerRedeemCouponsStoreMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: RedeemCouponDto;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: RedeemCouponDto;
    },
    TContext
  >({
    mutationFn: ({ requestBody }) =>
      UsersService.userCouponControllerRedeemCouponsStore(
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type UsersServiceBusinessAdminControllerGetBusinessAdminDefaultResponse =
  Awaited<
    ReturnType<typeof UsersService.businessAdminControllerGetBusinessAdmin>
  >;

export type UsersServiceBusinessAdminControllerGetBusinessAdminQueryResult<
  TData = UsersServiceBusinessAdminControllerGetBusinessAdminDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useUsersServiceBusinessAdminControllerGetBusinessAdminKey =
  'UsersServiceBusinessAdminControllerGetBusinessAdmin';

/**
 * Get business admin information
 */
export const useUsersServiceBusinessAdminControllerGetBusinessAdmin = <
  TData = UsersServiceBusinessAdminControllerGetBusinessAdminDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    id,
  }: {
    id: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      UsersService.businessAdminControllerGetBusinessAdmin(id) as TData,
    queryKey: [
      useUsersServiceBusinessAdminControllerGetBusinessAdminKey,
      ...(queryKey ?? [{ id }]),
    ],
    ...options,
  });

export type SupportTicketServicesupportTicketControllerCreateSupportTicketMutationResult =
  Awaited<
    ReturnType<
      typeof SupportTicketService.supportTicketControllerCreateSupportTicket
    >
  >;

/**
 * Create support ticket from user
 */
export const useSupportTicketServiceSupportTicketControllerCreateSupportTicket =
  <
    TData = SupportTicketServicesupportTicketControllerCreateSupportTicketMutationResult,
    TError = unknown,
    TContext = unknown,
  >(
    options?: Omit<
      UseMutationOptions<
        TData,
        TError,
        {
          requestBody: CreateSupportTicketDto;
        },
        TContext
      >,
      'mutationFn'
    >,
  ) =>
    useMutation<
      TData,
      TError,
      {
        requestBody: CreateSupportTicketDto;
      },
      TContext
    >({
      mutationFn: ({ requestBody }) =>
        SupportTicketService.supportTicketControllerCreateSupportTicket(
          requestBody,
        ) as unknown as Promise<TData>,
      ...options,
    });

export type SupportTicketServiceSupportTicketAdminControllerGetListSupportTicketDefaultResponse =
  Awaited<
    ReturnType<
      typeof SupportTicketService.supportTicketAdminControllerGetListSupportTicket
    >
  >;

export type SupportTicketServiceSupportTicketAdminControllerGetListSupportTicketQueryResult<
  TData = SupportTicketServiceSupportTicketAdminControllerGetListSupportTicketDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useSupportTicketServiceSupportTicketAdminControllerGetListSupportTicketKey =
  'SupportTicketServiceSupportTicketAdminControllerGetListSupportTicket';

/**
 * Get list support ticket
 */
export const useSupportTicketServiceSupportTicketAdminControllerGetListSupportTicket =
  <
    TData = SupportTicketServiceSupportTicketAdminControllerGetListSupportTicketDefaultResponse,
    TError = unknown,
    TQueryKey extends unknown[] = unknown[],
  >(
    {
      limit,
      offset,
      q,
      status,
      issueType,
      resolvedById,
      bookingId,
      solvedDateFrom,
      solvedDateTo,
      receivedDateFrom,
      receivedDateTo,
      order,
    }: {
      limit?: number;
      offset: number;
      q?: string | null;
      status?: 'OPEN' | 'CLOSED' | null;
      issueType?: 'PAYMENT_ISSUE' | 'LOST_ITEM' | 'OTHER' | null;
      resolvedById?: string | null;
      bookingId?: string;
      solvedDateFrom?: string | null;
      solvedDateTo?: string | null;
      receivedDateFrom?: string | null;
      receivedDateTo?: string | null;
      order?: string | null;
    },
    queryKey?: TQueryKey,
    options?: Omit<
      UseQueryOptions<TData, TError>,
      'queryKey' | 'queryFn' | 'initialData'
    >,
  ) =>
    useQuery<TData, TError>({
      queryFn: () =>
        SupportTicketService.supportTicketAdminControllerGetListSupportTicket(
          limit,
          offset,
          q,
          status,
          issueType,
          resolvedById,
          bookingId,
          solvedDateFrom,
          solvedDateTo,
          receivedDateFrom,
          receivedDateTo,
          order,
        ) as TData,
      queryKey: [
        useSupportTicketServiceSupportTicketAdminControllerGetListSupportTicketKey,
        ...(queryKey ?? [
          {
            bookingId,
            issueType,
            limit,
            offset,
            order,
            q,
            receivedDateFrom,
            receivedDateTo,
            resolvedById,
            solvedDateFrom,
            solvedDateTo,
            status,
          },
        ]),
      ],
      ...options,
    });

export type SupportTicketServiceSupportTicketControllerGetListOfIssueReasonDefaultResponse =
  Awaited<
    ReturnType<
      typeof SupportTicketService.supportTicketControllerGetListOfIssueReason
    >
  >;

export type SupportTicketServiceSupportTicketControllerGetListOfIssueReasonQueryResult<
  TData = SupportTicketServiceSupportTicketControllerGetListOfIssueReasonDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useSupportTicketServiceSupportTicketControllerGetListOfIssueReasonKey =
  'SupportTicketServiceSupportTicketControllerGetListOfIssueReason';

/**
 * Get list of issue reason
 */
export const useSupportTicketServiceSupportTicketControllerGetListOfIssueReason =
  <
    TData = SupportTicketServiceSupportTicketControllerGetListOfIssueReasonDefaultResponse,
    TError = unknown,
    TQueryKey extends unknown[] = unknown[],
  >(
    queryKey?: TQueryKey,
    options?: Omit<
      UseQueryOptions<TData, TError>,
      'queryKey' | 'queryFn' | 'initialData'
    >,
  ) =>
    useQuery<TData, TError>({
      queryFn: () =>
        SupportTicketService.supportTicketControllerGetListOfIssueReason() as TData,
      queryKey: [
        useSupportTicketServiceSupportTicketControllerGetListOfIssueReasonKey,
        ...(queryKey ?? []),
      ],
      ...options,
    });

export type SupportTicketServiceSupportTicketAdminControllerGetListSupportProgressOfBookingDefaultResponse =
  Awaited<
    ReturnType<
      typeof SupportTicketService.supportTicketAdminControllerGetListSupportProgressOfBooking
    >
  >;

export type SupportTicketServiceSupportTicketAdminControllerGetListSupportProgressOfBookingQueryResult<
  TData = SupportTicketServiceSupportTicketAdminControllerGetListSupportProgressOfBookingDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useSupportTicketServiceSupportTicketAdminControllerGetListSupportProgressOfBookingKey =
  'SupportTicketServiceSupportTicketAdminControllerGetListSupportProgressOfBooking';

/**
 * Get list support progress of booking
 */
export const useSupportTicketServiceSupportTicketAdminControllerGetListSupportProgressOfBooking =
  <
    TData = SupportTicketServiceSupportTicketAdminControllerGetListSupportProgressOfBookingDefaultResponse,
    TError = unknown,
    TQueryKey extends unknown[] = unknown[],
  >(
    {
      bookingId,
      limit,
      offset,
    }: {
      bookingId: string;
      limit?: number;
      offset: number;
    },
    queryKey?: TQueryKey,
    options?: Omit<
      UseQueryOptions<TData, TError>,
      'queryKey' | 'queryFn' | 'initialData'
    >,
  ) =>
    useQuery<TData, TError>({
      queryFn: () =>
        SupportTicketService.supportTicketAdminControllerGetListSupportProgressOfBooking(
          bookingId,
          limit,
          offset,
        ) as TData,
      queryKey: [
        useSupportTicketServiceSupportTicketAdminControllerGetListSupportProgressOfBookingKey,
        ...(queryKey ?? [{ bookingId, limit, offset }]),
      ],
      ...options,
    });

export type SupportTicketServicesupportTicketAdminControllerCreateRefundPaymentMutationResult =
  Awaited<
    ReturnType<
      typeof SupportTicketService.supportTicketAdminControllerCreateRefundPayment
    >
  >;

/**
 * Create refund payment
 */
export const useSupportTicketServiceSupportTicketAdminControllerCreateRefundPayment =
  <
    TData = SupportTicketServicesupportTicketAdminControllerCreateRefundPaymentMutationResult,
    TError = unknown,
    TContext = unknown,
  >(
    options?: Omit<
      UseMutationOptions<
        TData,
        TError,
        {
          bookingId: string;
          requestBody: CreateRefundPaymentDto;
        },
        TContext
      >,
      'mutationFn'
    >,
  ) =>
    useMutation<
      TData,
      TError,
      {
        bookingId: string;
        requestBody: CreateRefundPaymentDto;
      },
      TContext
    >({
      mutationFn: ({ bookingId, requestBody }) =>
        SupportTicketService.supportTicketAdminControllerCreateRefundPayment(
          bookingId,
          requestBody,
        ) as unknown as Promise<TData>,
      ...options,
    });

export type SupportTicketServiceSupportTicketAdminControllerGetSupportTicketDetailDefaultResponse =
  Awaited<
    ReturnType<
      typeof SupportTicketService.supportTicketAdminControllerGetSupportTicketDetail
    >
  >;

export type SupportTicketServiceSupportTicketAdminControllerGetSupportTicketDetailQueryResult<
  TData = SupportTicketServiceSupportTicketAdminControllerGetSupportTicketDetailDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useSupportTicketServiceSupportTicketAdminControllerGetSupportTicketDetailKey =
  'SupportTicketServiceSupportTicketAdminControllerGetSupportTicketDetail';

/**
 * get support ticket detail
 */
export const useSupportTicketServiceSupportTicketAdminControllerGetSupportTicketDetail =
  <
    TData = SupportTicketServiceSupportTicketAdminControllerGetSupportTicketDetailDefaultResponse,
    TError = unknown,
    TQueryKey extends unknown[] = unknown[],
  >(
    {
      id,
    }: {
      id: string;
    },
    queryKey?: TQueryKey,
    options?: Omit<
      UseQueryOptions<TData, TError>,
      'queryKey' | 'queryFn' | 'initialData'
    >,
  ) =>
    useQuery<TData, TError>({
      queryFn: () =>
        SupportTicketService.supportTicketAdminControllerGetSupportTicketDetail(
          id,
        ) as TData,
      queryKey: [
        useSupportTicketServiceSupportTicketAdminControllerGetSupportTicketDetailKey,
        ...(queryKey ?? [{ id }]),
      ],
      ...options,
    });

export type SupportTicketServicesupportTicketAdminControllerResolveTicketMutationResult =
  Awaited<
    ReturnType<
      typeof SupportTicketService.supportTicketAdminControllerResolveTicket
    >
  >;

/**
 * mark ticket as resolved
 */
export const useSupportTicketServiceSupportTicketAdminControllerResolveTicket =
  <
    TData = SupportTicketServicesupportTicketAdminControllerResolveTicketMutationResult,
    TError = unknown,
    TContext = unknown,
  >(
    options?: Omit<
      UseMutationOptions<
        TData,
        TError,
        {
          id: string;
          requestBody: ResolveTicketDto;
        },
        TContext
      >,
      'mutationFn'
    >,
  ) =>
    useMutation<
      TData,
      TError,
      {
        id: string;
        requestBody: ResolveTicketDto;
      },
      TContext
    >({
      mutationFn: ({ id, requestBody }) =>
        SupportTicketService.supportTicketAdminControllerResolveTicket(
          id,
          requestBody,
        ) as unknown as Promise<TData>,
      ...options,
    });

export type SubscriptionsServiceSubscriptionControllerGetAppSubscriptionsDefaultResponse =
  Awaited<
    ReturnType<
      typeof SubscriptionsService.subscriptionControllerGetAppSubscriptions
    >
  >;

export type SubscriptionsServiceSubscriptionControllerGetAppSubscriptionsQueryResult<
  TData = SubscriptionsServiceSubscriptionControllerGetAppSubscriptionsDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useSubscriptionsServiceSubscriptionControllerGetAppSubscriptionsKey =
  'SubscriptionsServiceSubscriptionControllerGetAppSubscriptions';

/**
 * Get list of subscriptions on app
 */
export const useSubscriptionsServiceSubscriptionControllerGetAppSubscriptions =
  <
    TData = SubscriptionsServiceSubscriptionControllerGetAppSubscriptionsDefaultResponse,
    TError = unknown,
    TQueryKey extends unknown[] = unknown[],
  >(
    {
      limit,
      offset,
    }: {
      limit?: number;
      offset: number;
    },
    queryKey?: TQueryKey,
    options?: Omit<
      UseQueryOptions<TData, TError>,
      'queryKey' | 'queryFn' | 'initialData'
    >,
  ) =>
    useQuery<TData, TError>({
      queryFn: () =>
        SubscriptionsService.subscriptionControllerGetAppSubscriptions(
          limit,
          offset,
        ) as TData,
      queryKey: [
        useSubscriptionsServiceSubscriptionControllerGetAppSubscriptionsKey,
        ...(queryKey ?? [{ limit, offset }]),
      ],
      ...options,
    });

export type SubscriptionsServiceSubscriptionControllerGetAppSubscriptionDefaultResponse =
  Awaited<
    ReturnType<
      typeof SubscriptionsService.subscriptionControllerGetAppSubscription
    >
  >;

export type SubscriptionsServiceSubscriptionControllerGetAppSubscriptionQueryResult<
  TData = SubscriptionsServiceSubscriptionControllerGetAppSubscriptionDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useSubscriptionsServiceSubscriptionControllerGetAppSubscriptionKey =
  'SubscriptionsServiceSubscriptionControllerGetAppSubscription';

/**
 * Get a subscription on app
 */
export const useSubscriptionsServiceSubscriptionControllerGetAppSubscription = <
  TData = SubscriptionsServiceSubscriptionControllerGetAppSubscriptionDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    id,
  }: {
    id: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      SubscriptionsService.subscriptionControllerGetAppSubscription(
        id,
      ) as TData,
    queryKey: [
      useSubscriptionsServiceSubscriptionControllerGetAppSubscriptionKey,
      ...(queryKey ?? [{ id }]),
    ],
    ...options,
  });

export type SubscriptionsServiceSubscriptionControllerGetCouponsOfSubscriptionDefaultResponse =
  Awaited<
    ReturnType<
      typeof SubscriptionsService.subscriptionControllerGetCouponsOfSubscription
    >
  >;

export type SubscriptionsServiceSubscriptionControllerGetCouponsOfSubscriptionQueryResult<
  TData = SubscriptionsServiceSubscriptionControllerGetCouponsOfSubscriptionDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useSubscriptionsServiceSubscriptionControllerGetCouponsOfSubscriptionKey =
  'SubscriptionsServiceSubscriptionControllerGetCouponsOfSubscription';

/**
 * Get list of coupons of subscription on app
 */
export const useSubscriptionsServiceSubscriptionControllerGetCouponsOfSubscription =
  <
    TData = SubscriptionsServiceSubscriptionControllerGetCouponsOfSubscriptionDefaultResponse,
    TError = unknown,
    TQueryKey extends unknown[] = unknown[],
  >(
    {
      id,
      limit,
      offset,
    }: {
      id: string;
      limit?: number;
      offset: number;
    },
    queryKey?: TQueryKey,
    options?: Omit<
      UseQueryOptions<TData, TError>,
      'queryKey' | 'queryFn' | 'initialData'
    >,
  ) =>
    useQuery<TData, TError>({
      queryFn: () =>
        SubscriptionsService.subscriptionControllerGetCouponsOfSubscription(
          id,
          limit,
          offset,
        ) as TData,
      queryKey: [
        useSubscriptionsServiceSubscriptionControllerGetCouponsOfSubscriptionKey,
        ...(queryKey ?? [{ id, limit, offset }]),
      ],
      ...options,
    });

export type SubscriptionsServicesubscriptionControllerSubscribeSubscriptionMutationResult =
  Awaited<
    ReturnType<
      typeof SubscriptionsService.subscriptionControllerSubscribeSubscription
    >
  >;

/**
 * User subscribe a subscription
 */
export const useSubscriptionsServiceSubscriptionControllerSubscribeSubscription =
  <
    TData = SubscriptionsServicesubscriptionControllerSubscribeSubscriptionMutationResult,
    TError = unknown,
    TContext = unknown,
  >(
    options?: Omit<
      UseMutationOptions<
        TData,
        TError,
        {
          requestBody: SubscribeSubscriptionDto;
        },
        TContext
      >,
      'mutationFn'
    >,
  ) =>
    useMutation<
      TData,
      TError,
      {
        requestBody: SubscribeSubscriptionDto;
      },
      TContext
    >({
      mutationFn: ({ requestBody }) =>
        SubscriptionsService.subscriptionControllerSubscribeSubscription(
          requestBody,
        ) as unknown as Promise<TData>,
      ...options,
    });

export type SubscriptionsServicesubscriptionControllerChangePlanSubscriptionMutationResult =
  Awaited<
    ReturnType<
      typeof SubscriptionsService.subscriptionControllerChangePlanSubscription
    >
  >;

/**
 * User change plan of a subscription
 */
export const useSubscriptionsServiceSubscriptionControllerChangePlanSubscription =
  <
    TData = SubscriptionsServicesubscriptionControllerChangePlanSubscriptionMutationResult,
    TError = unknown,
    TContext = unknown,
  >(
    options?: Omit<
      UseMutationOptions<
        TData,
        TError,
        {
          id: string;
          requestBody: SubscribeSubscriptionDto;
        },
        TContext
      >,
      'mutationFn'
    >,
  ) =>
    useMutation<
      TData,
      TError,
      {
        id: string;
        requestBody: SubscribeSubscriptionDto;
      },
      TContext
    >({
      mutationFn: ({ id, requestBody }) =>
        SubscriptionsService.subscriptionControllerChangePlanSubscription(
          id,
          requestBody,
        ) as unknown as Promise<TData>,
      ...options,
    });

export type SubscriptionsServiceSubscriptionMeControllerGetCurrentSubscriptionDefaultResponse =
  Awaited<
    ReturnType<
      typeof SubscriptionsService.subscriptionMeControllerGetCurrentSubscription
    >
  >;

export type SubscriptionsServiceSubscriptionMeControllerGetCurrentSubscriptionQueryResult<
  TData = SubscriptionsServiceSubscriptionMeControllerGetCurrentSubscriptionDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useSubscriptionsServiceSubscriptionMeControllerGetCurrentSubscriptionKey =
  'SubscriptionsServiceSubscriptionMeControllerGetCurrentSubscription';

/**
 * Get current subscription of me
 */
export const useSubscriptionsServiceSubscriptionMeControllerGetCurrentSubscription =
  <
    TData = SubscriptionsServiceSubscriptionMeControllerGetCurrentSubscriptionDefaultResponse,
    TError = unknown,
    TQueryKey extends unknown[] = unknown[],
  >(
    queryKey?: TQueryKey,
    options?: Omit<
      UseQueryOptions<TData, TError>,
      'queryKey' | 'queryFn' | 'initialData'
    >,
  ) =>
    useQuery<TData, TError>({
      queryFn: () =>
        SubscriptionsService.subscriptionMeControllerGetCurrentSubscription() as TData,
      queryKey: [
        useSubscriptionsServiceSubscriptionMeControllerGetCurrentSubscriptionKey,
        ...(queryKey ?? []),
      ],
      ...options,
    });

export type SubscriptionsServiceSubscriptionMeControllerGetCurrentSubscriptionCouponsDefaultResponse =
  Awaited<
    ReturnType<
      typeof SubscriptionsService.subscriptionMeControllerGetCurrentSubscriptionCoupons
    >
  >;

export type SubscriptionsServiceSubscriptionMeControllerGetCurrentSubscriptionCouponsQueryResult<
  TData = SubscriptionsServiceSubscriptionMeControllerGetCurrentSubscriptionCouponsDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useSubscriptionsServiceSubscriptionMeControllerGetCurrentSubscriptionCouponsKey =
  'SubscriptionsServiceSubscriptionMeControllerGetCurrentSubscriptionCoupons';

/**
 * Get list coupon of my subscription
 */
export const useSubscriptionsServiceSubscriptionMeControllerGetCurrentSubscriptionCoupons =
  <
    TData = SubscriptionsServiceSubscriptionMeControllerGetCurrentSubscriptionCouponsDefaultResponse,
    TError = unknown,
    TQueryKey extends unknown[] = unknown[],
  >(
    queryKey?: TQueryKey,
    options?: Omit<
      UseQueryOptions<TData, TError>,
      'queryKey' | 'queryFn' | 'initialData'
    >,
  ) =>
    useQuery<TData, TError>({
      queryFn: () =>
        SubscriptionsService.subscriptionMeControllerGetCurrentSubscriptionCoupons() as TData,
      queryKey: [
        useSubscriptionsServiceSubscriptionMeControllerGetCurrentSubscriptionCouponsKey,
        ...(queryKey ?? []),
      ],
      ...options,
    });

export type SubscriptionsServiceSubscriptionMeControllerGetCurrentSubscriptionPaymentMethodDefaultResponse =
  Awaited<
    ReturnType<
      typeof SubscriptionsService.subscriptionMeControllerGetCurrentSubscriptionPaymentMethod
    >
  >;

export type SubscriptionsServiceSubscriptionMeControllerGetCurrentSubscriptionPaymentMethodQueryResult<
  TData = SubscriptionsServiceSubscriptionMeControllerGetCurrentSubscriptionPaymentMethodDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useSubscriptionsServiceSubscriptionMeControllerGetCurrentSubscriptionPaymentMethodKey =
  'SubscriptionsServiceSubscriptionMeControllerGetCurrentSubscriptionPaymentMethod';

/**
 * Get card of my subscription
 */
export const useSubscriptionsServiceSubscriptionMeControllerGetCurrentSubscriptionPaymentMethod =
  <
    TData = SubscriptionsServiceSubscriptionMeControllerGetCurrentSubscriptionPaymentMethodDefaultResponse,
    TError = unknown,
    TQueryKey extends unknown[] = unknown[],
  >(
    queryKey?: TQueryKey,
    options?: Omit<
      UseQueryOptions<TData, TError>,
      'queryKey' | 'queryFn' | 'initialData'
    >,
  ) =>
    useQuery<TData, TError>({
      queryFn: () =>
        SubscriptionsService.subscriptionMeControllerGetCurrentSubscriptionPaymentMethod() as TData,
      queryKey: [
        useSubscriptionsServiceSubscriptionMeControllerGetCurrentSubscriptionPaymentMethodKey,
        ...(queryKey ?? []),
      ],
      ...options,
    });

export type SubscriptionsServicesubscriptionMeControllerUpdateSubscriptionCardMutationResult =
  Awaited<
    ReturnType<
      typeof SubscriptionsService.subscriptionMeControllerUpdateSubscriptionCard
    >
  >;

/**
 * Update card of my subscription
 */
export const useSubscriptionsServiceSubscriptionMeControllerUpdateSubscriptionCard =
  <
    TData = SubscriptionsServicesubscriptionMeControllerUpdateSubscriptionCardMutationResult,
    TError = unknown,
    TContext = unknown,
  >(
    options?: Omit<
      UseMutationOptions<
        TData,
        TError,
        {
          requestBody: UpdateSubscriptionCardDto;
        },
        TContext
      >,
      'mutationFn'
    >,
  ) =>
    useMutation<
      TData,
      TError,
      {
        requestBody: UpdateSubscriptionCardDto;
      },
      TContext
    >({
      mutationFn: ({ requestBody }) =>
        SubscriptionsService.subscriptionMeControllerUpdateSubscriptionCard(
          requestBody,
        ) as unknown as Promise<TData>,
      ...options,
    });

export type SubscriptionsServiceSubscriptionMeControllerGetSubscriptionTransactionOfMeDefaultResponse =
  Awaited<
    ReturnType<
      typeof SubscriptionsService.subscriptionMeControllerGetSubscriptionTransactionOfMe
    >
  >;

export type SubscriptionsServiceSubscriptionMeControllerGetSubscriptionTransactionOfMeQueryResult<
  TData = SubscriptionsServiceSubscriptionMeControllerGetSubscriptionTransactionOfMeDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useSubscriptionsServiceSubscriptionMeControllerGetSubscriptionTransactionOfMeKey =
  'SubscriptionsServiceSubscriptionMeControllerGetSubscriptionTransactionOfMe';

/**
 * Get list subscription transaction of me
 */
export const useSubscriptionsServiceSubscriptionMeControllerGetSubscriptionTransactionOfMe =
  <
    TData = SubscriptionsServiceSubscriptionMeControllerGetSubscriptionTransactionOfMeDefaultResponse,
    TError = unknown,
    TQueryKey extends unknown[] = unknown[],
  >(
    {
      limit,
      offset,
    }: {
      limit?: number;
      offset: number;
    },
    queryKey?: TQueryKey,
    options?: Omit<
      UseQueryOptions<TData, TError>,
      'queryKey' | 'queryFn' | 'initialData'
    >,
  ) =>
    useQuery<TData, TError>({
      queryFn: () =>
        SubscriptionsService.subscriptionMeControllerGetSubscriptionTransactionOfMe(
          limit,
          offset,
        ) as TData,
      queryKey: [
        useSubscriptionsServiceSubscriptionMeControllerGetSubscriptionTransactionOfMeKey,
        ...(queryKey ?? [{ limit, offset }]),
      ],
      ...options,
    });

export type SubscriptionsServiceSubscriptionMeControllerGetSubscriptionTransactionDefaultResponse =
  Awaited<
    ReturnType<
      typeof SubscriptionsService.subscriptionMeControllerGetSubscriptionTransaction
    >
  >;

export type SubscriptionsServiceSubscriptionMeControllerGetSubscriptionTransactionQueryResult<
  TData = SubscriptionsServiceSubscriptionMeControllerGetSubscriptionTransactionDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useSubscriptionsServiceSubscriptionMeControllerGetSubscriptionTransactionKey =
  'SubscriptionsServiceSubscriptionMeControllerGetSubscriptionTransaction';

/**
 * Get a subscription transaction
 */
export const useSubscriptionsServiceSubscriptionMeControllerGetSubscriptionTransaction =
  <
    TData = SubscriptionsServiceSubscriptionMeControllerGetSubscriptionTransactionDefaultResponse,
    TError = unknown,
    TQueryKey extends unknown[] = unknown[],
  >(
    {
      id,
    }: {
      id: string;
    },
    queryKey?: TQueryKey,
    options?: Omit<
      UseQueryOptions<TData, TError>,
      'queryKey' | 'queryFn' | 'initialData'
    >,
  ) =>
    useQuery<TData, TError>({
      queryFn: () =>
        SubscriptionsService.subscriptionMeControllerGetSubscriptionTransaction(
          id,
        ) as TData,
      queryKey: [
        useSubscriptionsServiceSubscriptionMeControllerGetSubscriptionTransactionKey,
        ...(queryKey ?? [{ id }]),
      ],
      ...options,
    });

export type SubscriptionsServicesubscriptionMeControllerCancelRenewSubscriptionMutationResult =
  Awaited<
    ReturnType<
      typeof SubscriptionsService.subscriptionMeControllerCancelRenewSubscription
    >
  >;

/**
 * Cancel renew a subscription
 */
export const useSubscriptionsServiceSubscriptionMeControllerCancelRenewSubscription =
  <
    TData = SubscriptionsServicesubscriptionMeControllerCancelRenewSubscriptionMutationResult,
    TError = unknown,
    TContext = unknown,
  >(
    options?: Omit<
      UseMutationOptions<
        TData,
        TError,
        {
          requestBody: CancelSubscriptionDto;
        },
        TContext
      >,
      'mutationFn'
    >,
  ) =>
    useMutation<
      TData,
      TError,
      {
        requestBody: CancelSubscriptionDto;
      },
      TContext
    >({
      mutationFn: ({ requestBody }) =>
        SubscriptionsService.subscriptionMeControllerCancelRenewSubscription(
          requestBody,
        ) as unknown as Promise<TData>,
      ...options,
    });

export type SubscriptionsServicesubscriptionMeControllerReSubscribeSubscriptionMutationResult =
  Awaited<
    ReturnType<
      typeof SubscriptionsService.subscriptionMeControllerReSubscribeSubscription
    >
  >;

/**
 * Resubscribe a subscription
 */
export const useSubscriptionsServiceSubscriptionMeControllerReSubscribeSubscription =
  <
    TData = SubscriptionsServicesubscriptionMeControllerReSubscribeSubscriptionMutationResult,
    TError = unknown,
    TContext = unknown,
  >(
    options?: Omit<
      UseMutationOptions<TData, TError, void, TContext>,
      'mutationFn'
    >,
  ) =>
    useMutation<TData, TError, void, TContext>({
      mutationFn: () =>
        SubscriptionsService.subscriptionMeControllerReSubscribeSubscription() as unknown as Promise<TData>,
      ...options,
    });

export type SubscriptionsServiceSubscriptionSuperAdminControllerGetSubscriptionsDefaultResponse =
  Awaited<
    ReturnType<
      typeof SubscriptionsService.subscriptionSuperAdminControllerGetSubscriptions
    >
  >;

export type SubscriptionsServiceSubscriptionSuperAdminControllerGetSubscriptionsQueryResult<
  TData = SubscriptionsServiceSubscriptionSuperAdminControllerGetSubscriptionsDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useSubscriptionsServiceSubscriptionSuperAdminControllerGetSubscriptionsKey =
  'SubscriptionsServiceSubscriptionSuperAdminControllerGetSubscriptions';

/**
 * Get list of subscriptions of system
 */
export const useSubscriptionsServiceSubscriptionSuperAdminControllerGetSubscriptions =
  <
    TData = SubscriptionsServiceSubscriptionSuperAdminControllerGetSubscriptionsDefaultResponse,
    TError = unknown,
    TQueryKey extends unknown[] = unknown[],
  >(
    {
      limit,
      offset,
      order,
    }: {
      limit?: number;
      offset: number;
      order?: string | null;
    },
    queryKey?: TQueryKey,
    options?: Omit<
      UseQueryOptions<TData, TError>,
      'queryKey' | 'queryFn' | 'initialData'
    >,
  ) =>
    useQuery<TData, TError>({
      queryFn: () =>
        SubscriptionsService.subscriptionSuperAdminControllerGetSubscriptions(
          limit,
          offset,
          order,
        ) as TData,
      queryKey: [
        useSubscriptionsServiceSubscriptionSuperAdminControllerGetSubscriptionsKey,
        ...(queryKey ?? [{ limit, offset, order }]),
      ],
      ...options,
    });

export type SubscriptionsServicesubscriptionSuperAdminControllerCreateSubscriptionMutationResult =
  Awaited<
    ReturnType<
      typeof SubscriptionsService.subscriptionSuperAdminControllerCreateSubscription
    >
  >;

/**
 * Create a subscription of system
 */
export const useSubscriptionsServiceSubscriptionSuperAdminControllerCreateSubscription =
  <
    TData = SubscriptionsServicesubscriptionSuperAdminControllerCreateSubscriptionMutationResult,
    TError = unknown,
    TContext = unknown,
  >(
    options?: Omit<
      UseMutationOptions<
        TData,
        TError,
        {
          requestBody: CreateSubscriptionDto;
        },
        TContext
      >,
      'mutationFn'
    >,
  ) =>
    useMutation<
      TData,
      TError,
      {
        requestBody: CreateSubscriptionDto;
      },
      TContext
    >({
      mutationFn: ({ requestBody }) =>
        SubscriptionsService.subscriptionSuperAdminControllerCreateSubscription(
          requestBody,
        ) as unknown as Promise<TData>,
      ...options,
    });

export type SubscriptionsServiceSubscriptionSuperAdminControllerGetSubscriptionsFilterDefaultResponse =
  Awaited<
    ReturnType<
      typeof SubscriptionsService.subscriptionSuperAdminControllerGetSubscriptionsFilter
    >
  >;

export type SubscriptionsServiceSubscriptionSuperAdminControllerGetSubscriptionsFilterQueryResult<
  TData = SubscriptionsServiceSubscriptionSuperAdminControllerGetSubscriptionsFilterDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useSubscriptionsServiceSubscriptionSuperAdminControllerGetSubscriptionsFilterKey =
  'SubscriptionsServiceSubscriptionSuperAdminControllerGetSubscriptionsFilter';

/**
 * Get list filter of subscriptions of system
 */
export const useSubscriptionsServiceSubscriptionSuperAdminControllerGetSubscriptionsFilter =
  <
    TData = SubscriptionsServiceSubscriptionSuperAdminControllerGetSubscriptionsFilterDefaultResponse,
    TError = unknown,
    TQueryKey extends unknown[] = unknown[],
  >(
    {
      limit,
      offset,
      order,
    }: {
      limit?: number;
      offset: number;
      order?: string | null;
    },
    queryKey?: TQueryKey,
    options?: Omit<
      UseQueryOptions<TData, TError>,
      'queryKey' | 'queryFn' | 'initialData'
    >,
  ) =>
    useQuery<TData, TError>({
      queryFn: () =>
        SubscriptionsService.subscriptionSuperAdminControllerGetSubscriptionsFilter(
          limit,
          offset,
          order,
        ) as TData,
      queryKey: [
        useSubscriptionsServiceSubscriptionSuperAdminControllerGetSubscriptionsFilterKey,
        ...(queryKey ?? [{ limit, offset, order }]),
      ],
      ...options,
    });

export type SubscriptionsServiceSubscriptionSuperAdminControllerGetOverallDashBoardDefaultResponse =
  Awaited<
    ReturnType<
      typeof SubscriptionsService.subscriptionSuperAdminControllerGetOverallDashBoard
    >
  >;

export type SubscriptionsServiceSubscriptionSuperAdminControllerGetOverallDashBoardQueryResult<
  TData = SubscriptionsServiceSubscriptionSuperAdminControllerGetOverallDashBoardDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useSubscriptionsServiceSubscriptionSuperAdminControllerGetOverallDashBoardKey =
  'SubscriptionsServiceSubscriptionSuperAdminControllerGetOverallDashBoard';

/**
 * Summary the overall subscription data
 */
export const useSubscriptionsServiceSubscriptionSuperAdminControllerGetOverallDashBoard =
  <
    TData = SubscriptionsServiceSubscriptionSuperAdminControllerGetOverallDashBoardDefaultResponse,
    TError = unknown,
    TQueryKey extends unknown[] = unknown[],
  >(
    {
      startTime,
      endTime,
      subscriptionId,
    }: {
      startTime: string;
      endTime: string;
      subscriptionId?: string | null;
    },
    queryKey?: TQueryKey,
    options?: Omit<
      UseQueryOptions<TData, TError>,
      'queryKey' | 'queryFn' | 'initialData'
    >,
  ) =>
    useQuery<TData, TError>({
      queryFn: () =>
        SubscriptionsService.subscriptionSuperAdminControllerGetOverallDashBoard(
          startTime,
          endTime,
          subscriptionId,
        ) as TData,
      queryKey: [
        useSubscriptionsServiceSubscriptionSuperAdminControllerGetOverallDashBoardKey,
        ...(queryKey ?? [{ endTime, startTime, subscriptionId }]),
      ],
      ...options,
    });

export type SubscriptionsServiceSubscriptionSuperAdminControllerGetSubscriptionDashboardChartDefaultResponse =
  Awaited<
    ReturnType<
      typeof SubscriptionsService.subscriptionSuperAdminControllerGetSubscriptionDashboardChart
    >
  >;

export type SubscriptionsServiceSubscriptionSuperAdminControllerGetSubscriptionDashboardChartQueryResult<
  TData = SubscriptionsServiceSubscriptionSuperAdminControllerGetSubscriptionDashboardChartDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useSubscriptionsServiceSubscriptionSuperAdminControllerGetSubscriptionDashboardChartKey =
  'SubscriptionsServiceSubscriptionSuperAdminControllerGetSubscriptionDashboardChart';

/**
 * Summary the subscription chart
 */
export const useSubscriptionsServiceSubscriptionSuperAdminControllerGetSubscriptionDashboardChart =
  <
    TData = SubscriptionsServiceSubscriptionSuperAdminControllerGetSubscriptionDashboardChartDefaultResponse,
    TError = unknown,
    TQueryKey extends unknown[] = unknown[],
  >(
    {
      startTime,
      endTime,
      timezone,
      subscriptionId,
    }: {
      startTime: string;
      endTime: string;
      timezone: string;
      subscriptionId?: string | null;
    },
    queryKey?: TQueryKey,
    options?: Omit<
      UseQueryOptions<TData, TError>,
      'queryKey' | 'queryFn' | 'initialData'
    >,
  ) =>
    useQuery<TData, TError>({
      queryFn: () =>
        SubscriptionsService.subscriptionSuperAdminControllerGetSubscriptionDashboardChart(
          startTime,
          endTime,
          timezone,
          subscriptionId,
        ) as TData,
      queryKey: [
        useSubscriptionsServiceSubscriptionSuperAdminControllerGetSubscriptionDashboardChartKey,
        ...(queryKey ?? [{ endTime, startTime, subscriptionId, timezone }]),
      ],
      ...options,
    });

export type SubscriptionsServiceSubscriptionSuperAdminControllerGetSubscriptionDefaultResponse =
  Awaited<
    ReturnType<
      typeof SubscriptionsService.subscriptionSuperAdminControllerGetSubscription
    >
  >;

export type SubscriptionsServiceSubscriptionSuperAdminControllerGetSubscriptionQueryResult<
  TData = SubscriptionsServiceSubscriptionSuperAdminControllerGetSubscriptionDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useSubscriptionsServiceSubscriptionSuperAdminControllerGetSubscriptionKey =
  'SubscriptionsServiceSubscriptionSuperAdminControllerGetSubscription';

/**
 * Get a subscription of system
 */
export const useSubscriptionsServiceSubscriptionSuperAdminControllerGetSubscription =
  <
    TData = SubscriptionsServiceSubscriptionSuperAdminControllerGetSubscriptionDefaultResponse,
    TError = unknown,
    TQueryKey extends unknown[] = unknown[],
  >(
    {
      id,
    }: {
      id: string;
    },
    queryKey?: TQueryKey,
    options?: Omit<
      UseQueryOptions<TData, TError>,
      'queryKey' | 'queryFn' | 'initialData'
    >,
  ) =>
    useQuery<TData, TError>({
      queryFn: () =>
        SubscriptionsService.subscriptionSuperAdminControllerGetSubscription(
          id,
        ) as TData,
      queryKey: [
        useSubscriptionsServiceSubscriptionSuperAdminControllerGetSubscriptionKey,
        ...(queryKey ?? [{ id }]),
      ],
      ...options,
    });

export type SubscriptionsServicesubscriptionSuperAdminControllerUpdateSubscriptionMutationResult =
  Awaited<
    ReturnType<
      typeof SubscriptionsService.subscriptionSuperAdminControllerUpdateSubscription
    >
  >;

/**
 * Update a subscription of a system
 */
export const useSubscriptionsServiceSubscriptionSuperAdminControllerUpdateSubscription =
  <
    TData = SubscriptionsServicesubscriptionSuperAdminControllerUpdateSubscriptionMutationResult,
    TError = unknown,
    TContext = unknown,
  >(
    options?: Omit<
      UseMutationOptions<
        TData,
        TError,
        {
          id: string;
          requestBody: UpdateSubscriptionDto;
        },
        TContext
      >,
      'mutationFn'
    >,
  ) =>
    useMutation<
      TData,
      TError,
      {
        id: string;
        requestBody: UpdateSubscriptionDto;
      },
      TContext
    >({
      mutationFn: ({ id, requestBody }) =>
        SubscriptionsService.subscriptionSuperAdminControllerUpdateSubscription(
          id,
          requestBody,
        ) as unknown as Promise<TData>,
      ...options,
    });

export type SubscriptionsServicesubscriptionSuperAdminControllerDeleteSubscriptionMutationResult =
  Awaited<
    ReturnType<
      typeof SubscriptionsService.subscriptionSuperAdminControllerDeleteSubscription
    >
  >;

/**
 * Delete a subscription of system
 */
export const useSubscriptionsServiceSubscriptionSuperAdminControllerDeleteSubscription =
  <
    TData = SubscriptionsServicesubscriptionSuperAdminControllerDeleteSubscriptionMutationResult,
    TError = unknown,
    TContext = unknown,
  >(
    options?: Omit<
      UseMutationOptions<
        TData,
        TError,
        {
          id: string;
        },
        TContext
      >,
      'mutationFn'
    >,
  ) =>
    useMutation<
      TData,
      TError,
      {
        id: string;
      },
      TContext
    >({
      mutationFn: ({ id }) =>
        SubscriptionsService.subscriptionSuperAdminControllerDeleteSubscription(
          id,
        ) as unknown as Promise<TData>,
      ...options,
    });

export type SubscriptionsServicesubscriptionSuperAdminControllerUpdateUserStatusMutationResult =
  Awaited<
    ReturnType<
      typeof SubscriptionsService.subscriptionSuperAdminControllerUpdateUserStatus
    >
  >;

/**
 * Update status of a subscription
 */
export const useSubscriptionsServiceSubscriptionSuperAdminControllerUpdateUserStatus =
  <
    TData = SubscriptionsServicesubscriptionSuperAdminControllerUpdateUserStatusMutationResult,
    TError = unknown,
    TContext = unknown,
  >(
    options?: Omit<
      UseMutationOptions<
        TData,
        TError,
        {
          id: string;
          requestBody: UpdateSubscriptionStatusDto;
        },
        TContext
      >,
      'mutationFn'
    >,
  ) =>
    useMutation<
      TData,
      TError,
      {
        id: string;
        requestBody: UpdateSubscriptionStatusDto;
      },
      TContext
    >({
      mutationFn: ({ id, requestBody }) =>
        SubscriptionsService.subscriptionSuperAdminControllerUpdateUserStatus(
          id,
          requestBody,
        ) as unknown as Promise<TData>,
      ...options,
    });

export type SubscriptionsServiceSubscriptionSuperAdminControllerGetCouponStatisticsOfSubscriptionDefaultResponse =
  Awaited<
    ReturnType<
      typeof SubscriptionsService.subscriptionSuperAdminControllerGetCouponStatisticsOfSubscription
    >
  >;

export type SubscriptionsServiceSubscriptionSuperAdminControllerGetCouponStatisticsOfSubscriptionQueryResult<
  TData = SubscriptionsServiceSubscriptionSuperAdminControllerGetCouponStatisticsOfSubscriptionDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useSubscriptionsServiceSubscriptionSuperAdminControllerGetCouponStatisticsOfSubscriptionKey =
  'SubscriptionsServiceSubscriptionSuperAdminControllerGetCouponStatisticsOfSubscription';

/**
 * Get list of coupon statistics of subscription
 */
export const useSubscriptionsServiceSubscriptionSuperAdminControllerGetCouponStatisticsOfSubscription =
  <
    TData = SubscriptionsServiceSubscriptionSuperAdminControllerGetCouponStatisticsOfSubscriptionDefaultResponse,
    TError = unknown,
    TQueryKey extends unknown[] = unknown[],
  >(
    {
      id,
      q,
      couponId,
      fromDate,
      toDate,
    }: {
      id: string;
      q?: string;
      couponId?: string;
      fromDate?: string;
      toDate?: string;
    },
    queryKey?: TQueryKey,
    options?: Omit<
      UseQueryOptions<TData, TError>,
      'queryKey' | 'queryFn' | 'initialData'
    >,
  ) =>
    useQuery<TData, TError>({
      queryFn: () =>
        SubscriptionsService.subscriptionSuperAdminControllerGetCouponStatisticsOfSubscription(
          id,
          q,
          couponId,
          fromDate,
          toDate,
        ) as TData,
      queryKey: [
        useSubscriptionsServiceSubscriptionSuperAdminControllerGetCouponStatisticsOfSubscriptionKey,
        ...(queryKey ?? [{ couponId, fromDate, id, q, toDate }]),
      ],
      ...options,
    });

export type SubscriptionsServiceSubscriptionSuperAdminControllerGetCouponLogsOfSubscriptionDefaultResponse =
  Awaited<
    ReturnType<
      typeof SubscriptionsService.subscriptionSuperAdminControllerGetCouponLogsOfSubscription
    >
  >;

export type SubscriptionsServiceSubscriptionSuperAdminControllerGetCouponLogsOfSubscriptionQueryResult<
  TData = SubscriptionsServiceSubscriptionSuperAdminControllerGetCouponLogsOfSubscriptionDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useSubscriptionsServiceSubscriptionSuperAdminControllerGetCouponLogsOfSubscriptionKey =
  'SubscriptionsServiceSubscriptionSuperAdminControllerGetCouponLogsOfSubscription';

/**
 * Get list of coupon logs of subscription
 */
export const useSubscriptionsServiceSubscriptionSuperAdminControllerGetCouponLogsOfSubscription =
  <
    TData = SubscriptionsServiceSubscriptionSuperAdminControllerGetCouponLogsOfSubscriptionDefaultResponse,
    TError = unknown,
    TQueryKey extends unknown[] = unknown[],
  >(
    {
      id,
      q,
      couponId,
      fromDate,
      toDate,
      limit,
      offset,
      order,
    }: {
      id: string;
      q?: string;
      couponId?: string;
      fromDate?: string;
      toDate?: string;
      limit?: number;
      offset: number;
      order?: string | null;
    },
    queryKey?: TQueryKey,
    options?: Omit<
      UseQueryOptions<TData, TError>,
      'queryKey' | 'queryFn' | 'initialData'
    >,
  ) =>
    useQuery<TData, TError>({
      queryFn: () =>
        SubscriptionsService.subscriptionSuperAdminControllerGetCouponLogsOfSubscription(
          id,
          q,
          couponId,
          fromDate,
          toDate,
          limit,
          offset,
          order,
        ) as TData,
      queryKey: [
        useSubscriptionsServiceSubscriptionSuperAdminControllerGetCouponLogsOfSubscriptionKey,
        ...(queryKey ?? [
          { couponId, fromDate, id, limit, offset, order, q, toDate },
        ]),
      ],
      ...options,
    });

export type SubscriptionsServiceSubscriptionSuperAdminControllerGetCouponsOfSubscriptionDefaultResponse =
  Awaited<
    ReturnType<
      typeof SubscriptionsService.subscriptionSuperAdminControllerGetCouponsOfSubscription
    >
  >;

export type SubscriptionsServiceSubscriptionSuperAdminControllerGetCouponsOfSubscriptionQueryResult<
  TData = SubscriptionsServiceSubscriptionSuperAdminControllerGetCouponsOfSubscriptionDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useSubscriptionsServiceSubscriptionSuperAdminControllerGetCouponsOfSubscriptionKey =
  'SubscriptionsServiceSubscriptionSuperAdminControllerGetCouponsOfSubscription';

/**
 * Get list of coupons of subscription
 */
export const useSubscriptionsServiceSubscriptionSuperAdminControllerGetCouponsOfSubscription =
  <
    TData = SubscriptionsServiceSubscriptionSuperAdminControllerGetCouponsOfSubscriptionDefaultResponse,
    TError = unknown,
    TQueryKey extends unknown[] = unknown[],
  >(
    {
      id,
      limit,
      offset,
    }: {
      id: string;
      limit?: number;
      offset: number;
    },
    queryKey?: TQueryKey,
    options?: Omit<
      UseQueryOptions<TData, TError>,
      'queryKey' | 'queryFn' | 'initialData'
    >,
  ) =>
    useQuery<TData, TError>({
      queryFn: () =>
        SubscriptionsService.subscriptionSuperAdminControllerGetCouponsOfSubscription(
          id,
          limit,
          offset,
        ) as TData,
      queryKey: [
        useSubscriptionsServiceSubscriptionSuperAdminControllerGetCouponsOfSubscriptionKey,
        ...(queryKey ?? [{ id, limit, offset }]),
      ],
      ...options,
    });

export type SubscriptionsServicesubscriptionSuperAdminControllerCreateCouponOfSubscriptionMutationResult =
  Awaited<
    ReturnType<
      typeof SubscriptionsService.subscriptionSuperAdminControllerCreateCouponOfSubscription
    >
  >;

/**
 * Create a coupon of subscription
 */
export const useSubscriptionsServiceSubscriptionSuperAdminControllerCreateCouponOfSubscription =
  <
    TData = SubscriptionsServicesubscriptionSuperAdminControllerCreateCouponOfSubscriptionMutationResult,
    TError = unknown,
    TContext = unknown,
  >(
    options?: Omit<
      UseMutationOptions<
        TData,
        TError,
        {
          id: string;
          requestBody: CreateSubscriptionCouponDto;
        },
        TContext
      >,
      'mutationFn'
    >,
  ) =>
    useMutation<
      TData,
      TError,
      {
        id: string;
        requestBody: CreateSubscriptionCouponDto;
      },
      TContext
    >({
      mutationFn: ({ id, requestBody }) =>
        SubscriptionsService.subscriptionSuperAdminControllerCreateCouponOfSubscription(
          id,
          requestBody,
        ) as unknown as Promise<TData>,
      ...options,
    });

export type SubscriptionsServiceSubscriptionSuperAdminControllerGetCouponOfSubscriptionDefaultResponse =
  Awaited<
    ReturnType<
      typeof SubscriptionsService.subscriptionSuperAdminControllerGetCouponOfSubscription
    >
  >;

export type SubscriptionsServiceSubscriptionSuperAdminControllerGetCouponOfSubscriptionQueryResult<
  TData = SubscriptionsServiceSubscriptionSuperAdminControllerGetCouponOfSubscriptionDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useSubscriptionsServiceSubscriptionSuperAdminControllerGetCouponOfSubscriptionKey =
  'SubscriptionsServiceSubscriptionSuperAdminControllerGetCouponOfSubscription';

/**
 * Get a coupon of subscription
 */
export const useSubscriptionsServiceSubscriptionSuperAdminControllerGetCouponOfSubscription =
  <
    TData = SubscriptionsServiceSubscriptionSuperAdminControllerGetCouponOfSubscriptionDefaultResponse,
    TError = unknown,
    TQueryKey extends unknown[] = unknown[],
  >(
    {
      id,
      couponId,
    }: {
      id: string;
      couponId: string;
    },
    queryKey?: TQueryKey,
    options?: Omit<
      UseQueryOptions<TData, TError>,
      'queryKey' | 'queryFn' | 'initialData'
    >,
  ) =>
    useQuery<TData, TError>({
      queryFn: () =>
        SubscriptionsService.subscriptionSuperAdminControllerGetCouponOfSubscription(
          id,
          couponId,
        ) as TData,
      queryKey: [
        useSubscriptionsServiceSubscriptionSuperAdminControllerGetCouponOfSubscriptionKey,
        ...(queryKey ?? [{ couponId, id }]),
      ],
      ...options,
    });

export type SubscriptionsServicesubscriptionSuperAdminControllerUpdateCouponOfSubscriptionMutationResult =
  Awaited<
    ReturnType<
      typeof SubscriptionsService.subscriptionSuperAdminControllerUpdateCouponOfSubscription
    >
  >;

/**
 * Update a coupon of a subscription
 */
export const useSubscriptionsServiceSubscriptionSuperAdminControllerUpdateCouponOfSubscription =
  <
    TData = SubscriptionsServicesubscriptionSuperAdminControllerUpdateCouponOfSubscriptionMutationResult,
    TError = unknown,
    TContext = unknown,
  >(
    options?: Omit<
      UseMutationOptions<
        TData,
        TError,
        {
          id: string;
          couponId: string;
          requestBody: UpdateSubscriptionCouponDto;
        },
        TContext
      >,
      'mutationFn'
    >,
  ) =>
    useMutation<
      TData,
      TError,
      {
        id: string;
        couponId: string;
        requestBody: UpdateSubscriptionCouponDto;
      },
      TContext
    >({
      mutationFn: ({ id, couponId, requestBody }) =>
        SubscriptionsService.subscriptionSuperAdminControllerUpdateCouponOfSubscription(
          id,
          couponId,
          requestBody,
        ) as unknown as Promise<TData>,
      ...options,
    });

export type SubscriptionsServicesubscriptionSuperAdminControllerDeleteCouponOfSubscriptionMutationResult =
  Awaited<
    ReturnType<
      typeof SubscriptionsService.subscriptionSuperAdminControllerDeleteCouponOfSubscription
    >
  >;

/**
 * Delete a coupon of subscription
 */
export const useSubscriptionsServiceSubscriptionSuperAdminControllerDeleteCouponOfSubscription =
  <
    TData = SubscriptionsServicesubscriptionSuperAdminControllerDeleteCouponOfSubscriptionMutationResult,
    TError = unknown,
    TContext = unknown,
  >(
    options?: Omit<
      UseMutationOptions<
        TData,
        TError,
        {
          id: string;
          couponId: string;
        },
        TContext
      >,
      'mutationFn'
    >,
  ) =>
    useMutation<
      TData,
      TError,
      {
        id: string;
        couponId: string;
      },
      TContext
    >({
      mutationFn: ({ id, couponId }) =>
        SubscriptionsService.subscriptionSuperAdminControllerDeleteCouponOfSubscription(
          id,
          couponId,
        ) as unknown as Promise<TData>,
      ...options,
    });

export type SubscriptionsServicesubscriptionSuperAdminControllerUpdatePositionsOfCouponOfSubscriptionMutationResult =
  Awaited<
    ReturnType<
      typeof SubscriptionsService.subscriptionSuperAdminControllerUpdatePositionsOfCouponOfSubscription
    >
  >;

/**
 * Update a coupon of a subscription
 */
export const useSubscriptionsServiceSubscriptionSuperAdminControllerUpdatePositionsOfCouponOfSubscription =
  <
    TData = SubscriptionsServicesubscriptionSuperAdminControllerUpdatePositionsOfCouponOfSubscriptionMutationResult,
    TError = unknown,
    TContext = unknown,
  >(
    options?: Omit<
      UseMutationOptions<
        TData,
        TError,
        {
          id: string;
          couponId: string;
          requestBody: UpdatePositionsCouponDto;
        },
        TContext
      >,
      'mutationFn'
    >,
  ) =>
    useMutation<
      TData,
      TError,
      {
        id: string;
        couponId: string;
        requestBody: UpdatePositionsCouponDto;
      },
      TContext
    >({
      mutationFn: ({ id, couponId, requestBody }) =>
        SubscriptionsService.subscriptionSuperAdminControllerUpdatePositionsOfCouponOfSubscription(
          id,
          couponId,
          requestBody,
        ) as unknown as Promise<TData>,
      ...options,
    });

export type SubscriptionsServicesubscriptionSuperAdminControllerCreateDiscountOfSubscriptionPlanMutationResult =
  Awaited<
    ReturnType<
      typeof SubscriptionsService.subscriptionSuperAdminControllerCreateDiscountOfSubscriptionPlan
    >
  >;

/**
 * Create a discount for plan of subscription
 */
export const useSubscriptionsServiceSubscriptionSuperAdminControllerCreateDiscountOfSubscriptionPlan =
  <
    TData = SubscriptionsServicesubscriptionSuperAdminControllerCreateDiscountOfSubscriptionPlanMutationResult,
    TError = unknown,
    TContext = unknown,
  >(
    options?: Omit<
      UseMutationOptions<
        TData,
        TError,
        {
          id: string;
          requestBody: CreateDiscountDto;
        },
        TContext
      >,
      'mutationFn'
    >,
  ) =>
    useMutation<
      TData,
      TError,
      {
        id: string;
        requestBody: CreateDiscountDto;
      },
      TContext
    >({
      mutationFn: ({ id, requestBody }) =>
        SubscriptionsService.subscriptionSuperAdminControllerCreateDiscountOfSubscriptionPlan(
          id,
          requestBody,
        ) as unknown as Promise<TData>,
      ...options,
    });

export type SubscriptionsServiceSubscriptionSuperAdminControllerGetDiscountsOfSubscriptionDefaultResponse =
  Awaited<
    ReturnType<
      typeof SubscriptionsService.subscriptionSuperAdminControllerGetDiscountsOfSubscription
    >
  >;

export type SubscriptionsServiceSubscriptionSuperAdminControllerGetDiscountsOfSubscriptionQueryResult<
  TData = SubscriptionsServiceSubscriptionSuperAdminControllerGetDiscountsOfSubscriptionDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useSubscriptionsServiceSubscriptionSuperAdminControllerGetDiscountsOfSubscriptionKey =
  'SubscriptionsServiceSubscriptionSuperAdminControllerGetDiscountsOfSubscription';

/**
 * Get discounts for plans of subscription
 */
export const useSubscriptionsServiceSubscriptionSuperAdminControllerGetDiscountsOfSubscription =
  <
    TData = SubscriptionsServiceSubscriptionSuperAdminControllerGetDiscountsOfSubscriptionDefaultResponse,
    TError = unknown,
    TQueryKey extends unknown[] = unknown[],
  >(
    {
      id,
      limit,
      offset,
      order,
    }: {
      id: string;
      limit?: number;
      offset: number;
      order?: string | null;
    },
    queryKey?: TQueryKey,
    options?: Omit<
      UseQueryOptions<TData, TError>,
      'queryKey' | 'queryFn' | 'initialData'
    >,
  ) =>
    useQuery<TData, TError>({
      queryFn: () =>
        SubscriptionsService.subscriptionSuperAdminControllerGetDiscountsOfSubscription(
          id,
          limit,
          offset,
          order,
        ) as TData,
      queryKey: [
        useSubscriptionsServiceSubscriptionSuperAdminControllerGetDiscountsOfSubscriptionKey,
        ...(queryKey ?? [{ id, limit, offset, order }]),
      ],
      ...options,
    });

export type SubscriptionsServiceSubscriptionSuperAdminControllerGetSubscriptionStatisticsDefaultResponse =
  Awaited<
    ReturnType<
      typeof SubscriptionsService.subscriptionSuperAdminControllerGetSubscriptionStatistics
    >
  >;

export type SubscriptionsServiceSubscriptionSuperAdminControllerGetSubscriptionStatisticsQueryResult<
  TData = SubscriptionsServiceSubscriptionSuperAdminControllerGetSubscriptionStatisticsDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useSubscriptionsServiceSubscriptionSuperAdminControllerGetSubscriptionStatisticsKey =
  'SubscriptionsServiceSubscriptionSuperAdminControllerGetSubscriptionStatistics';

/**
 * Get statistics of a subscription
 */
export const useSubscriptionsServiceSubscriptionSuperAdminControllerGetSubscriptionStatistics =
  <
    TData = SubscriptionsServiceSubscriptionSuperAdminControllerGetSubscriptionStatisticsDefaultResponse,
    TError = unknown,
    TQueryKey extends unknown[] = unknown[],
  >(
    {
      id,
      startTime,
      endTime,
      subscriptionId,
    }: {
      id: string;
      startTime: string;
      endTime: string;
      subscriptionId?: string | null;
    },
    queryKey?: TQueryKey,
    options?: Omit<
      UseQueryOptions<TData, TError>,
      'queryKey' | 'queryFn' | 'initialData'
    >,
  ) =>
    useQuery<TData, TError>({
      queryFn: () =>
        SubscriptionsService.subscriptionSuperAdminControllerGetSubscriptionStatistics(
          id,
          startTime,
          endTime,
          subscriptionId,
        ) as TData,
      queryKey: [
        useSubscriptionsServiceSubscriptionSuperAdminControllerGetSubscriptionStatisticsKey,
        ...(queryKey ?? [{ endTime, id, startTime, subscriptionId }]),
      ],
      ...options,
    });

export type SubscriptionsServiceSubscriptionSuperAdminControllerGetTransactionsOfSubscriptionDefaultResponse =
  Awaited<
    ReturnType<
      typeof SubscriptionsService.subscriptionSuperAdminControllerGetTransactionsOfSubscription
    >
  >;

export type SubscriptionsServiceSubscriptionSuperAdminControllerGetTransactionsOfSubscriptionQueryResult<
  TData = SubscriptionsServiceSubscriptionSuperAdminControllerGetTransactionsOfSubscriptionDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useSubscriptionsServiceSubscriptionSuperAdminControllerGetTransactionsOfSubscriptionKey =
  'SubscriptionsServiceSubscriptionSuperAdminControllerGetTransactionsOfSubscription';

/**
 * Get transactions of a subscription
 */
export const useSubscriptionsServiceSubscriptionSuperAdminControllerGetTransactionsOfSubscription =
  <
    TData = SubscriptionsServiceSubscriptionSuperAdminControllerGetTransactionsOfSubscriptionDefaultResponse,
    TError = unknown,
    TQueryKey extends unknown[] = unknown[],
  >(
    {
      id,
      q,
      action,
      status,
      recurringType,
      fromDate,
      toDate,
      limit,
      offset,
      order,
    }: {
      id: string;
      q?: string;
      action?: 'RENEW' | 'NEW' | 'CANCEL';
      status?: 'PURCHASED' | 'DECLINED';
      recurringType?: 'MONTHLY_1' | 'YEARLY_1';
      fromDate?: string;
      toDate?: string;
      limit?: number;
      offset: number;
      order?: string | null;
    },
    queryKey?: TQueryKey,
    options?: Omit<
      UseQueryOptions<TData, TError>,
      'queryKey' | 'queryFn' | 'initialData'
    >,
  ) =>
    useQuery<TData, TError>({
      queryFn: () =>
        SubscriptionsService.subscriptionSuperAdminControllerGetTransactionsOfSubscription(
          id,
          q,
          action,
          status,
          recurringType,
          fromDate,
          toDate,
          limit,
          offset,
          order,
        ) as TData,
      queryKey: [
        useSubscriptionsServiceSubscriptionSuperAdminControllerGetTransactionsOfSubscriptionKey,
        ...(queryKey ?? [
          {
            action,
            fromDate,
            id,
            limit,
            offset,
            order,
            q,
            recurringType,
            status,
            toDate,
          },
        ]),
      ],
      ...options,
    });

export type SubscriptionsServiceSubscriptionSuperAdminControllerGetCouponLogActivitiesDefaultResponse =
  Awaited<
    ReturnType<
      typeof SubscriptionsService.subscriptionSuperAdminControllerGetCouponLogActivities
    >
  >;

export type SubscriptionsServiceSubscriptionSuperAdminControllerGetCouponLogActivitiesQueryResult<
  TData = SubscriptionsServiceSubscriptionSuperAdminControllerGetCouponLogActivitiesDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useSubscriptionsServiceSubscriptionSuperAdminControllerGetCouponLogActivitiesKey =
  'SubscriptionsServiceSubscriptionSuperAdminControllerGetCouponLogActivities';

/**
 * Get list of coupon log activities of subscription
 */
export const useSubscriptionsServiceSubscriptionSuperAdminControllerGetCouponLogActivities =
  <
    TData = SubscriptionsServiceSubscriptionSuperAdminControllerGetCouponLogActivitiesDefaultResponse,
    TError = unknown,
    TQueryKey extends unknown[] = unknown[],
  >(
    {
      id,
      limit,
      offset,
      order,
    }: {
      id: string;
      limit?: number;
      offset: number;
      order?: string | null;
    },
    queryKey?: TQueryKey,
    options?: Omit<
      UseQueryOptions<TData, TError>,
      'queryKey' | 'queryFn' | 'initialData'
    >,
  ) =>
    useQuery<TData, TError>({
      queryFn: () =>
        SubscriptionsService.subscriptionSuperAdminControllerGetCouponLogActivities(
          id,
          limit,
          offset,
          order,
        ) as TData,
      queryKey: [
        useSubscriptionsServiceSubscriptionSuperAdminControllerGetCouponLogActivitiesKey,
        ...(queryKey ?? [{ id, limit, offset, order }]),
      ],
      ...options,
    });

export type SubscriptionsServiceUserSubscriptionControllerGetSubscriptionTransactionsDefaultResponse =
  Awaited<
    ReturnType<
      typeof SubscriptionsService.userSubscriptionControllerGetSubscriptionTransactions
    >
  >;

export type SubscriptionsServiceUserSubscriptionControllerGetSubscriptionTransactionsQueryResult<
  TData = SubscriptionsServiceUserSubscriptionControllerGetSubscriptionTransactionsDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useSubscriptionsServiceUserSubscriptionControllerGetSubscriptionTransactionsKey =
  'SubscriptionsServiceUserSubscriptionControllerGetSubscriptionTransactions';

/**
 * Get list subscription transaction of user
 */
export const useSubscriptionsServiceUserSubscriptionControllerGetSubscriptionTransactions =
  <
    TData = SubscriptionsServiceUserSubscriptionControllerGetSubscriptionTransactionsDefaultResponse,
    TError = unknown,
    TQueryKey extends unknown[] = unknown[],
  >(
    {
      id,
      stripePaymentIntentId,
      subscriptionId,
      fromDate,
      toDate,
      limit,
      offset,
      order,
    }: {
      id: string;
      stripePaymentIntentId?: string | null;
      subscriptionId?: string | null;
      fromDate?: string | null;
      toDate?: string | null;
      limit?: number;
      offset: number;
      order?: string | null;
    },
    queryKey?: TQueryKey,
    options?: Omit<
      UseQueryOptions<TData, TError>,
      'queryKey' | 'queryFn' | 'initialData'
    >,
  ) =>
    useQuery<TData, TError>({
      queryFn: () =>
        SubscriptionsService.userSubscriptionControllerGetSubscriptionTransactions(
          id,
          stripePaymentIntentId,
          subscriptionId,
          fromDate,
          toDate,
          limit,
          offset,
          order,
        ) as TData,
      queryKey: [
        useSubscriptionsServiceUserSubscriptionControllerGetSubscriptionTransactionsKey,
        ...(queryKey ?? [
          {
            fromDate,
            id,
            limit,
            offset,
            order,
            stripePaymentIntentId,
            subscriptionId,
            toDate,
          },
        ]),
      ],
      ...options,
    });

export type SubscriptionsServicesubscriptionSuperAdminControllerV2CreateSubscriptionV2MutationResult =
  Awaited<
    ReturnType<
      typeof SubscriptionsService.subscriptionSuperAdminControllerV2CreateSubscriptionV2
    >
  >;

/**
 * Create a subscription of system
 */
export const useSubscriptionsServiceSubscriptionSuperAdminControllerV2CreateSubscriptionV2 =
  <
    TData = SubscriptionsServicesubscriptionSuperAdminControllerV2CreateSubscriptionV2MutationResult,
    TError = unknown,
    TContext = unknown,
  >(
    options?: Omit<
      UseMutationOptions<
        TData,
        TError,
        {
          requestBody: CreateSubscriptionV2Dto;
        },
        TContext
      >,
      'mutationFn'
    >,
  ) =>
    useMutation<
      TData,
      TError,
      {
        requestBody: CreateSubscriptionV2Dto;
      },
      TContext
    >({
      mutationFn: ({ requestBody }) =>
        SubscriptionsService.subscriptionSuperAdminControllerV2CreateSubscriptionV2(
          requestBody,
        ) as unknown as Promise<TData>,
      ...options,
    });

export type SubscriptionsServicesubscriptionSuperAdminControllerV2UpdateSubscriptionV2MutationResult =
  Awaited<
    ReturnType<
      typeof SubscriptionsService.subscriptionSuperAdminControllerV2UpdateSubscriptionV2
    >
  >;

/**
 * Update a subscription of a system
 */
export const useSubscriptionsServiceSubscriptionSuperAdminControllerV2UpdateSubscriptionV2 =
  <
    TData = SubscriptionsServicesubscriptionSuperAdminControllerV2UpdateSubscriptionV2MutationResult,
    TError = unknown,
    TContext = unknown,
  >(
    options?: Omit<
      UseMutationOptions<
        TData,
        TError,
        {
          id: string;
          requestBody: UpdateSubscriptionV2Dto;
        },
        TContext
      >,
      'mutationFn'
    >,
  ) =>
    useMutation<
      TData,
      TError,
      {
        id: string;
        requestBody: UpdateSubscriptionV2Dto;
      },
      TContext
    >({
      mutationFn: ({ id, requestBody }) =>
        SubscriptionsService.subscriptionSuperAdminControllerV2UpdateSubscriptionV2(
          id,
          requestBody,
        ) as unknown as Promise<TData>,
      ...options,
    });

export type SubscriptionsServiceSubscriptionMeV2ControllerGetCurrentSubscriptionDefaultResponse =
  Awaited<
    ReturnType<
      typeof SubscriptionsService.subscriptionMeV2ControllerGetCurrentSubscription
    >
  >;

export type SubscriptionsServiceSubscriptionMeV2ControllerGetCurrentSubscriptionQueryResult<
  TData = SubscriptionsServiceSubscriptionMeV2ControllerGetCurrentSubscriptionDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useSubscriptionsServiceSubscriptionMeV2ControllerGetCurrentSubscriptionKey =
  'SubscriptionsServiceSubscriptionMeV2ControllerGetCurrentSubscription';

/**
 * Get current subscription of me
 */
export const useSubscriptionsServiceSubscriptionMeV2ControllerGetCurrentSubscription =
  <
    TData = SubscriptionsServiceSubscriptionMeV2ControllerGetCurrentSubscriptionDefaultResponse,
    TError = unknown,
    TQueryKey extends unknown[] = unknown[],
  >(
    {
      limit,
      offset,
      order,
    }: {
      limit?: number;
      offset: number;
      order?: string | null;
    },
    queryKey?: TQueryKey,
    options?: Omit<
      UseQueryOptions<TData, TError>,
      'queryKey' | 'queryFn' | 'initialData'
    >,
  ) =>
    useQuery<TData, TError>({
      queryFn: () =>
        SubscriptionsService.subscriptionMeV2ControllerGetCurrentSubscription(
          limit,
          offset,
          order,
        ) as TData,
      queryKey: [
        useSubscriptionsServiceSubscriptionMeV2ControllerGetCurrentSubscriptionKey,
        ...(queryKey ?? [{ limit, offset, order }]),
      ],
      ...options,
    });

export type SubscriptionsServiceSubscriptionMeV2ControllerGetCurrentSubscriptionPaymentMethodDefaultResponse =
  Awaited<
    ReturnType<
      typeof SubscriptionsService.subscriptionMeV2ControllerGetCurrentSubscriptionPaymentMethod
    >
  >;

export type SubscriptionsServiceSubscriptionMeV2ControllerGetCurrentSubscriptionPaymentMethodQueryResult<
  TData = SubscriptionsServiceSubscriptionMeV2ControllerGetCurrentSubscriptionPaymentMethodDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useSubscriptionsServiceSubscriptionMeV2ControllerGetCurrentSubscriptionPaymentMethodKey =
  'SubscriptionsServiceSubscriptionMeV2ControllerGetCurrentSubscriptionPaymentMethod';

/**
 * Get card of my subscription
 */
export const useSubscriptionsServiceSubscriptionMeV2ControllerGetCurrentSubscriptionPaymentMethod =
  <
    TData = SubscriptionsServiceSubscriptionMeV2ControllerGetCurrentSubscriptionPaymentMethodDefaultResponse,
    TError = unknown,
    TQueryKey extends unknown[] = unknown[],
  >(
    {
      id,
    }: {
      id: string;
    },
    queryKey?: TQueryKey,
    options?: Omit<
      UseQueryOptions<TData, TError>,
      'queryKey' | 'queryFn' | 'initialData'
    >,
  ) =>
    useQuery<TData, TError>({
      queryFn: () =>
        SubscriptionsService.subscriptionMeV2ControllerGetCurrentSubscriptionPaymentMethod(
          id,
        ) as TData,
      queryKey: [
        useSubscriptionsServiceSubscriptionMeV2ControllerGetCurrentSubscriptionPaymentMethodKey,
        ...(queryKey ?? [{ id }]),
      ],
      ...options,
    });

export type SubscriptionsServicesubscriptionMeV2ControllerUpdateSubscriptionCardMutationResult =
  Awaited<
    ReturnType<
      typeof SubscriptionsService.subscriptionMeV2ControllerUpdateSubscriptionCard
    >
  >;

/**
 * Update card of my subscription
 */
export const useSubscriptionsServiceSubscriptionMeV2ControllerUpdateSubscriptionCard =
  <
    TData = SubscriptionsServicesubscriptionMeV2ControllerUpdateSubscriptionCardMutationResult,
    TError = unknown,
    TContext = unknown,
  >(
    options?: Omit<
      UseMutationOptions<
        TData,
        TError,
        {
          id: string;
          requestBody: UpdateSubscriptionCardDto;
        },
        TContext
      >,
      'mutationFn'
    >,
  ) =>
    useMutation<
      TData,
      TError,
      {
        id: string;
        requestBody: UpdateSubscriptionCardDto;
      },
      TContext
    >({
      mutationFn: ({ id, requestBody }) =>
        SubscriptionsService.subscriptionMeV2ControllerUpdateSubscriptionCard(
          id,
          requestBody,
        ) as unknown as Promise<TData>,
      ...options,
    });

export type SubscriptionsServicesubscriptionMeV2ControllerCancelRenewSubscriptionMutationResult =
  Awaited<
    ReturnType<
      typeof SubscriptionsService.subscriptionMeV2ControllerCancelRenewSubscription
    >
  >;

/**
 * Cancel renew a subscription
 */
export const useSubscriptionsServiceSubscriptionMeV2ControllerCancelRenewSubscription =
  <
    TData = SubscriptionsServicesubscriptionMeV2ControllerCancelRenewSubscriptionMutationResult,
    TError = unknown,
    TContext = unknown,
  >(
    options?: Omit<
      UseMutationOptions<
        TData,
        TError,
        {
          id: string;
          requestBody: CancelSubscriptionDto;
        },
        TContext
      >,
      'mutationFn'
    >,
  ) =>
    useMutation<
      TData,
      TError,
      {
        id: string;
        requestBody: CancelSubscriptionDto;
      },
      TContext
    >({
      mutationFn: ({ id, requestBody }) =>
        SubscriptionsService.subscriptionMeV2ControllerCancelRenewSubscription(
          id,
          requestBody,
        ) as unknown as Promise<TData>,
      ...options,
    });

export type SubscriptionsServicesubscriptionMeV2ControllerReSubscribeSubscriptionMutationResult =
  Awaited<
    ReturnType<
      typeof SubscriptionsService.subscriptionMeV2ControllerReSubscribeSubscription
    >
  >;

/**
 * Resubscribe a subscription
 */
export const useSubscriptionsServiceSubscriptionMeV2ControllerReSubscribeSubscription =
  <
    TData = SubscriptionsServicesubscriptionMeV2ControllerReSubscribeSubscriptionMutationResult,
    TError = unknown,
    TContext = unknown,
  >(
    options?: Omit<
      UseMutationOptions<
        TData,
        TError,
        {
          id: string;
        },
        TContext
      >,
      'mutationFn'
    >,
  ) =>
    useMutation<
      TData,
      TError,
      {
        id: string;
      },
      TContext
    >({
      mutationFn: ({ id }) =>
        SubscriptionsService.subscriptionMeV2ControllerReSubscribeSubscription(
          id,
        ) as unknown as Promise<TData>,
      ...options,
    });

export type StoragesServicestorageControllerGenerateS3PresignedUrlMutationResult =
  Awaited<
    ReturnType<typeof StoragesService.storageControllerGenerateS3PresignedUrl>
  >;

/**
 * Create a Presigned URL to upload file
 */
export const useStoragesServiceStorageControllerGenerateS3PresignedUrl = <
  TData = StoragesServicestorageControllerGenerateS3PresignedUrlMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: CreateUploadPresignedUrlDto;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: CreateUploadPresignedUrlDto;
    },
    TContext
  >({
    mutationFn: ({ requestBody }) =>
      StoragesService.storageControllerGenerateS3PresignedUrl(
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type StoragesServicestorageControllerGenerateS3PresignedUrlForExcelMutationResult =
  Awaited<
    ReturnType<
      typeof StoragesService.storageControllerGenerateS3PresignedUrlForExcel
    >
  >;

/**
 * Create a Presigned URL to upload Excel file
 */
export const useStoragesServiceStorageControllerGenerateS3PresignedUrlForExcel =
  <
    TData = StoragesServicestorageControllerGenerateS3PresignedUrlForExcelMutationResult,
    TError = unknown,
    TContext = unknown,
  >(
    options?: Omit<
      UseMutationOptions<
        TData,
        TError,
        {
          requestBody: CreateUploadExcelPresignedUrlDto;
        },
        TContext
      >,
      'mutationFn'
    >,
  ) =>
    useMutation<
      TData,
      TError,
      {
        requestBody: CreateUploadExcelPresignedUrlDto;
      },
      TContext
    >({
      mutationFn: ({ requestBody }) =>
        StoragesService.storageControllerGenerateS3PresignedUrlForExcel(
          requestBody,
        ) as unknown as Promise<TData>,
      ...options,
    });

export type RolesServiceRoleControllerGetRolesOfBusinessDefaultResponse =
  Awaited<ReturnType<typeof RolesService.roleControllerGetRolesOfBusiness>>;

export type RolesServiceRoleControllerGetRolesOfBusinessQueryResult<
  TData = RolesServiceRoleControllerGetRolesOfBusinessDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useRolesServiceRoleControllerGetRolesOfBusinessKey =
  'RolesServiceRoleControllerGetRolesOfBusiness';

/**
 * Get list of roles of a business
 */
export const useRolesServiceRoleControllerGetRolesOfBusiness = <
  TData = RolesServiceRoleControllerGetRolesOfBusinessDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    businessId,
    limit,
    offset,
    q,
    order,
  }: {
    businessId: string;
    limit?: number;
    offset: number;
    q?: string | null;
    order?: string | null;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      RolesService.roleControllerGetRolesOfBusiness(
        businessId,
        limit,
        offset,
        q,
        order,
      ) as TData,
    queryKey: [
      useRolesServiceRoleControllerGetRolesOfBusinessKey,
      ...(queryKey ?? [{ businessId, limit, offset, order, q }]),
    ],
    ...options,
  });

export type RolesServiceroleControllerCreateRoleOfBusinessMutationResult =
  Awaited<ReturnType<typeof RolesService.roleControllerCreateRoleOfBusiness>>;

/**
 * Create a role of business
 */
export const useRolesServiceRoleControllerCreateRoleOfBusiness = <
  TData = RolesServiceroleControllerCreateRoleOfBusinessMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        businessId: string;
        requestBody: CreateRoleDto;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      businessId: string;
      requestBody: CreateRoleDto;
    },
    TContext
  >({
    mutationFn: ({ businessId, requestBody }) =>
      RolesService.roleControllerCreateRoleOfBusiness(
        businessId,
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type RolesServiceRoleControllerGetRoleOfBusinessDefaultResponse =
  Awaited<ReturnType<typeof RolesService.roleControllerGetRoleOfBusiness>>;

export type RolesServiceRoleControllerGetRoleOfBusinessQueryResult<
  TData = RolesServiceRoleControllerGetRoleOfBusinessDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useRolesServiceRoleControllerGetRoleOfBusinessKey =
  'RolesServiceRoleControllerGetRoleOfBusiness';

/**
 * Get a role of business
 */
export const useRolesServiceRoleControllerGetRoleOfBusiness = <
  TData = RolesServiceRoleControllerGetRoleOfBusinessDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    id,
    businessId,
  }: {
    id: string;
    businessId: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      RolesService.roleControllerGetRoleOfBusiness(id, businessId) as TData,
    queryKey: [
      useRolesServiceRoleControllerGetRoleOfBusinessKey,
      ...(queryKey ?? [{ businessId, id }]),
    ],
    ...options,
  });

export type RolesServiceroleControllerUpdateRoleOfBusinessMutationResult =
  Awaited<ReturnType<typeof RolesService.roleControllerUpdateRoleOfBusiness>>;

/**
 * Update a role of a business
 */
export const useRolesServiceRoleControllerUpdateRoleOfBusiness = <
  TData = RolesServiceroleControllerUpdateRoleOfBusinessMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        id: string;
        businessId: string;
        requestBody: UpdateRoleDto;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      id: string;
      businessId: string;
      requestBody: UpdateRoleDto;
    },
    TContext
  >({
    mutationFn: ({ id, businessId, requestBody }) =>
      RolesService.roleControllerUpdateRoleOfBusiness(
        id,
        businessId,
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type RolesServiceroleControllerDeleteRoleOfBusinessMutationResult =
  Awaited<ReturnType<typeof RolesService.roleControllerDeleteRoleOfBusiness>>;

/**
 * Delete a role of business
 */
export const useRolesServiceRoleControllerDeleteRoleOfBusiness = <
  TData = RolesServiceroleControllerDeleteRoleOfBusinessMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        id: string;
        businessId: string;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      id: string;
      businessId: string;
    },
    TContext
  >({
    mutationFn: ({ id, businessId }) =>
      RolesService.roleControllerDeleteRoleOfBusiness(
        id,
        businessId,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type RolesServiceRoleControllerGetRolesDefaultResponse = Awaited<
  ReturnType<typeof RolesService.roleControllerGetRoles>
>;

export type RolesServiceRoleControllerGetRolesQueryResult<
  TData = RolesServiceRoleControllerGetRolesDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useRolesServiceRoleControllerGetRolesKey =
  'RolesServiceRoleControllerGetRoles';

/**
 * Get list of roles of system
 */
export const useRolesServiceRoleControllerGetRoles = <
  TData = RolesServiceRoleControllerGetRolesDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    limit,
    offset,
    q,
    order,
  }: {
    limit?: number;
    offset: number;
    q?: string | null;
    order?: string | null;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      RolesService.roleControllerGetRoles(limit, offset, q, order) as TData,
    queryKey: [
      useRolesServiceRoleControllerGetRolesKey,
      ...(queryKey ?? [{ limit, offset, order, q }]),
    ],
    ...options,
  });

export type RolesServiceroleControllerCreateRoleMutationResult = Awaited<
  ReturnType<typeof RolesService.roleControllerCreateRole>
>;

/**
 * Create a role of system
 */
export const useRolesServiceRoleControllerCreateRole = <
  TData = RolesServiceroleControllerCreateRoleMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: CreateRoleDto;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: CreateRoleDto;
    },
    TContext
  >({
    mutationFn: ({ requestBody }) =>
      RolesService.roleControllerCreateRole(
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type RolesServiceRoleControllerGetRoleDefaultResponse = Awaited<
  ReturnType<typeof RolesService.roleControllerGetRole>
>;

export type RolesServiceRoleControllerGetRoleQueryResult<
  TData = RolesServiceRoleControllerGetRoleDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useRolesServiceRoleControllerGetRoleKey =
  'RolesServiceRoleControllerGetRole';

/**
 * Get a role of system
 */
export const useRolesServiceRoleControllerGetRole = <
  TData = RolesServiceRoleControllerGetRoleDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    id,
  }: {
    id: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () => RolesService.roleControllerGetRole(id) as TData,
    queryKey: [
      useRolesServiceRoleControllerGetRoleKey,
      ...(queryKey ?? [{ id }]),
    ],
    ...options,
  });

export type RolesServiceroleControllerUpdateRoleMutationResult = Awaited<
  ReturnType<typeof RolesService.roleControllerUpdateRole>
>;

/**
 * Update a role of a system
 */
export const useRolesServiceRoleControllerUpdateRole = <
  TData = RolesServiceroleControllerUpdateRoleMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        id: string;
        requestBody: UpdateRoleDto;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      id: string;
      requestBody: UpdateRoleDto;
    },
    TContext
  >({
    mutationFn: ({ id, requestBody }) =>
      RolesService.roleControllerUpdateRole(
        id,
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type RolesServiceroleControllerDeleteRoleMutationResult = Awaited<
  ReturnType<typeof RolesService.roleControllerDeleteRole>
>;

/**
 * Delete a role of system
 */
export const useRolesServiceRoleControllerDeleteRole = <
  TData = RolesServiceroleControllerDeleteRoleMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        id: string;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      id: string;
    },
    TContext
  >({
    mutationFn: ({ id }) =>
      RolesService.roleControllerDeleteRole(id) as unknown as Promise<TData>,
    ...options,
  });

export type PermissionsServicePermissionControllerGetPermissionsDefaultResponse =
  Awaited<
    ReturnType<typeof PermissionsService.permissionControllerGetPermissions>
  >;

export type PermissionsServicePermissionControllerGetPermissionsQueryResult<
  TData = PermissionsServicePermissionControllerGetPermissionsDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const usePermissionsServicePermissionControllerGetPermissionsKey =
  'PermissionsServicePermissionControllerGetPermissions';

/**
 * Get list of permission
 */
export const usePermissionsServicePermissionControllerGetPermissions = <
  TData = PermissionsServicePermissionControllerGetPermissionsDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    limit,
    offset,
    order,
  }: {
    limit?: number;
    offset: number;
    order?: string | null;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      PermissionsService.permissionControllerGetPermissions(
        limit,
        offset,
        order,
      ) as TData,
    queryKey: [
      usePermissionsServicePermissionControllerGetPermissionsKey,
      ...(queryKey ?? [{ limit, offset, order }]),
    ],
    ...options,
  });

export type NotificationsServiceNotificationControllerGetNotificationsDefaultResponse =
  Awaited<
    ReturnType<
      typeof NotificationsService.notificationControllerGetNotifications
    >
  >;

export type NotificationsServiceNotificationControllerGetNotificationsQueryResult<
  TData = NotificationsServiceNotificationControllerGetNotificationsDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useNotificationsServiceNotificationControllerGetNotificationsKey =
  'NotificationsServiceNotificationControllerGetNotifications';

/**
 * Get list of notifications
 */
export const useNotificationsServiceNotificationControllerGetNotifications = <
  TData = NotificationsServiceNotificationControllerGetNotificationsDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    limit,
    offset,
    type,
    order,
  }: {
    limit?: number;
    offset: number;
    type?: 'GENERAL' | 'PROMOTION' | null;
    order?: string | null;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      NotificationsService.notificationControllerGetNotifications(
        limit,
        offset,
        type,
        order,
      ) as TData,
    queryKey: [
      useNotificationsServiceNotificationControllerGetNotificationsKey,
      ...(queryKey ?? [{ limit, offset, order, type }]),
    ],
    ...options,
  });

export type NotificationsServicenotificationAdminControllerCreateNotificationMutationResult =
  Awaited<
    ReturnType<
      typeof NotificationsService.notificationAdminControllerCreateNotification
    >
  >;

/**
 * Create a notification
 */
export const useNotificationsServiceNotificationAdminControllerCreateNotification =
  <
    TData = NotificationsServicenotificationAdminControllerCreateNotificationMutationResult,
    TError = unknown,
    TContext = unknown,
  >(
    options?: Omit<
      UseMutationOptions<
        TData,
        TError,
        {
          requestBody: CreateNotificationDto;
        },
        TContext
      >,
      'mutationFn'
    >,
  ) =>
    useMutation<
      TData,
      TError,
      {
        requestBody: CreateNotificationDto;
      },
      TContext
    >({
      mutationFn: ({ requestBody }) =>
        NotificationsService.notificationAdminControllerCreateNotification(
          requestBody,
        ) as unknown as Promise<TData>,
      ...options,
    });

export type NotificationsServiceNotificationControllerGetNotificationDefaultResponse =
  Awaited<
    ReturnType<
      typeof NotificationsService.notificationControllerGetNotification
    >
  >;

export type NotificationsServiceNotificationControllerGetNotificationQueryResult<
  TData = NotificationsServiceNotificationControllerGetNotificationDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useNotificationsServiceNotificationControllerGetNotificationKey =
  'NotificationsServiceNotificationControllerGetNotification';

/**
 * Get a notification
 */
export const useNotificationsServiceNotificationControllerGetNotification = <
  TData = NotificationsServiceNotificationControllerGetNotificationDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    id,
  }: {
    id: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      NotificationsService.notificationControllerGetNotification(id) as TData,
    queryKey: [
      useNotificationsServiceNotificationControllerGetNotificationKey,
      ...(queryKey ?? [{ id }]),
    ],
    ...options,
  });

export type NotificationsServicenotificationControllerDeleteNotificationMutationResult =
  Awaited<
    ReturnType<
      typeof NotificationsService.notificationControllerDeleteNotification
    >
  >;

/**
 * Delete a notification
 */
export const useNotificationsServiceNotificationControllerDeleteNotification = <
  TData = NotificationsServicenotificationControllerDeleteNotificationMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        id: string;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      id: string;
    },
    TContext
  >({
    mutationFn: ({ id }) =>
      NotificationsService.notificationControllerDeleteNotification(
        id,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type NotificationsServicenotificationControllerUpdateNotificationMutationResult =
  Awaited<
    ReturnType<
      typeof NotificationsService.notificationControllerUpdateNotification
    >
  >;

/**
 * Read a notification
 */
export const useNotificationsServiceNotificationControllerUpdateNotification = <
  TData = NotificationsServicenotificationControllerUpdateNotificationMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        id: string;
        requestBody: UpdateNotificationDto;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      id: string;
      requestBody: UpdateNotificationDto;
    },
    TContext
  >({
    mutationFn: ({ id, requestBody }) =>
      NotificationsService.notificationControllerUpdateNotification(
        id,
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type NotificationsServicenotificationControllerSeenAllNotificationMutationResult =
  Awaited<
    ReturnType<
      typeof NotificationsService.notificationControllerSeenAllNotification
    >
  >;

/**
 * Read all notifications
 */
export const useNotificationsServiceNotificationControllerSeenAllNotification =
  <
    TData = NotificationsServicenotificationControllerSeenAllNotificationMutationResult,
    TError = unknown,
    TContext = unknown,
  >(
    options?: Omit<
      UseMutationOptions<
        TData,
        TError,
        {
          requestBody: SeenAllNotificationsDto;
        },
        TContext
      >,
      'mutationFn'
    >,
  ) =>
    useMutation<
      TData,
      TError,
      {
        requestBody: SeenAllNotificationsDto;
      },
      TContext
    >({
      mutationFn: ({ requestBody }) =>
        NotificationsService.notificationControllerSeenAllNotification(
          requestBody,
        ) as unknown as Promise<TData>,
      ...options,
    });

export type NotificationsServiceNotificationControllerHasUnseenNotificationsDefaultResponse =
  Awaited<
    ReturnType<
      typeof NotificationsService.notificationControllerHasUnseenNotifications
    >
  >;

export type NotificationsServiceNotificationControllerHasUnseenNotificationsQueryResult<
  TData = NotificationsServiceNotificationControllerHasUnseenNotificationsDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useNotificationsServiceNotificationControllerHasUnseenNotificationsKey =
  'NotificationsServiceNotificationControllerHasUnseenNotifications';

/**
 * Check if there are new notifications for the user
 */
export const useNotificationsServiceNotificationControllerHasUnseenNotifications =
  <
    TData = NotificationsServiceNotificationControllerHasUnseenNotificationsDefaultResponse,
    TError = unknown,
    TQueryKey extends unknown[] = unknown[],
  >(
    queryKey?: TQueryKey,
    options?: Omit<
      UseQueryOptions<TData, TError>,
      'queryKey' | 'queryFn' | 'initialData'
    >,
  ) =>
    useQuery<TData, TError>({
      queryFn: () =>
        NotificationsService.notificationControllerHasUnseenNotifications() as TData,
      queryKey: [
        useNotificationsServiceNotificationControllerHasUnseenNotificationsKey,
        ...(queryKey ?? []),
      ],
      ...options,
    });

export type NotificationsServiceNotificationControllerTotalUnseenNotificationsDefaultResponse =
  Awaited<
    ReturnType<
      typeof NotificationsService.notificationControllerTotalUnseenNotifications
    >
  >;

export type NotificationsServiceNotificationControllerTotalUnseenNotificationsQueryResult<
  TData = NotificationsServiceNotificationControllerTotalUnseenNotificationsDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useNotificationsServiceNotificationControllerTotalUnseenNotificationsKey =
  'NotificationsServiceNotificationControllerTotalUnseenNotifications';

/**
 * Count total unseen notifications for the user
 */
export const useNotificationsServiceNotificationControllerTotalUnseenNotifications =
  <
    TData = NotificationsServiceNotificationControllerTotalUnseenNotificationsDefaultResponse,
    TError = unknown,
    TQueryKey extends unknown[] = unknown[],
  >(
    queryKey?: TQueryKey,
    options?: Omit<
      UseQueryOptions<TData, TError>,
      'queryKey' | 'queryFn' | 'initialData'
    >,
  ) =>
    useQuery<TData, TError>({
      queryFn: () =>
        NotificationsService.notificationControllerTotalUnseenNotifications() as TData,
      queryKey: [
        useNotificationsServiceNotificationControllerTotalUnseenNotificationsKey,
        ...(queryKey ?? []),
      ],
      ...options,
    });

export type NotificationsServiceNotificationAdminControllerGetBusinessNotificationsDefaultResponse =
  Awaited<
    ReturnType<
      typeof NotificationsService.notificationAdminControllerGetBusinessNotifications
    >
  >;

export type NotificationsServiceNotificationAdminControllerGetBusinessNotificationsQueryResult<
  TData = NotificationsServiceNotificationAdminControllerGetBusinessNotificationsDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useNotificationsServiceNotificationAdminControllerGetBusinessNotificationsKey =
  'NotificationsServiceNotificationAdminControllerGetBusinessNotifications';

/**
 * Get list of notifications of a business
 */
export const useNotificationsServiceNotificationAdminControllerGetBusinessNotifications =
  <
    TData = NotificationsServiceNotificationAdminControllerGetBusinessNotificationsDefaultResponse,
    TError = unknown,
    TQueryKey extends unknown[] = unknown[],
  >(
    {
      businessId,
      limit,
      offset,
      notificationType,
      fromDate,
      toDate,
      order,
    }: {
      businessId: string;
      limit?: number;
      offset: number;
      notificationType?: any;
      fromDate?: string | null;
      toDate?: string | null;
      order?: string | null;
    },
    queryKey?: TQueryKey,
    options?: Omit<
      UseQueryOptions<TData, TError>,
      'queryKey' | 'queryFn' | 'initialData'
    >,
  ) =>
    useQuery<TData, TError>({
      queryFn: () =>
        NotificationsService.notificationAdminControllerGetBusinessNotifications(
          businessId,
          limit,
          offset,
          notificationType,
          fromDate,
          toDate,
          order,
        ) as TData,
      queryKey: [
        useNotificationsServiceNotificationAdminControllerGetBusinessNotificationsKey,
        ...(queryKey ?? [
          {
            businessId,
            fromDate,
            limit,
            notificationType,
            offset,
            order,
            toDate,
          },
        ]),
      ],
      ...options,
    });

export type NotificationsServiceNotificationAdminControllerGetBusinessNotificationDefaultResponse =
  Awaited<
    ReturnType<
      typeof NotificationsService.notificationAdminControllerGetBusinessNotification
    >
  >;

export type NotificationsServiceNotificationAdminControllerGetBusinessNotificationQueryResult<
  TData = NotificationsServiceNotificationAdminControllerGetBusinessNotificationDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useNotificationsServiceNotificationAdminControllerGetBusinessNotificationKey =
  'NotificationsServiceNotificationAdminControllerGetBusinessNotification';

/**
 * Get a notification of business
 */
export const useNotificationsServiceNotificationAdminControllerGetBusinessNotification =
  <
    TData = NotificationsServiceNotificationAdminControllerGetBusinessNotificationDefaultResponse,
    TError = unknown,
    TQueryKey extends unknown[] = unknown[],
  >(
    {
      id,
      businessId,
    }: {
      id: string;
      businessId: string;
    },
    queryKey?: TQueryKey,
    options?: Omit<
      UseQueryOptions<TData, TError>,
      'queryKey' | 'queryFn' | 'initialData'
    >,
  ) =>
    useQuery<TData, TError>({
      queryFn: () =>
        NotificationsService.notificationAdminControllerGetBusinessNotification(
          id,
          businessId,
        ) as TData,
      queryKey: [
        useNotificationsServiceNotificationAdminControllerGetBusinessNotificationKey,
        ...(queryKey ?? [{ businessId, id }]),
      ],
      ...options,
    });

export type NotificationsServicenotificationAdminControllerDeleteNotificationMutationResult =
  Awaited<
    ReturnType<
      typeof NotificationsService.notificationAdminControllerDeleteNotification
    >
  >;

/**
 * Delete a notification of business
 */
export const useNotificationsServiceNotificationAdminControllerDeleteNotification =
  <
    TData = NotificationsServicenotificationAdminControllerDeleteNotificationMutationResult,
    TError = unknown,
    TContext = unknown,
  >(
    options?: Omit<
      UseMutationOptions<
        TData,
        TError,
        {
          id: string;
          businessId: string;
        },
        TContext
      >,
      'mutationFn'
    >,
  ) =>
    useMutation<
      TData,
      TError,
      {
        id: string;
        businessId: string;
      },
      TContext
    >({
      mutationFn: ({ id, businessId }) =>
        NotificationsService.notificationAdminControllerDeleteNotification(
          id,
          businessId,
        ) as unknown as Promise<TData>,
      ...options,
    });

export type NotificationsServicenotificationAdminControllerUpdateNotificationMutationResult =
  Awaited<
    ReturnType<
      typeof NotificationsService.notificationAdminControllerUpdateNotification
    >
  >;

/**
 * Read a notification of business
 */
export const useNotificationsServiceNotificationAdminControllerUpdateNotification =
  <
    TData = NotificationsServicenotificationAdminControllerUpdateNotificationMutationResult,
    TError = unknown,
    TContext = unknown,
  >(
    options?: Omit<
      UseMutationOptions<
        TData,
        TError,
        {
          id: string;
          businessId: string;
          requestBody: UpdateNotificationDto;
        },
        TContext
      >,
      'mutationFn'
    >,
  ) =>
    useMutation<
      TData,
      TError,
      {
        id: string;
        businessId: string;
        requestBody: UpdateNotificationDto;
      },
      TContext
    >({
      mutationFn: ({ id, businessId, requestBody }) =>
        NotificationsService.notificationAdminControllerUpdateNotification(
          id,
          businessId,
          requestBody,
        ) as unknown as Promise<TData>,
      ...options,
    });

export type NotificationsServiceNotificationAdminControllerGetNotificationsDefaultResponse =
  Awaited<
    ReturnType<
      typeof NotificationsService.notificationAdminControllerGetNotifications
    >
  >;

export type NotificationsServiceNotificationAdminControllerGetNotificationsQueryResult<
  TData = NotificationsServiceNotificationAdminControllerGetNotificationsDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useNotificationsServiceNotificationAdminControllerGetNotificationsKey =
  'NotificationsServiceNotificationAdminControllerGetNotifications';

/**
 * Get list of notifications by admin
 */
export const useNotificationsServiceNotificationAdminControllerGetNotifications =
  <
    TData = NotificationsServiceNotificationAdminControllerGetNotificationsDefaultResponse,
    TError = unknown,
    TQueryKey extends unknown[] = unknown[],
  >(
    {
      limit,
      offset,
      notificationType,
      fromDate,
      toDate,
      createdByIds,
      sendTimeFrom,
      sendTimeTo,
      status,
      q,
      type,
      order,
    }: {
      limit?: number;
      offset: number;
      notificationType?: any;
      fromDate?: string | null;
      toDate?: string | null;
      createdByIds?: string;
      sendTimeFrom?: string | null;
      sendTimeTo?: string | null;
      status?:
        | 'COMPLETED'
        | 'SCHEDULED'
        | 'CANCELLED'
        | 'REMOVED'
        | 'IN_PROGRESS'
        | null;
      q?: string | null;
      type?: 'GENERAL' | 'PROMOTION' | null;
      order?: string | null;
    },
    queryKey?: TQueryKey,
    options?: Omit<
      UseQueryOptions<TData, TError>,
      'queryKey' | 'queryFn' | 'initialData'
    >,
  ) =>
    useQuery<TData, TError>({
      queryFn: () =>
        NotificationsService.notificationAdminControllerGetNotifications(
          limit,
          offset,
          notificationType,
          fromDate,
          toDate,
          createdByIds,
          sendTimeFrom,
          sendTimeTo,
          status,
          q,
          type,
          order,
        ) as TData,
      queryKey: [
        useNotificationsServiceNotificationAdminControllerGetNotificationsKey,
        ...(queryKey ?? [
          {
            createdByIds,
            fromDate,
            limit,
            notificationType,
            offset,
            order,
            q,
            sendTimeFrom,
            sendTimeTo,
            status,
            toDate,
            type,
          },
        ]),
      ],
      ...options,
    });

export type NotificationsServiceNotificationAdminControllerGetNotificationDefaultResponse =
  Awaited<
    ReturnType<
      typeof NotificationsService.notificationAdminControllerGetNotification
    >
  >;

export type NotificationsServiceNotificationAdminControllerGetNotificationQueryResult<
  TData = NotificationsServiceNotificationAdminControllerGetNotificationDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useNotificationsServiceNotificationAdminControllerGetNotificationKey =
  'NotificationsServiceNotificationAdminControllerGetNotification';

/**
 * Get a notification
 */
export const useNotificationsServiceNotificationAdminControllerGetNotification =
  <
    TData = NotificationsServiceNotificationAdminControllerGetNotificationDefaultResponse,
    TError = unknown,
    TQueryKey extends unknown[] = unknown[],
  >(
    {
      id,
    }: {
      id: string;
    },
    queryKey?: TQueryKey,
    options?: Omit<
      UseQueryOptions<TData, TError>,
      'queryKey' | 'queryFn' | 'initialData'
    >,
  ) =>
    useQuery<TData, TError>({
      queryFn: () =>
        NotificationsService.notificationAdminControllerGetNotification(
          id,
        ) as TData,
      queryKey: [
        useNotificationsServiceNotificationAdminControllerGetNotificationKey,
        ...(queryKey ?? [{ id }]),
      ],
      ...options,
    });

export type NotificationsServicenotificationAdminControllerUpdateStatusNotificationMutationResult =
  Awaited<
    ReturnType<
      typeof NotificationsService.notificationAdminControllerUpdateStatusNotification
    >
  >;

/**
 * Update status notification by admin
 */
export const useNotificationsServiceNotificationAdminControllerUpdateStatusNotification =
  <
    TData = NotificationsServicenotificationAdminControllerUpdateStatusNotificationMutationResult,
    TError = unknown,
    TContext = unknown,
  >(
    options?: Omit<
      UseMutationOptions<
        TData,
        TError,
        {
          id: string;
          requestBody: UpdateStatusNotificationDto;
        },
        TContext
      >,
      'mutationFn'
    >,
  ) =>
    useMutation<
      TData,
      TError,
      {
        id: string;
        requestBody: UpdateStatusNotificationDto;
      },
      TContext
    >({
      mutationFn: ({ id, requestBody }) =>
        NotificationsService.notificationAdminControllerUpdateStatusNotification(
          id,
          requestBody,
        ) as unknown as Promise<TData>,
      ...options,
    });

export type NotificationTokensServicenotificationTokenControllerCreateNotificationTokenMutationResult =
  Awaited<
    ReturnType<
      typeof NotificationTokensService.notificationTokenControllerCreateNotificationToken
    >
  >;

/**
 * Register a fcm token
 */
export const useNotificationTokensServiceNotificationTokenControllerCreateNotificationToken =
  <
    TData = NotificationTokensServicenotificationTokenControllerCreateNotificationTokenMutationResult,
    TError = unknown,
    TContext = unknown,
  >(
    options?: Omit<
      UseMutationOptions<
        TData,
        TError,
        {
          requestBody: CreateNotificationTokenDto;
        },
        TContext
      >,
      'mutationFn'
    >,
  ) =>
    useMutation<
      TData,
      TError,
      {
        requestBody: CreateNotificationTokenDto;
      },
      TContext
    >({
      mutationFn: ({ requestBody }) =>
        NotificationTokensService.notificationTokenControllerCreateNotificationToken(
          requestBody,
        ) as unknown as Promise<TData>,
      ...options,
    });

export type NotificationTokensServicenotificationTokenControllerDeleteNotificationTokenMutationResult =
  Awaited<
    ReturnType<
      typeof NotificationTokensService.notificationTokenControllerDeleteNotificationToken
    >
  >;

/**
 * Delete a notification token when logout
 */
export const useNotificationTokensServiceNotificationTokenControllerDeleteNotificationToken =
  <
    TData = NotificationTokensServicenotificationTokenControllerDeleteNotificationTokenMutationResult,
    TError = unknown,
    TContext = unknown,
  >(
    options?: Omit<
      UseMutationOptions<
        TData,
        TError,
        {
          id: string;
        },
        TContext
      >,
      'mutationFn'
    >,
  ) =>
    useMutation<
      TData,
      TError,
      {
        id: string;
      },
      TContext
    >({
      mutationFn: ({ id }) =>
        NotificationTokensService.notificationTokenControllerDeleteNotificationToken(
          id,
        ) as unknown as Promise<TData>,
      ...options,
    });

export type LoyalPointServiceLoyalPointControllerGetLoyalPointConfigDefaultResponse =
  Awaited<
    ReturnType<typeof LoyalPointService.loyalPointControllerGetLoyalPointConfig>
  >;

export type LoyalPointServiceLoyalPointControllerGetLoyalPointConfigQueryResult<
  TData = LoyalPointServiceLoyalPointControllerGetLoyalPointConfigDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useLoyalPointServiceLoyalPointControllerGetLoyalPointConfigKey =
  'LoyalPointServiceLoyalPointControllerGetLoyalPointConfig';

/**
 * Get loyal point config
 */
export const useLoyalPointServiceLoyalPointControllerGetLoyalPointConfig = <
  TData = LoyalPointServiceLoyalPointControllerGetLoyalPointConfigDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      LoyalPointService.loyalPointControllerGetLoyalPointConfig() as TData,
    queryKey: [
      useLoyalPointServiceLoyalPointControllerGetLoyalPointConfigKey,
      ...(queryKey ?? []),
    ],
    ...options,
  });

export type LoyalPointServiceloyalPointControllerUpdateLoyalPointConfigMutationResult =
  Awaited<
    ReturnType<
      typeof LoyalPointService.loyalPointControllerUpdateLoyalPointConfig
    >
  >;

/**
 * Update loyal point config
 */
export const useLoyalPointServiceLoyalPointControllerUpdateLoyalPointConfig = <
  TData = LoyalPointServiceloyalPointControllerUpdateLoyalPointConfigMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: UpdateLoyalPointConfigDto;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: UpdateLoyalPointConfigDto;
    },
    TContext
  >({
    mutationFn: ({ requestBody }) =>
      LoyalPointService.loyalPointControllerUpdateLoyalPointConfig(
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type LoyalPointServiceLoyalPointControllerGetLoyalPointRewardsDefaultResponse =
  Awaited<
    ReturnType<
      typeof LoyalPointService.loyalPointControllerGetLoyalPointRewards
    >
  >;

export type LoyalPointServiceLoyalPointControllerGetLoyalPointRewardsQueryResult<
  TData = LoyalPointServiceLoyalPointControllerGetLoyalPointRewardsDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useLoyalPointServiceLoyalPointControllerGetLoyalPointRewardsKey =
  'LoyalPointServiceLoyalPointControllerGetLoyalPointRewards';

/**
 * Get list loyal point rewards
 */
export const useLoyalPointServiceLoyalPointControllerGetLoyalPointRewards = <
  TData = LoyalPointServiceLoyalPointControllerGetLoyalPointRewardsDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      LoyalPointService.loyalPointControllerGetLoyalPointRewards() as TData,
    queryKey: [
      useLoyalPointServiceLoyalPointControllerGetLoyalPointRewardsKey,
      ...(queryKey ?? []),
    ],
    ...options,
  });

export type LoyalPointServiceLoyalPointUserControllerGetPointsDefaultResponse =
  Awaited<
    ReturnType<typeof LoyalPointService.loyalPointUserControllerGetPoints>
  >;

export type LoyalPointServiceLoyalPointUserControllerGetPointsQueryResult<
  TData = LoyalPointServiceLoyalPointUserControllerGetPointsDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useLoyalPointServiceLoyalPointUserControllerGetPointsKey =
  'LoyalPointServiceLoyalPointUserControllerGetPoints';

/**
 * Get list of points of me
 */
export const useLoyalPointServiceLoyalPointUserControllerGetPoints = <
  TData = LoyalPointServiceLoyalPointUserControllerGetPointsDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    limit,
    offset,
    order,
  }: {
    limit?: number;
    offset: number;
    order?: string | null;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      LoyalPointService.loyalPointUserControllerGetPoints(
        limit,
        offset,
        order,
      ) as TData,
    queryKey: [
      useLoyalPointServiceLoyalPointUserControllerGetPointsKey,
      ...(queryKey ?? [{ limit, offset, order }]),
    ],
    ...options,
  });

export type LoyalPointServiceLoyalPointPublicControllerGetAppLoyalPointConfigDefaultResponse =
  Awaited<
    ReturnType<
      typeof LoyalPointService.loyalPointPublicControllerGetAppLoyalPointConfig
    >
  >;

export type LoyalPointServiceLoyalPointPublicControllerGetAppLoyalPointConfigQueryResult<
  TData = LoyalPointServiceLoyalPointPublicControllerGetAppLoyalPointConfigDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useLoyalPointServiceLoyalPointPublicControllerGetAppLoyalPointConfigKey =
  'LoyalPointServiceLoyalPointPublicControllerGetAppLoyalPointConfig';

/**
 * Get loyal point config in app
 */
export const useLoyalPointServiceLoyalPointPublicControllerGetAppLoyalPointConfig =
  <
    TData = LoyalPointServiceLoyalPointPublicControllerGetAppLoyalPointConfigDefaultResponse,
    TError = unknown,
    TQueryKey extends unknown[] = unknown[],
  >(
    queryKey?: TQueryKey,
    options?: Omit<
      UseQueryOptions<TData, TError>,
      'queryKey' | 'queryFn' | 'initialData'
    >,
  ) =>
    useQuery<TData, TError>({
      queryFn: () =>
        LoyalPointService.loyalPointPublicControllerGetAppLoyalPointConfig() as TData,
      queryKey: [
        useLoyalPointServiceLoyalPointPublicControllerGetAppLoyalPointConfigKey,
        ...(queryKey ?? []),
      ],
      ...options,
    });

export type LocationsServicelocationControllerCreateLocationMutationResult =
  Awaited<ReturnType<typeof LocationsService.locationControllerCreateLocation>>;

/**
 * Create a location
 */
export const useLocationsServiceLocationControllerCreateLocation = <
  TData = LocationsServicelocationControllerCreateLocationMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: CreateLocationDto;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: CreateLocationDto;
    },
    TContext
  >({
    mutationFn: ({ requestBody }) =>
      LocationsService.locationControllerCreateLocation(
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type LocationsServiceLocationControllerGetListDefaultResponse = Awaited<
  ReturnType<typeof LocationsService.locationControllerGetList>
>;

export type LocationsServiceLocationControllerGetListQueryResult<
  TData = LocationsServiceLocationControllerGetListDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useLocationsServiceLocationControllerGetListKey =
  'LocationsServiceLocationControllerGetList';

/**
 * Get list of location
 */
export const useLocationsServiceLocationControllerGetList = <
  TData = LocationsServiceLocationControllerGetListDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    limit,
    offset,
    q,
    order,
  }: {
    limit?: number;
    offset: number;
    q?: string;
    order?: string | null;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      LocationsService.locationControllerGetList(
        limit,
        offset,
        q,
        order,
      ) as TData,
    queryKey: [
      useLocationsServiceLocationControllerGetListKey,
      ...(queryKey ?? [{ limit, offset, order, q }]),
    ],
    ...options,
  });

export type LocationsServiceLocationControllerGetLocationsByPlaceIdsDefaultResponse =
  Awaited<
    ReturnType<typeof LocationsService.locationControllerGetLocationsByPlaceIds>
  >;

export type LocationsServiceLocationControllerGetLocationsByPlaceIdsQueryResult<
  TData = LocationsServiceLocationControllerGetLocationsByPlaceIdsDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useLocationsServiceLocationControllerGetLocationsByPlaceIdsKey =
  'LocationsServiceLocationControllerGetLocationsByPlaceIds';

/**
 * Get list location by place ids
 */
export const useLocationsServiceLocationControllerGetLocationsByPlaceIds = <
  TData = LocationsServiceLocationControllerGetLocationsByPlaceIdsDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    placeIds,
  }: {
    placeIds?: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      LocationsService.locationControllerGetLocationsByPlaceIds(
        placeIds,
      ) as TData,
    queryKey: [
      useLocationsServiceLocationControllerGetLocationsByPlaceIdsKey,
      ...(queryKey ?? [{ placeIds }]),
    ],
    ...options,
  });

export type LocationsServiceLocationControllerGetLocationDefaultResponse =
  Awaited<ReturnType<typeof LocationsService.locationControllerGetLocation>>;

export type LocationsServiceLocationControllerGetLocationQueryResult<
  TData = LocationsServiceLocationControllerGetLocationDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useLocationsServiceLocationControllerGetLocationKey =
  'LocationsServiceLocationControllerGetLocation';

/**
 * Get location details
 */
export const useLocationsServiceLocationControllerGetLocation = <
  TData = LocationsServiceLocationControllerGetLocationDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    id,
  }: {
    id: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () => LocationsService.locationControllerGetLocation(id) as TData,
    queryKey: [
      useLocationsServiceLocationControllerGetLocationKey,
      ...(queryKey ?? [{ id }]),
    ],
    ...options,
  });

export type LocationsServicelocationControllerUpdateLocationMutationResult =
  Awaited<ReturnType<typeof LocationsService.locationControllerUpdateLocation>>;

/**
 * Update a location
 */
export const useLocationsServiceLocationControllerUpdateLocation = <
  TData = LocationsServicelocationControllerUpdateLocationMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        id: string;
        requestBody: UpdateLocationDto;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      id: string;
      requestBody: UpdateLocationDto;
    },
    TContext
  >({
    mutationFn: ({ id, requestBody }) =>
      LocationsService.locationControllerUpdateLocation(
        id,
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type LocationsServicelocationControllerDeleteLocationMutationResult =
  Awaited<ReturnType<typeof LocationsService.locationControllerDeleteLocation>>;

/**
 * Delete a location
 */
export const useLocationsServiceLocationControllerDeleteLocation = <
  TData = LocationsServicelocationControllerDeleteLocationMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        id: string;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      id: string;
    },
    TContext
  >({
    mutationFn: ({ id }) =>
      LocationsService.locationControllerDeleteLocation(
        id,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type LocationsServicelocationControllerUpdateStatusOfLocationMutationResult =
  Awaited<
    ReturnType<typeof LocationsService.locationControllerUpdateStatusOfLocation>
  >;

/**
 * Update status of location
 */
export const useLocationsServiceLocationControllerUpdateStatusOfLocation = <
  TData = LocationsServicelocationControllerUpdateStatusOfLocationMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        id: string;
        requestBody: UpdateLocationStatusDto;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      id: string;
      requestBody: UpdateLocationStatusDto;
    },
    TContext
  >({
    mutationFn: ({ id, requestBody }) =>
      LocationsService.locationControllerUpdateStatusOfLocation(
        id,
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type FeeConfigurationServiceFeeConfigurationControllerGetServiceFeesDefaultResponse =
  Awaited<
    ReturnType<
      typeof FeeConfigurationService.feeConfigurationControllerGetServiceFees
    >
  >;

export type FeeConfigurationServiceFeeConfigurationControllerGetServiceFeesQueryResult<
  TData = FeeConfigurationServiceFeeConfigurationControllerGetServiceFeesDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useFeeConfigurationServiceFeeConfigurationControllerGetServiceFeesKey =
  'FeeConfigurationServiceFeeConfigurationControllerGetServiceFees';

/**
 * Get list service fee
 */
export const useFeeConfigurationServiceFeeConfigurationControllerGetServiceFees =
  <
    TData = FeeConfigurationServiceFeeConfigurationControllerGetServiceFeesDefaultResponse,
    TError = unknown,
    TQueryKey extends unknown[] = unknown[],
  >(
    {
      limit,
      offset,
      q,
      vehicleType,
      status,
      createdByIds,
      perType,
      order,
    }: {
      limit?: number;
      offset: number;
      q?: string;
      vehicleType?: string;
      status?: string;
      createdByIds?: string;
      perType?: string;
      order?: string | null;
    },
    queryKey?: TQueryKey,
    options?: Omit<
      UseQueryOptions<TData, TError>,
      'queryKey' | 'queryFn' | 'initialData'
    >,
  ) =>
    useQuery<TData, TError>({
      queryFn: () =>
        FeeConfigurationService.feeConfigurationControllerGetServiceFees(
          limit,
          offset,
          q,
          vehicleType,
          status,
          createdByIds,
          perType,
          order,
        ) as TData,
      queryKey: [
        useFeeConfigurationServiceFeeConfigurationControllerGetServiceFeesKey,
        ...(queryKey ?? [
          {
            createdByIds,
            limit,
            offset,
            order,
            perType,
            q,
            status,
            vehicleType,
          },
        ]),
      ],
      ...options,
    });

export type FeeConfigurationServicefeeConfigurationControllerCreateServiceFeeMutationResult =
  Awaited<
    ReturnType<
      typeof FeeConfigurationService.feeConfigurationControllerCreateServiceFee
    >
  >;

/**
 * Create a service fee
 */
export const useFeeConfigurationServiceFeeConfigurationControllerCreateServiceFee =
  <
    TData = FeeConfigurationServicefeeConfigurationControllerCreateServiceFeeMutationResult,
    TError = unknown,
    TContext = unknown,
  >(
    options?: Omit<
      UseMutationOptions<
        TData,
        TError,
        {
          requestBody: CreateServiceFeeDto;
        },
        TContext
      >,
      'mutationFn'
    >,
  ) =>
    useMutation<
      TData,
      TError,
      {
        requestBody: CreateServiceFeeDto;
      },
      TContext
    >({
      mutationFn: ({ requestBody }) =>
        FeeConfigurationService.feeConfigurationControllerCreateServiceFee(
          requestBody,
        ) as unknown as Promise<TData>,
      ...options,
    });

export type FeeConfigurationServiceFeeConfigurationControllerGetServiceFeeDefaultResponse =
  Awaited<
    ReturnType<
      typeof FeeConfigurationService.feeConfigurationControllerGetServiceFee
    >
  >;

export type FeeConfigurationServiceFeeConfigurationControllerGetServiceFeeQueryResult<
  TData = FeeConfigurationServiceFeeConfigurationControllerGetServiceFeeDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useFeeConfigurationServiceFeeConfigurationControllerGetServiceFeeKey =
  'FeeConfigurationServiceFeeConfigurationControllerGetServiceFee';

/**
 * Get service fee detail
 */
export const useFeeConfigurationServiceFeeConfigurationControllerGetServiceFee =
  <
    TData = FeeConfigurationServiceFeeConfigurationControllerGetServiceFeeDefaultResponse,
    TError = unknown,
    TQueryKey extends unknown[] = unknown[],
  >(
    {
      id,
    }: {
      id: string;
    },
    queryKey?: TQueryKey,
    options?: Omit<
      UseQueryOptions<TData, TError>,
      'queryKey' | 'queryFn' | 'initialData'
    >,
  ) =>
    useQuery<TData, TError>({
      queryFn: () =>
        FeeConfigurationService.feeConfigurationControllerGetServiceFee(
          id,
        ) as TData,
      queryKey: [
        useFeeConfigurationServiceFeeConfigurationControllerGetServiceFeeKey,
        ...(queryKey ?? [{ id }]),
      ],
      ...options,
    });

export type FeeConfigurationServicefeeConfigurationControllerUpdateServiceFeeMutationResult =
  Awaited<
    ReturnType<
      typeof FeeConfigurationService.feeConfigurationControllerUpdateServiceFee
    >
  >;

/**
 * Update a service fee
 */
export const useFeeConfigurationServiceFeeConfigurationControllerUpdateServiceFee =
  <
    TData = FeeConfigurationServicefeeConfigurationControllerUpdateServiceFeeMutationResult,
    TError = unknown,
    TContext = unknown,
  >(
    options?: Omit<
      UseMutationOptions<
        TData,
        TError,
        {
          id: string;
          requestBody: CreateServiceFeeDto;
        },
        TContext
      >,
      'mutationFn'
    >,
  ) =>
    useMutation<
      TData,
      TError,
      {
        id: string;
        requestBody: CreateServiceFeeDto;
      },
      TContext
    >({
      mutationFn: ({ id, requestBody }) =>
        FeeConfigurationService.feeConfigurationControllerUpdateServiceFee(
          id,
          requestBody,
        ) as unknown as Promise<TData>,
      ...options,
    });

export type FeeConfigurationServicefeeConfigurationControllerDeleteServiceFeeMutationResult =
  Awaited<
    ReturnType<
      typeof FeeConfigurationService.feeConfigurationControllerDeleteServiceFee
    >
  >;

/**
 * Delete a service fee
 */
export const useFeeConfigurationServiceFeeConfigurationControllerDeleteServiceFee =
  <
    TData = FeeConfigurationServicefeeConfigurationControllerDeleteServiceFeeMutationResult,
    TError = unknown,
    TContext = unknown,
  >(
    options?: Omit<
      UseMutationOptions<
        TData,
        TError,
        {
          id: string;
        },
        TContext
      >,
      'mutationFn'
    >,
  ) =>
    useMutation<
      TData,
      TError,
      {
        id: string;
      },
      TContext
    >({
      mutationFn: ({ id }) =>
        FeeConfigurationService.feeConfigurationControllerDeleteServiceFee(
          id,
        ) as unknown as Promise<TData>,
      ...options,
    });

export type FeeConfigurationServicefeeConfigurationControllerUpdateStatusServiceFeeMutationResult =
  Awaited<
    ReturnType<
      typeof FeeConfigurationService.feeConfigurationControllerUpdateStatusServiceFee
    >
  >;

/**
 * Update status service fee
 */
export const useFeeConfigurationServiceFeeConfigurationControllerUpdateStatusServiceFee =
  <
    TData = FeeConfigurationServicefeeConfigurationControllerUpdateStatusServiceFeeMutationResult,
    TError = unknown,
    TContext = unknown,
  >(
    options?: Omit<
      UseMutationOptions<
        TData,
        TError,
        {
          id: string;
          requestBody: UpdateServiceFeeStatusDto;
        },
        TContext
      >,
      'mutationFn'
    >,
  ) =>
    useMutation<
      TData,
      TError,
      {
        id: string;
        requestBody: UpdateServiceFeeStatusDto;
      },
      TContext
    >({
      mutationFn: ({ id, requestBody }) =>
        FeeConfigurationService.feeConfigurationControllerUpdateStatusServiceFee(
          id,
          requestBody,
        ) as unknown as Promise<TData>,
      ...options,
    });

export type FeeConfigurationServiceFeeConfigurationControllerGetRateFeesDefaultResponse =
  Awaited<
    ReturnType<
      typeof FeeConfigurationService.feeConfigurationControllerGetRateFees
    >
  >;

export type FeeConfigurationServiceFeeConfigurationControllerGetRateFeesQueryResult<
  TData = FeeConfigurationServiceFeeConfigurationControllerGetRateFeesDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useFeeConfigurationServiceFeeConfigurationControllerGetRateFeesKey =
  'FeeConfigurationServiceFeeConfigurationControllerGetRateFees';

/**
 * Get list rate fee
 */
export const useFeeConfigurationServiceFeeConfigurationControllerGetRateFees = <
  TData = FeeConfigurationServiceFeeConfigurationControllerGetRateFeesDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    limit,
    offset,
    q,
    vehicleType,
    status,
    createdByIds,
    perType,
    order,
  }: {
    limit?: number;
    offset: number;
    q?: string;
    vehicleType?: string;
    status?: string;
    createdByIds?: string;
    perType?: string;
    order?: string | null;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      FeeConfigurationService.feeConfigurationControllerGetRateFees(
        limit,
        offset,
        q,
        vehicleType,
        status,
        createdByIds,
        perType,
        order,
      ) as TData,
    queryKey: [
      useFeeConfigurationServiceFeeConfigurationControllerGetRateFeesKey,
      ...(queryKey ?? [
        { createdByIds, limit, offset, order, perType, q, status, vehicleType },
      ]),
    ],
    ...options,
  });

export type FeeConfigurationServicefeeConfigurationControllerCreateRateFeeMutationResult =
  Awaited<
    ReturnType<
      typeof FeeConfigurationService.feeConfigurationControllerCreateRateFee
    >
  >;

/**
 * Create a rate fee
 */
export const useFeeConfigurationServiceFeeConfigurationControllerCreateRateFee =
  <
    TData = FeeConfigurationServicefeeConfigurationControllerCreateRateFeeMutationResult,
    TError = unknown,
    TContext = unknown,
  >(
    options?: Omit<
      UseMutationOptions<
        TData,
        TError,
        {
          requestBody: CreateRateFeeDto;
        },
        TContext
      >,
      'mutationFn'
    >,
  ) =>
    useMutation<
      TData,
      TError,
      {
        requestBody: CreateRateFeeDto;
      },
      TContext
    >({
      mutationFn: ({ requestBody }) =>
        FeeConfigurationService.feeConfigurationControllerCreateRateFee(
          requestBody,
        ) as unknown as Promise<TData>,
      ...options,
    });

export type FeeConfigurationServiceFeeConfigurationControllerGetRateFeeDefaultResponse =
  Awaited<
    ReturnType<
      typeof FeeConfigurationService.feeConfigurationControllerGetRateFee
    >
  >;

export type FeeConfigurationServiceFeeConfigurationControllerGetRateFeeQueryResult<
  TData = FeeConfigurationServiceFeeConfigurationControllerGetRateFeeDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useFeeConfigurationServiceFeeConfigurationControllerGetRateFeeKey =
  'FeeConfigurationServiceFeeConfigurationControllerGetRateFee';

/**
 * Get rate fee detail
 */
export const useFeeConfigurationServiceFeeConfigurationControllerGetRateFee = <
  TData = FeeConfigurationServiceFeeConfigurationControllerGetRateFeeDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    id,
  }: {
    id: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      FeeConfigurationService.feeConfigurationControllerGetRateFee(id) as TData,
    queryKey: [
      useFeeConfigurationServiceFeeConfigurationControllerGetRateFeeKey,
      ...(queryKey ?? [{ id }]),
    ],
    ...options,
  });

export type FeeConfigurationServicefeeConfigurationControllerUpdateRateFeeMutationResult =
  Awaited<
    ReturnType<
      typeof FeeConfigurationService.feeConfigurationControllerUpdateRateFee
    >
  >;

/**
 * Update a rate fee
 */
export const useFeeConfigurationServiceFeeConfigurationControllerUpdateRateFee =
  <
    TData = FeeConfigurationServicefeeConfigurationControllerUpdateRateFeeMutationResult,
    TError = unknown,
    TContext = unknown,
  >(
    options?: Omit<
      UseMutationOptions<
        TData,
        TError,
        {
          id: string;
          requestBody: CreateRateFeeDto;
        },
        TContext
      >,
      'mutationFn'
    >,
  ) =>
    useMutation<
      TData,
      TError,
      {
        id: string;
        requestBody: CreateRateFeeDto;
      },
      TContext
    >({
      mutationFn: ({ id, requestBody }) =>
        FeeConfigurationService.feeConfigurationControllerUpdateRateFee(
          id,
          requestBody,
        ) as unknown as Promise<TData>,
      ...options,
    });

export type FeeConfigurationServicefeeConfigurationControllerDeleteRateFeeMutationResult =
  Awaited<
    ReturnType<
      typeof FeeConfigurationService.feeConfigurationControllerDeleteRateFee
    >
  >;

/**
 * Delete a rate fee
 */
export const useFeeConfigurationServiceFeeConfigurationControllerDeleteRateFee =
  <
    TData = FeeConfigurationServicefeeConfigurationControllerDeleteRateFeeMutationResult,
    TError = unknown,
    TContext = unknown,
  >(
    options?: Omit<
      UseMutationOptions<
        TData,
        TError,
        {
          id: string;
        },
        TContext
      >,
      'mutationFn'
    >,
  ) =>
    useMutation<
      TData,
      TError,
      {
        id: string;
      },
      TContext
    >({
      mutationFn: ({ id }) =>
        FeeConfigurationService.feeConfigurationControllerDeleteRateFee(
          id,
        ) as unknown as Promise<TData>,
      ...options,
    });

export type FeeConfigurationServicefeeConfigurationControllerUpdateStatusRateFeeMutationResult =
  Awaited<
    ReturnType<
      typeof FeeConfigurationService.feeConfigurationControllerUpdateStatusRateFee
    >
  >;

/**
 * Update a status rate fee
 */
export const useFeeConfigurationServiceFeeConfigurationControllerUpdateStatusRateFee =
  <
    TData = FeeConfigurationServicefeeConfigurationControllerUpdateStatusRateFeeMutationResult,
    TError = unknown,
    TContext = unknown,
  >(
    options?: Omit<
      UseMutationOptions<
        TData,
        TError,
        {
          id: string;
          requestBody: UpdateServiceFeeStatusDto;
        },
        TContext
      >,
      'mutationFn'
    >,
  ) =>
    useMutation<
      TData,
      TError,
      {
        id: string;
        requestBody: UpdateServiceFeeStatusDto;
      },
      TContext
    >({
      mutationFn: ({ id, requestBody }) =>
        FeeConfigurationService.feeConfigurationControllerUpdateStatusRateFee(
          id,
          requestBody,
        ) as unknown as Promise<TData>,
      ...options,
    });

export type FeeConfigurationServiceFeeConfigurationControllerGetRateFeePriceDefaultResponse =
  Awaited<
    ReturnType<
      typeof FeeConfigurationService.feeConfigurationControllerGetRateFeePrice
    >
  >;

export type FeeConfigurationServiceFeeConfigurationControllerGetRateFeePriceQueryResult<
  TData = FeeConfigurationServiceFeeConfigurationControllerGetRateFeePriceDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useFeeConfigurationServiceFeeConfigurationControllerGetRateFeePriceKey =
  'FeeConfigurationServiceFeeConfigurationControllerGetRateFeePrice';

/**
 * Get rate fee price
 */
export const useFeeConfigurationServiceFeeConfigurationControllerGetRateFeePrice =
  <
    TData = FeeConfigurationServiceFeeConfigurationControllerGetRateFeePriceDefaultResponse,
    TError = unknown,
    TQueryKey extends unknown[] = unknown[],
  >(
    queryKey?: TQueryKey,
    options?: Omit<
      UseQueryOptions<TData, TError>,
      'queryKey' | 'queryFn' | 'initialData'
    >,
  ) =>
    useQuery<TData, TError>({
      queryFn: () =>
        FeeConfigurationService.feeConfigurationControllerGetRateFeePrice() as TData,
      queryKey: [
        useFeeConfigurationServiceFeeConfigurationControllerGetRateFeePriceKey,
        ...(queryKey ?? []),
      ],
      ...options,
    });

export type FeeConfigurationServiceFeeConfigurationControllerGetFixedFaresDefaultResponse =
  Awaited<
    ReturnType<
      typeof FeeConfigurationService.feeConfigurationControllerGetFixedFares
    >
  >;

export type FeeConfigurationServiceFeeConfigurationControllerGetFixedFaresQueryResult<
  TData = FeeConfigurationServiceFeeConfigurationControllerGetFixedFaresDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useFeeConfigurationServiceFeeConfigurationControllerGetFixedFaresKey =
  'FeeConfigurationServiceFeeConfigurationControllerGetFixedFares';

/**
 * Get list fixed fares
 */
export const useFeeConfigurationServiceFeeConfigurationControllerGetFixedFares =
  <
    TData = FeeConfigurationServiceFeeConfigurationControllerGetFixedFaresDefaultResponse,
    TError = unknown,
    TQueryKey extends unknown[] = unknown[],
  >(
    {
      limit,
      offset,
      q,
      vehicleType,
      status,
      createdByIds,
      zones,
      order,
    }: {
      limit?: number;
      offset: number;
      q?: string;
      vehicleType?: string;
      status?: string;
      createdByIds?: string;
      zones?: string;
      order?: string | null;
    },
    queryKey?: TQueryKey,
    options?: Omit<
      UseQueryOptions<TData, TError>,
      'queryKey' | 'queryFn' | 'initialData'
    >,
  ) =>
    useQuery<TData, TError>({
      queryFn: () =>
        FeeConfigurationService.feeConfigurationControllerGetFixedFares(
          limit,
          offset,
          q,
          vehicleType,
          status,
          createdByIds,
          zones,
          order,
        ) as TData,
      queryKey: [
        useFeeConfigurationServiceFeeConfigurationControllerGetFixedFaresKey,
        ...(queryKey ?? [
          { createdByIds, limit, offset, order, q, status, vehicleType, zones },
        ]),
      ],
      ...options,
    });

export type FeeConfigurationServicefeeConfigurationControllerCreateFixedFareMutationResult =
  Awaited<
    ReturnType<
      typeof FeeConfigurationService.feeConfigurationControllerCreateFixedFare
    >
  >;

/**
 * Create a fixed fare
 */
export const useFeeConfigurationServiceFeeConfigurationControllerCreateFixedFare =
  <
    TData = FeeConfigurationServicefeeConfigurationControllerCreateFixedFareMutationResult,
    TError = unknown,
    TContext = unknown,
  >(
    options?: Omit<
      UseMutationOptions<
        TData,
        TError,
        {
          requestBody: CreateFixedFareDto;
        },
        TContext
      >,
      'mutationFn'
    >,
  ) =>
    useMutation<
      TData,
      TError,
      {
        requestBody: CreateFixedFareDto;
      },
      TContext
    >({
      mutationFn: ({ requestBody }) =>
        FeeConfigurationService.feeConfigurationControllerCreateFixedFare(
          requestBody,
        ) as unknown as Promise<TData>,
      ...options,
    });

export type FeeConfigurationServiceFeeConfigurationControllerGetFixedFareDefaultResponse =
  Awaited<
    ReturnType<
      typeof FeeConfigurationService.feeConfigurationControllerGetFixedFare
    >
  >;

export type FeeConfigurationServiceFeeConfigurationControllerGetFixedFareQueryResult<
  TData = FeeConfigurationServiceFeeConfigurationControllerGetFixedFareDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useFeeConfigurationServiceFeeConfigurationControllerGetFixedFareKey =
  'FeeConfigurationServiceFeeConfigurationControllerGetFixedFare';

/**
 * Get fixed fare detail
 */
export const useFeeConfigurationServiceFeeConfigurationControllerGetFixedFare =
  <
    TData = FeeConfigurationServiceFeeConfigurationControllerGetFixedFareDefaultResponse,
    TError = unknown,
    TQueryKey extends unknown[] = unknown[],
  >(
    {
      id,
    }: {
      id: string;
    },
    queryKey?: TQueryKey,
    options?: Omit<
      UseQueryOptions<TData, TError>,
      'queryKey' | 'queryFn' | 'initialData'
    >,
  ) =>
    useQuery<TData, TError>({
      queryFn: () =>
        FeeConfigurationService.feeConfigurationControllerGetFixedFare(
          id,
        ) as TData,
      queryKey: [
        useFeeConfigurationServiceFeeConfigurationControllerGetFixedFareKey,
        ...(queryKey ?? [{ id }]),
      ],
      ...options,
    });

export type FeeConfigurationServicefeeConfigurationControllerUpdateFixedFareMutationResult =
  Awaited<
    ReturnType<
      typeof FeeConfigurationService.feeConfigurationControllerUpdateFixedFare
    >
  >;

/**
 * Update a fixed fare
 */
export const useFeeConfigurationServiceFeeConfigurationControllerUpdateFixedFare =
  <
    TData = FeeConfigurationServicefeeConfigurationControllerUpdateFixedFareMutationResult,
    TError = unknown,
    TContext = unknown,
  >(
    options?: Omit<
      UseMutationOptions<
        TData,
        TError,
        {
          id: string;
          requestBody: UpdateFixedFareDto;
        },
        TContext
      >,
      'mutationFn'
    >,
  ) =>
    useMutation<
      TData,
      TError,
      {
        id: string;
        requestBody: UpdateFixedFareDto;
      },
      TContext
    >({
      mutationFn: ({ id, requestBody }) =>
        FeeConfigurationService.feeConfigurationControllerUpdateFixedFare(
          id,
          requestBody,
        ) as unknown as Promise<TData>,
      ...options,
    });

export type FeeConfigurationServicefeeConfigurationControllerDeleteFixedFareMutationResult =
  Awaited<
    ReturnType<
      typeof FeeConfigurationService.feeConfigurationControllerDeleteFixedFare
    >
  >;

/**
 * Delete a fixed fare
 */
export const useFeeConfigurationServiceFeeConfigurationControllerDeleteFixedFare =
  <
    TData = FeeConfigurationServicefeeConfigurationControllerDeleteFixedFareMutationResult,
    TError = unknown,
    TContext = unknown,
  >(
    options?: Omit<
      UseMutationOptions<
        TData,
        TError,
        {
          id: string;
        },
        TContext
      >,
      'mutationFn'
    >,
  ) =>
    useMutation<
      TData,
      TError,
      {
        id: string;
      },
      TContext
    >({
      mutationFn: ({ id }) =>
        FeeConfigurationService.feeConfigurationControllerDeleteFixedFare(
          id,
        ) as unknown as Promise<TData>,
      ...options,
    });

export type FeeConfigurationServicefeeConfigurationControllerUpdateStatusFixedFareMutationResult =
  Awaited<
    ReturnType<
      typeof FeeConfigurationService.feeConfigurationControllerUpdateStatusFixedFare
    >
  >;

/**
 * Update status fixed fare
 */
export const useFeeConfigurationServiceFeeConfigurationControllerUpdateStatusFixedFare =
  <
    TData = FeeConfigurationServicefeeConfigurationControllerUpdateStatusFixedFareMutationResult,
    TError = unknown,
    TContext = unknown,
  >(
    options?: Omit<
      UseMutationOptions<
        TData,
        TError,
        {
          id: string;
          requestBody: UpdateFixedFareStatusDto;
        },
        TContext
      >,
      'mutationFn'
    >,
  ) =>
    useMutation<
      TData,
      TError,
      {
        id: string;
        requestBody: UpdateFixedFareStatusDto;
      },
      TContext
    >({
      mutationFn: ({ id, requestBody }) =>
        FeeConfigurationService.feeConfigurationControllerUpdateStatusFixedFare(
          id,
          requestBody,
        ) as unknown as Promise<TData>,
      ...options,
    });

export type EmployeesServiceEmployeeControllerGetEmployeesOfBusinessDefaultResponse =
  Awaited<
    ReturnType<typeof EmployeesService.employeeControllerGetEmployeesOfBusiness>
  >;

export type EmployeesServiceEmployeeControllerGetEmployeesOfBusinessQueryResult<
  TData = EmployeesServiceEmployeeControllerGetEmployeesOfBusinessDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useEmployeesServiceEmployeeControllerGetEmployeesOfBusinessKey =
  'EmployeesServiceEmployeeControllerGetEmployeesOfBusiness';

/**
 * Get list of employee of a business
 */
export const useEmployeesServiceEmployeeControllerGetEmployeesOfBusiness = <
  TData = EmployeesServiceEmployeeControllerGetEmployeesOfBusinessDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    businessId,
    limit,
    offset,
    q,
    departmentId,
    order,
  }: {
    businessId: string;
    limit?: number;
    offset: number;
    q?: string | null;
    departmentId?: string | null;
    order?: string | null;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      EmployeesService.employeeControllerGetEmployeesOfBusiness(
        businessId,
        limit,
        offset,
        q,
        departmentId,
        order,
      ) as TData,
    queryKey: [
      useEmployeesServiceEmployeeControllerGetEmployeesOfBusinessKey,
      ...(queryKey ?? [{ businessId, departmentId, limit, offset, order, q }]),
    ],
    ...options,
  });

export type EmployeesServiceemployeeControllerCreateEmployeeOfBusinessMutationResult =
  Awaited<
    ReturnType<
      typeof EmployeesService.employeeControllerCreateEmployeeOfBusiness
    >
  >;

/**
 * Create a employee of a business
 */
export const useEmployeesServiceEmployeeControllerCreateEmployeeOfBusiness = <
  TData = EmployeesServiceemployeeControllerCreateEmployeeOfBusinessMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        businessId: string;
        requestBody: CreateEmployeeOfBusinessDto;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      businessId: string;
      requestBody: CreateEmployeeOfBusinessDto;
    },
    TContext
  >({
    mutationFn: ({ businessId, requestBody }) =>
      EmployeesService.employeeControllerCreateEmployeeOfBusiness(
        businessId,
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type EmployeesServiceEmployeeControllerGetDepartmentOfBusinessDefaultResponse =
  Awaited<
    ReturnType<
      typeof EmployeesService.employeeControllerGetDepartmentOfBusiness
    >
  >;

export type EmployeesServiceEmployeeControllerGetDepartmentOfBusinessQueryResult<
  TData = EmployeesServiceEmployeeControllerGetDepartmentOfBusinessDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useEmployeesServiceEmployeeControllerGetDepartmentOfBusinessKey =
  'EmployeesServiceEmployeeControllerGetDepartmentOfBusiness';

/**
 * Get a employee of a business
 */
export const useEmployeesServiceEmployeeControllerGetDepartmentOfBusiness = <
  TData = EmployeesServiceEmployeeControllerGetDepartmentOfBusinessDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    id,
    businessId,
  }: {
    id: string;
    businessId: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      EmployeesService.employeeControllerGetDepartmentOfBusiness(
        id,
        businessId,
      ) as TData,
    queryKey: [
      useEmployeesServiceEmployeeControllerGetDepartmentOfBusinessKey,
      ...(queryKey ?? [{ businessId, id }]),
    ],
    ...options,
  });

export type EmployeesServiceemployeeControllerUpdateEmployeeOfBusinessMutationResult =
  Awaited<
    ReturnType<
      typeof EmployeesService.employeeControllerUpdateEmployeeOfBusiness
    >
  >;

/**
 * Update a employee of a business
 */
export const useEmployeesServiceEmployeeControllerUpdateEmployeeOfBusiness = <
  TData = EmployeesServiceemployeeControllerUpdateEmployeeOfBusinessMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        id: string;
        businessId: string;
        requestBody: UpdateEmployeeOfBusinessDto;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      id: string;
      businessId: string;
      requestBody: UpdateEmployeeOfBusinessDto;
    },
    TContext
  >({
    mutationFn: ({ id, businessId, requestBody }) =>
      EmployeesService.employeeControllerUpdateEmployeeOfBusiness(
        id,
        businessId,
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type EmployeesServiceemployeeControllerDeleteEmployeeOfBusinessMutationResult =
  Awaited<
    ReturnType<
      typeof EmployeesService.employeeControllerDeleteEmployeeOfBusiness
    >
  >;

/**
 * Delete a employee of a business
 */
export const useEmployeesServiceEmployeeControllerDeleteEmployeeOfBusiness = <
  TData = EmployeesServiceemployeeControllerDeleteEmployeeOfBusinessMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        id: string;
        businessId: string;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      id: string;
      businessId: string;
    },
    TContext
  >({
    mutationFn: ({ id, businessId }) =>
      EmployeesService.employeeControllerDeleteEmployeeOfBusiness(
        id,
        businessId,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type EmployeesServiceEmployeeControllerGetEmployeesOfDepartmentDefaultResponse =
  Awaited<
    ReturnType<
      typeof EmployeesService.employeeControllerGetEmployeesOfDepartment
    >
  >;

export type EmployeesServiceEmployeeControllerGetEmployeesOfDepartmentQueryResult<
  TData = EmployeesServiceEmployeeControllerGetEmployeesOfDepartmentDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useEmployeesServiceEmployeeControllerGetEmployeesOfDepartmentKey =
  'EmployeesServiceEmployeeControllerGetEmployeesOfDepartment';

/**
 * Get list of employee of a department
 */
export const useEmployeesServiceEmployeeControllerGetEmployeesOfDepartment = <
  TData = EmployeesServiceEmployeeControllerGetEmployeesOfDepartmentDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    departmentId,
    limit,
    offset,
    q,
    order,
  }: {
    departmentId: string;
    limit?: number;
    offset: number;
    q?: string | null;
    order?: string | null;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      EmployeesService.employeeControllerGetEmployeesOfDepartment(
        departmentId,
        limit,
        offset,
        q,
        order,
      ) as TData,
    queryKey: [
      useEmployeesServiceEmployeeControllerGetEmployeesOfDepartmentKey,
      ...(queryKey ?? [{ departmentId, limit, offset, order, q }]),
    ],
    ...options,
  });

export type EmployeesServiceemployeeControllerCreateEmployeeMutationResult =
  Awaited<ReturnType<typeof EmployeesService.employeeControllerCreateEmployee>>;

/**
 * Create a employee of a department
 */
export const useEmployeesServiceEmployeeControllerCreateEmployee = <
  TData = EmployeesServiceemployeeControllerCreateEmployeeMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        departmentId: string;
        requestBody: CreateEmployeeDto;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      departmentId: string;
      requestBody: CreateEmployeeDto;
    },
    TContext
  >({
    mutationFn: ({ departmentId, requestBody }) =>
      EmployeesService.employeeControllerCreateEmployee(
        departmentId,
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type EmployeesServiceEmployeeControllerGetDepartmentDefaultResponse =
  Awaited<ReturnType<typeof EmployeesService.employeeControllerGetDepartment>>;

export type EmployeesServiceEmployeeControllerGetDepartmentQueryResult<
  TData = EmployeesServiceEmployeeControllerGetDepartmentDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useEmployeesServiceEmployeeControllerGetDepartmentKey =
  'EmployeesServiceEmployeeControllerGetDepartment';

/**
 * Get a employee of a department
 */
export const useEmployeesServiceEmployeeControllerGetDepartment = <
  TData = EmployeesServiceEmployeeControllerGetDepartmentDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    id,
    departmentId,
  }: {
    id: string;
    departmentId: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      EmployeesService.employeeControllerGetDepartment(
        id,
        departmentId,
      ) as TData,
    queryKey: [
      useEmployeesServiceEmployeeControllerGetDepartmentKey,
      ...(queryKey ?? [{ departmentId, id }]),
    ],
    ...options,
  });

export type EmployeesServiceemployeeControllerUpdateEmployeeMutationResult =
  Awaited<ReturnType<typeof EmployeesService.employeeControllerUpdateEmployee>>;

/**
 * Update a employee of a department
 */
export const useEmployeesServiceEmployeeControllerUpdateEmployee = <
  TData = EmployeesServiceemployeeControllerUpdateEmployeeMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        id: string;
        departmentId: string;
        requestBody: UpdateEmployeeDto;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      id: string;
      departmentId: string;
      requestBody: UpdateEmployeeDto;
    },
    TContext
  >({
    mutationFn: ({ id, departmentId, requestBody }) =>
      EmployeesService.employeeControllerUpdateEmployee(
        id,
        departmentId,
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type EmployeesServiceemployeeControllerDeleteEmployeeMutationResult =
  Awaited<ReturnType<typeof EmployeesService.employeeControllerDeleteEmployee>>;

/**
 * Delete a employee of a department
 */
export const useEmployeesServiceEmployeeControllerDeleteEmployee = <
  TData = EmployeesServiceemployeeControllerDeleteEmployeeMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        id: string;
        departmentId: string;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      id: string;
      departmentId: string;
    },
    TContext
  >({
    mutationFn: ({ id, departmentId }) =>
      EmployeesService.employeeControllerDeleteEmployee(
        id,
        departmentId,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type EmployeesServiceEmployeeControllerGetAdminsOfBusinessDefaultResponse =
  Awaited<
    ReturnType<typeof EmployeesService.employeeControllerGetAdminsOfBusiness>
  >;

export type EmployeesServiceEmployeeControllerGetAdminsOfBusinessQueryResult<
  TData = EmployeesServiceEmployeeControllerGetAdminsOfBusinessDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useEmployeesServiceEmployeeControllerGetAdminsOfBusinessKey =
  'EmployeesServiceEmployeeControllerGetAdminsOfBusiness';

/**
 * Get list of admins of a business
 */
export const useEmployeesServiceEmployeeControllerGetAdminsOfBusiness = <
  TData = EmployeesServiceEmployeeControllerGetAdminsOfBusinessDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    businessId,
    limit,
    offset,
    q,
    roleId,
    order,
  }: {
    businessId: string;
    limit?: number;
    offset: number;
    q?: string | null;
    roleId?: string | null;
    order?: string | null;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      EmployeesService.employeeControllerGetAdminsOfBusiness(
        businessId,
        limit,
        offset,
        q,
        roleId,
        order,
      ) as TData,
    queryKey: [
      useEmployeesServiceEmployeeControllerGetAdminsOfBusinessKey,
      ...(queryKey ?? [{ businessId, limit, offset, order, q, roleId }]),
    ],
    ...options,
  });

export type EmployeesServiceemployeeControllerCreateAdminOfBusinessMutationResult =
  Awaited<
    ReturnType<typeof EmployeesService.employeeControllerCreateAdminOfBusiness>
  >;

/**
 * Create an admin of a business
 */
export const useEmployeesServiceEmployeeControllerCreateAdminOfBusiness = <
  TData = EmployeesServiceemployeeControllerCreateAdminOfBusinessMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        businessId: string;
        requestBody: CreateAdminOfBusinessDto;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      businessId: string;
      requestBody: CreateAdminOfBusinessDto;
    },
    TContext
  >({
    mutationFn: ({ businessId, requestBody }) =>
      EmployeesService.employeeControllerCreateAdminOfBusiness(
        businessId,
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type EmployeesServiceEmployeeControllerGetAdminOfBusinessDefaultResponse =
  Awaited<
    ReturnType<typeof EmployeesService.employeeControllerGetAdminOfBusiness>
  >;

export type EmployeesServiceEmployeeControllerGetAdminOfBusinessQueryResult<
  TData = EmployeesServiceEmployeeControllerGetAdminOfBusinessDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useEmployeesServiceEmployeeControllerGetAdminOfBusinessKey =
  'EmployeesServiceEmployeeControllerGetAdminOfBusiness';

/**
 * Get an admin of a business
 */
export const useEmployeesServiceEmployeeControllerGetAdminOfBusiness = <
  TData = EmployeesServiceEmployeeControllerGetAdminOfBusinessDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    id,
    businessId,
  }: {
    id: string;
    businessId: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      EmployeesService.employeeControllerGetAdminOfBusiness(
        id,
        businessId,
      ) as TData,
    queryKey: [
      useEmployeesServiceEmployeeControllerGetAdminOfBusinessKey,
      ...(queryKey ?? [{ businessId, id }]),
    ],
    ...options,
  });

export type EmployeesServiceemployeeControllerDeleteAdminOfBusinessMutationResult =
  Awaited<
    ReturnType<typeof EmployeesService.employeeControllerDeleteAdminOfBusiness>
  >;

/**
 * Delete an admin of a business
 */
export const useEmployeesServiceEmployeeControllerDeleteAdminOfBusiness = <
  TData = EmployeesServiceemployeeControllerDeleteAdminOfBusinessMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        id: string;
        businessId: string;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      id: string;
      businessId: string;
    },
    TContext
  >({
    mutationFn: ({ id, businessId }) =>
      EmployeesService.employeeControllerDeleteAdminOfBusiness(
        id,
        businessId,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type EmployeesServiceemployeeControllerUpdateAdminOfBusinessMutationResult =
  Awaited<
    ReturnType<typeof EmployeesService.employeeControllerUpdateAdminOfBusiness>
  >;

/**
 * Update an admin of a business
 */
export const useEmployeesServiceEmployeeControllerUpdateAdminOfBusiness = <
  TData = EmployeesServiceemployeeControllerUpdateAdminOfBusinessMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        id: string;
        businessId: string;
        requestBody: UpdateAdminOfBusinessDto;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      id: string;
      businessId: string;
      requestBody: UpdateAdminOfBusinessDto;
    },
    TContext
  >({
    mutationFn: ({ id, businessId, requestBody }) =>
      EmployeesService.employeeControllerUpdateAdminOfBusiness(
        id,
        businessId,
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type EmployeesServiceemployeeControllerUpdateEmployeeStatusMutationResult =
  Awaited<
    ReturnType<typeof EmployeesService.employeeControllerUpdateEmployeeStatus>
  >;

/**
 * Update status of a employee
 */
export const useEmployeesServiceEmployeeControllerUpdateEmployeeStatus = <
  TData = EmployeesServiceemployeeControllerUpdateEmployeeStatusMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        id: string;
        requestBody: UpdateEmployeeStatusDto;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      id: string;
      requestBody: UpdateEmployeeStatusDto;
    },
    TContext
  >({
    mutationFn: ({ id, requestBody }) =>
      EmployeesService.employeeControllerUpdateEmployeeStatus(
        id,
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type DriverServiceDriverControllerGetDriverDefaultResponse = Awaited<
  ReturnType<typeof DriverService.driverControllerGetDriver>
>;

export type DriverServiceDriverControllerGetDriverQueryResult<
  TData = DriverServiceDriverControllerGetDriverDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useDriverServiceDriverControllerGetDriverKey =
  'DriverServiceDriverControllerGetDriver';

/**
 * Get detail driver information
 */
export const useDriverServiceDriverControllerGetDriver = <
  TData = DriverServiceDriverControllerGetDriverDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    id,
  }: {
    id: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () => DriverService.driverControllerGetDriver(id) as TData,
    queryKey: [
      useDriverServiceDriverControllerGetDriverKey,
      ...(queryKey ?? [{ id }]),
    ],
    ...options,
  });

export type DepartmentsServiceDepartmentControllerGetDepartmentsOfBusinessDefaultResponse =
  Awaited<
    ReturnType<
      typeof DepartmentsService.departmentControllerGetDepartmentsOfBusiness
    >
  >;

export type DepartmentsServiceDepartmentControllerGetDepartmentsOfBusinessQueryResult<
  TData = DepartmentsServiceDepartmentControllerGetDepartmentsOfBusinessDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useDepartmentsServiceDepartmentControllerGetDepartmentsOfBusinessKey =
  'DepartmentsServiceDepartmentControllerGetDepartmentsOfBusiness';

/**
 * Get list of departments of a business
 */
export const useDepartmentsServiceDepartmentControllerGetDepartmentsOfBusiness =
  <
    TData = DepartmentsServiceDepartmentControllerGetDepartmentsOfBusinessDefaultResponse,
    TError = unknown,
    TQueryKey extends unknown[] = unknown[],
  >(
    {
      businessId,
      limit,
      offset,
      name,
      order,
    }: {
      businessId: string;
      limit?: number;
      offset: number;
      name?: string | null;
      order?: string | null;
    },
    queryKey?: TQueryKey,
    options?: Omit<
      UseQueryOptions<TData, TError>,
      'queryKey' | 'queryFn' | 'initialData'
    >,
  ) =>
    useQuery<TData, TError>({
      queryFn: () =>
        DepartmentsService.departmentControllerGetDepartmentsOfBusiness(
          businessId,
          limit,
          offset,
          name,
          order,
        ) as TData,
      queryKey: [
        useDepartmentsServiceDepartmentControllerGetDepartmentsOfBusinessKey,
        ...(queryKey ?? [{ businessId, limit, name, offset, order }]),
      ],
      ...options,
    });

export type DepartmentsServicedepartmentControllerCreateDepartmentMutationResult =
  Awaited<
    ReturnType<typeof DepartmentsService.departmentControllerCreateDepartment>
  >;

/**
 * Create a department
 */
export const useDepartmentsServiceDepartmentControllerCreateDepartment = <
  TData = DepartmentsServicedepartmentControllerCreateDepartmentMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        businessId: string;
        requestBody: CreateDepartmentDto;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      businessId: string;
      requestBody: CreateDepartmentDto;
    },
    TContext
  >({
    mutationFn: ({ businessId, requestBody }) =>
      DepartmentsService.departmentControllerCreateDepartment(
        businessId,
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type DepartmentsServiceDepartmentControllerGetDepartmentDefaultResponse =
  Awaited<
    ReturnType<typeof DepartmentsService.departmentControllerGetDepartment>
  >;

export type DepartmentsServiceDepartmentControllerGetDepartmentQueryResult<
  TData = DepartmentsServiceDepartmentControllerGetDepartmentDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useDepartmentsServiceDepartmentControllerGetDepartmentKey =
  'DepartmentsServiceDepartmentControllerGetDepartment';

/**
 * Get a department
 */
export const useDepartmentsServiceDepartmentControllerGetDepartment = <
  TData = DepartmentsServiceDepartmentControllerGetDepartmentDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    departmentId,
    businessId,
  }: {
    departmentId: string;
    businessId: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      DepartmentsService.departmentControllerGetDepartment(
        departmentId,
        businessId,
      ) as TData,
    queryKey: [
      useDepartmentsServiceDepartmentControllerGetDepartmentKey,
      ...(queryKey ?? [{ businessId, departmentId }]),
    ],
    ...options,
  });

export type DepartmentsServicedepartmentControllerUpdateDepartmentMutationResult =
  Awaited<
    ReturnType<typeof DepartmentsService.departmentControllerUpdateDepartment>
  >;

/**
 * Update a department
 */
export const useDepartmentsServiceDepartmentControllerUpdateDepartment = <
  TData = DepartmentsServicedepartmentControllerUpdateDepartmentMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        businessId: string;
        departmentId: string;
        requestBody: UpdateDepartmentDto;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      businessId: string;
      departmentId: string;
      requestBody: UpdateDepartmentDto;
    },
    TContext
  >({
    mutationFn: ({ businessId, departmentId, requestBody }) =>
      DepartmentsService.departmentControllerUpdateDepartment(
        businessId,
        departmentId,
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type DepartmentsServicedepartmentControllerDeleteEmployeeMutationResult =
  Awaited<
    ReturnType<typeof DepartmentsService.departmentControllerDeleteEmployee>
  >;

/**
 * Delete a department
 */
export const useDepartmentsServiceDepartmentControllerDeleteEmployee = <
  TData = DepartmentsServicedepartmentControllerDeleteEmployeeMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        departmentId: string;
        businessId: string;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      departmentId: string;
      businessId: string;
    },
    TContext
  >({
    mutationFn: ({ departmentId, businessId }) =>
      DepartmentsService.departmentControllerDeleteEmployee(
        departmentId,
        businessId,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type DepartmentsServicedepartmentControllerUpdateDepartmentStatusMutationResult =
  Awaited<
    ReturnType<
      typeof DepartmentsService.departmentControllerUpdateDepartmentStatus
    >
  >;

/**
 * Update status of a department
 */
export const useDepartmentsServiceDepartmentControllerUpdateDepartmentStatus = <
  TData = DepartmentsServicedepartmentControllerUpdateDepartmentStatusMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        departmentId: string;
        requestBody: UpdateDepartmentStatusDto;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      departmentId: string;
      requestBody: UpdateDepartmentStatusDto;
    },
    TContext
  >({
    mutationFn: ({ departmentId, requestBody }) =>
      DepartmentsService.departmentControllerUpdateDepartmentStatus(
        departmentId,
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type DepartmentRolesServiceDepartmentRoleControllerGetRolesOfDepartmentGroupDefaultResponse =
  Awaited<
    ReturnType<
      typeof DepartmentRolesService.departmentRoleControllerGetRolesOfDepartmentGroup
    >
  >;

export type DepartmentRolesServiceDepartmentRoleControllerGetRolesOfDepartmentGroupQueryResult<
  TData = DepartmentRolesServiceDepartmentRoleControllerGetRolesOfDepartmentGroupDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useDepartmentRolesServiceDepartmentRoleControllerGetRolesOfDepartmentGroupKey =
  'DepartmentRolesServiceDepartmentRoleControllerGetRolesOfDepartmentGroup';

/**
 * Get list of role of a group
 */
export const useDepartmentRolesServiceDepartmentRoleControllerGetRolesOfDepartmentGroup =
  <
    TData = DepartmentRolesServiceDepartmentRoleControllerGetRolesOfDepartmentGroupDefaultResponse,
    TError = unknown,
    TQueryKey extends unknown[] = unknown[],
  >(
    {
      departmentGroupId,
      limit,
      offset,
    }: {
      departmentGroupId: string;
      limit?: number;
      offset: number;
    },
    queryKey?: TQueryKey,
    options?: Omit<
      UseQueryOptions<TData, TError>,
      'queryKey' | 'queryFn' | 'initialData'
    >,
  ) =>
    useQuery<TData, TError>({
      queryFn: () =>
        DepartmentRolesService.departmentRoleControllerGetRolesOfDepartmentGroup(
          departmentGroupId,
          limit,
          offset,
        ) as TData,
      queryKey: [
        useDepartmentRolesServiceDepartmentRoleControllerGetRolesOfDepartmentGroupKey,
        ...(queryKey ?? [{ departmentGroupId, limit, offset }]),
      ],
      ...options,
    });

export type DepartmentRolesServicedepartmentRoleControllerCreateDepartmentRoleMutationResult =
  Awaited<
    ReturnType<
      typeof DepartmentRolesService.departmentRoleControllerCreateDepartmentRole
    >
  >;

/**
 * Create a department role
 */
export const useDepartmentRolesServiceDepartmentRoleControllerCreateDepartmentRole =
  <
    TData = DepartmentRolesServicedepartmentRoleControllerCreateDepartmentRoleMutationResult,
    TError = unknown,
    TContext = unknown,
  >(
    options?: Omit<
      UseMutationOptions<
        TData,
        TError,
        {
          departmentGroupId: string;
          requestBody: CreateDepartmentRoleDto;
        },
        TContext
      >,
      'mutationFn'
    >,
  ) =>
    useMutation<
      TData,
      TError,
      {
        departmentGroupId: string;
        requestBody: CreateDepartmentRoleDto;
      },
      TContext
    >({
      mutationFn: ({ departmentGroupId, requestBody }) =>
        DepartmentRolesService.departmentRoleControllerCreateDepartmentRole(
          departmentGroupId,
          requestBody,
        ) as unknown as Promise<TData>,
      ...options,
    });

export type DepartmentRolesServicedepartmentRoleControllerUpdateDepartmentRoleMutationResult =
  Awaited<
    ReturnType<
      typeof DepartmentRolesService.departmentRoleControllerUpdateDepartmentRole
    >
  >;

/**
 * Update a department role
 */
export const useDepartmentRolesServiceDepartmentRoleControllerUpdateDepartmentRole =
  <
    TData = DepartmentRolesServicedepartmentRoleControllerUpdateDepartmentRoleMutationResult,
    TError = unknown,
    TContext = unknown,
  >(
    options?: Omit<
      UseMutationOptions<
        TData,
        TError,
        {
          id: string;
          departmentGroupId: string;
          requestBody: UpdateDepartmentRoleDto;
        },
        TContext
      >,
      'mutationFn'
    >,
  ) =>
    useMutation<
      TData,
      TError,
      {
        id: string;
        departmentGroupId: string;
        requestBody: UpdateDepartmentRoleDto;
      },
      TContext
    >({
      mutationFn: ({ id, departmentGroupId, requestBody }) =>
        DepartmentRolesService.departmentRoleControllerUpdateDepartmentRole(
          id,
          departmentGroupId,
          requestBody,
        ) as unknown as Promise<TData>,
      ...options,
    });

export type DepartmentRolesServicedepartmentRoleControllerDeleteDepartmentRoleMutationResult =
  Awaited<
    ReturnType<
      typeof DepartmentRolesService.departmentRoleControllerDeleteDepartmentRole
    >
  >;

/**
 * Delete a department role
 */
export const useDepartmentRolesServiceDepartmentRoleControllerDeleteDepartmentRole =
  <
    TData = DepartmentRolesServicedepartmentRoleControllerDeleteDepartmentRoleMutationResult,
    TError = unknown,
    TContext = unknown,
  >(
    options?: Omit<
      UseMutationOptions<
        TData,
        TError,
        {
          id: string;
          departmentGroupId: string;
        },
        TContext
      >,
      'mutationFn'
    >,
  ) =>
    useMutation<
      TData,
      TError,
      {
        id: string;
        departmentGroupId: string;
      },
      TContext
    >({
      mutationFn: ({ id, departmentGroupId }) =>
        DepartmentRolesService.departmentRoleControllerDeleteDepartmentRole(
          id,
          departmentGroupId,
        ) as unknown as Promise<TData>,
      ...options,
    });

export type DepartmentGroupsServiceDepartmentGroupControllerGetGroupsOfDepartmentDefaultResponse =
  Awaited<
    ReturnType<
      typeof DepartmentGroupsService.departmentGroupControllerGetGroupsOfDepartment
    >
  >;

export type DepartmentGroupsServiceDepartmentGroupControllerGetGroupsOfDepartmentQueryResult<
  TData = DepartmentGroupsServiceDepartmentGroupControllerGetGroupsOfDepartmentDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useDepartmentGroupsServiceDepartmentGroupControllerGetGroupsOfDepartmentKey =
  'DepartmentGroupsServiceDepartmentGroupControllerGetGroupsOfDepartment';

/**
 * Get list of group of a department
 */
export const useDepartmentGroupsServiceDepartmentGroupControllerGetGroupsOfDepartment =
  <
    TData = DepartmentGroupsServiceDepartmentGroupControllerGetGroupsOfDepartmentDefaultResponse,
    TError = unknown,
    TQueryKey extends unknown[] = unknown[],
  >(
    {
      departmentId,
      limit,
      offset,
    }: {
      departmentId: string;
      limit?: number;
      offset: number;
    },
    queryKey?: TQueryKey,
    options?: Omit<
      UseQueryOptions<TData, TError>,
      'queryKey' | 'queryFn' | 'initialData'
    >,
  ) =>
    useQuery<TData, TError>({
      queryFn: () =>
        DepartmentGroupsService.departmentGroupControllerGetGroupsOfDepartment(
          departmentId,
          limit,
          offset,
        ) as TData,
      queryKey: [
        useDepartmentGroupsServiceDepartmentGroupControllerGetGroupsOfDepartmentKey,
        ...(queryKey ?? [{ departmentId, limit, offset }]),
      ],
      ...options,
    });

export type DepartmentGroupsServicedepartmentGroupControllerCreateDepartmentGroupMutationResult =
  Awaited<
    ReturnType<
      typeof DepartmentGroupsService.departmentGroupControllerCreateDepartmentGroup
    >
  >;

/**
 * Create a department group
 */
export const useDepartmentGroupsServiceDepartmentGroupControllerCreateDepartmentGroup =
  <
    TData = DepartmentGroupsServicedepartmentGroupControllerCreateDepartmentGroupMutationResult,
    TError = unknown,
    TContext = unknown,
  >(
    options?: Omit<
      UseMutationOptions<
        TData,
        TError,
        {
          departmentId: string;
          requestBody: CreateDepartmentGroupDto;
        },
        TContext
      >,
      'mutationFn'
    >,
  ) =>
    useMutation<
      TData,
      TError,
      {
        departmentId: string;
        requestBody: CreateDepartmentGroupDto;
      },
      TContext
    >({
      mutationFn: ({ departmentId, requestBody }) =>
        DepartmentGroupsService.departmentGroupControllerCreateDepartmentGroup(
          departmentId,
          requestBody,
        ) as unknown as Promise<TData>,
      ...options,
    });

export type DepartmentGroupsServicedepartmentGroupControllerUpdateDepartmentGroupMutationResult =
  Awaited<
    ReturnType<
      typeof DepartmentGroupsService.departmentGroupControllerUpdateDepartmentGroup
    >
  >;

/**
 * Update a department group
 */
export const useDepartmentGroupsServiceDepartmentGroupControllerUpdateDepartmentGroup =
  <
    TData = DepartmentGroupsServicedepartmentGroupControllerUpdateDepartmentGroupMutationResult,
    TError = unknown,
    TContext = unknown,
  >(
    options?: Omit<
      UseMutationOptions<
        TData,
        TError,
        {
          id: string;
          departmentId: string;
          requestBody: UpdateDepartmentGroupDto;
        },
        TContext
      >,
      'mutationFn'
    >,
  ) =>
    useMutation<
      TData,
      TError,
      {
        id: string;
        departmentId: string;
        requestBody: UpdateDepartmentGroupDto;
      },
      TContext
    >({
      mutationFn: ({ id, departmentId, requestBody }) =>
        DepartmentGroupsService.departmentGroupControllerUpdateDepartmentGroup(
          id,
          departmentId,
          requestBody,
        ) as unknown as Promise<TData>,
      ...options,
    });

export type DepartmentGroupsServicedepartmentGroupControllerDeleteDepartmentGroupMutationResult =
  Awaited<
    ReturnType<
      typeof DepartmentGroupsService.departmentGroupControllerDeleteDepartmentGroup
    >
  >;

/**
 * Delete a department group
 */
export const useDepartmentGroupsServiceDepartmentGroupControllerDeleteDepartmentGroup =
  <
    TData = DepartmentGroupsServicedepartmentGroupControllerDeleteDepartmentGroupMutationResult,
    TError = unknown,
    TContext = unknown,
  >(
    options?: Omit<
      UseMutationOptions<
        TData,
        TError,
        {
          id: string;
          departmentId: string;
        },
        TContext
      >,
      'mutationFn'
    >,
  ) =>
    useMutation<
      TData,
      TError,
      {
        id: string;
        departmentId: string;
      },
      TContext
    >({
      mutationFn: ({ id, departmentId }) =>
        DepartmentGroupsService.departmentGroupControllerDeleteDepartmentGroup(
          id,
          departmentId,
        ) as unknown as Promise<TData>,
      ...options,
    });

export type DefaultServicemigrationControllerHandleMutationResult = Awaited<
  ReturnType<typeof DefaultService.migrationControllerHandle>
>;

export const useDefaultServiceMigrationControllerHandle = <
  TData = DefaultServicemigrationControllerHandleMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<TData, TError, void, TContext>,
    'mutationFn'
  >,
) =>
  useMutation<TData, TError, void, TContext>({
    mutationFn: () =>
      DefaultService.migrationControllerHandle() as unknown as Promise<TData>,
    ...options,
  });

export type DashboardServiceDashBoardControllerGetOverallDashBoardDefaultResponse =
  Awaited<
    ReturnType<typeof DashboardService.dashBoardControllerGetOverallDashBoard>
  >;

export type DashboardServiceDashBoardControllerGetOverallDashBoardQueryResult<
  TData = DashboardServiceDashBoardControllerGetOverallDashBoardDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useDashboardServiceDashBoardControllerGetOverallDashBoardKey =
  'DashboardServiceDashBoardControllerGetOverallDashBoard';

/**
 * Summary the overall dashboard data
 */
export const useDashboardServiceDashBoardControllerGetOverallDashBoard = <
  TData = DashboardServiceDashBoardControllerGetOverallDashBoardDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    startTime,
    endTime,
    bookingType,
    zone,
    filterType,
  }: {
    startTime: string;
    endTime: string;
    bookingType?: 'PERSONAL' | 'BUSINESS' | null;
    zone?: string | null;
    filterType?:
      | 'LAST_1_HOUR'
      | 'LAST_6_HOURS'
      | 'TODAY'
      | 'YESTERDAY'
      | 'LAST_4_WEEKS'
      | 'LAST_3_MONTHS'
      | 'LAST_12_MONTHS'
      | 'ALL_TIME'
      | null;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      DashboardService.dashBoardControllerGetOverallDashBoard(
        startTime,
        endTime,
        bookingType,
        zone,
        filterType,
      ) as TData,
    queryKey: [
      useDashboardServiceDashBoardControllerGetOverallDashBoardKey,
      ...(queryKey ?? [{ bookingType, endTime, filterType, startTime, zone }]),
    ],
    ...options,
  });

export type DashboardServiceDashBoardControllerGetOverallDashBoardOfBusinessDefaultResponse =
  Awaited<
    ReturnType<
      typeof DashboardService.dashBoardControllerGetOverallDashBoardOfBusiness
    >
  >;

export type DashboardServiceDashBoardControllerGetOverallDashBoardOfBusinessQueryResult<
  TData = DashboardServiceDashBoardControllerGetOverallDashBoardOfBusinessDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useDashboardServiceDashBoardControllerGetOverallDashBoardOfBusinessKey =
  'DashboardServiceDashBoardControllerGetOverallDashBoardOfBusiness';

/**
 * Summary the overall dashboard data of business
 */
export const useDashboardServiceDashBoardControllerGetOverallDashBoardOfBusiness =
  <
    TData = DashboardServiceDashBoardControllerGetOverallDashBoardOfBusinessDefaultResponse,
    TError = unknown,
    TQueryKey extends unknown[] = unknown[],
  >(
    {
      startTime,
      endTime,
      businessId,
    }: {
      startTime: string;
      endTime: string;
      businessId: string;
    },
    queryKey?: TQueryKey,
    options?: Omit<
      UseQueryOptions<TData, TError>,
      'queryKey' | 'queryFn' | 'initialData'
    >,
  ) =>
    useQuery<TData, TError>({
      queryFn: () =>
        DashboardService.dashBoardControllerGetOverallDashBoardOfBusiness(
          startTime,
          endTime,
          businessId,
        ) as TData,
      queryKey: [
        useDashboardServiceDashBoardControllerGetOverallDashBoardOfBusinessKey,
        ...(queryKey ?? [{ businessId, endTime, startTime }]),
      ],
      ...options,
    });

export type DashboardServiceDashBoardControllerGetTopBusinessDefaultResponse =
  Awaited<
    ReturnType<typeof DashboardService.dashBoardControllerGetTopBusiness>
  >;

export type DashboardServiceDashBoardControllerGetTopBusinessQueryResult<
  TData = DashboardServiceDashBoardControllerGetTopBusinessDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useDashboardServiceDashBoardControllerGetTopBusinessKey =
  'DashboardServiceDashBoardControllerGetTopBusiness';

/**
 * Get top business
 */
export const useDashboardServiceDashBoardControllerGetTopBusiness = <
  TData = DashboardServiceDashBoardControllerGetTopBusinessDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    startTime,
    endTime,
    limit,
  }: {
    startTime: string;
    endTime: string;
    limit?: number;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      DashboardService.dashBoardControllerGetTopBusiness(
        startTime,
        endTime,
        limit,
      ) as TData,
    queryKey: [
      useDashboardServiceDashBoardControllerGetTopBusinessKey,
      ...(queryKey ?? [{ endTime, limit, startTime }]),
    ],
    ...options,
  });

export type DashboardServiceDashBoardControllerGetTopUsersDefaultResponse =
  Awaited<ReturnType<typeof DashboardService.dashBoardControllerGetTopUsers>>;

export type DashboardServiceDashBoardControllerGetTopUsersQueryResult<
  TData = DashboardServiceDashBoardControllerGetTopUsersDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useDashboardServiceDashBoardControllerGetTopUsersKey =
  'DashboardServiceDashBoardControllerGetTopUsers';

/**
 * Get top users
 */
export const useDashboardServiceDashBoardControllerGetTopUsers = <
  TData = DashboardServiceDashBoardControllerGetTopUsersDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    startTime,
    endTime,
    limit,
  }: {
    startTime: string;
    endTime: string;
    limit?: number;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      DashboardService.dashBoardControllerGetTopUsers(
        startTime,
        endTime,
        limit,
      ) as TData,
    queryKey: [
      useDashboardServiceDashBoardControllerGetTopUsersKey,
      ...(queryKey ?? [{ endTime, limit, startTime }]),
    ],
    ...options,
  });

export type DashboardServiceDashBoardControllerGetLastBookingsDefaultResponse =
  Awaited<
    ReturnType<typeof DashboardService.dashBoardControllerGetLastBookings>
  >;

export type DashboardServiceDashBoardControllerGetLastBookingsQueryResult<
  TData = DashboardServiceDashBoardControllerGetLastBookingsDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useDashboardServiceDashBoardControllerGetLastBookingsKey =
  'DashboardServiceDashBoardControllerGetLastBookings';

/**
 * Get the last bookings
 */
export const useDashboardServiceDashBoardControllerGetLastBookings = <
  TData = DashboardServiceDashBoardControllerGetLastBookingsDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    startTime,
    endTime,
    bookingType,
    limit,
  }: {
    startTime: string;
    endTime: string;
    bookingType: any;
    limit?: number;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      DashboardService.dashBoardControllerGetLastBookings(
        startTime,
        endTime,
        bookingType,
        limit,
      ) as TData,
    queryKey: [
      useDashboardServiceDashBoardControllerGetLastBookingsKey,
      ...(queryKey ?? [{ bookingType, endTime, limit, startTime }]),
    ],
    ...options,
  });

export type DashboardServiceDashBoardControllerGetReportBookingChartDefaultResponse =
  Awaited<
    ReturnType<typeof DashboardService.dashBoardControllerGetReportBookingChart>
  >;

export type DashboardServiceDashBoardControllerGetReportBookingChartQueryResult<
  TData = DashboardServiceDashBoardControllerGetReportBookingChartDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useDashboardServiceDashBoardControllerGetReportBookingChartKey =
  'DashboardServiceDashBoardControllerGetReportBookingChart';

/**
 * Get the booking charts
 */
export const useDashboardServiceDashBoardControllerGetReportBookingChart = <
  TData = DashboardServiceDashBoardControllerGetReportBookingChartDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    startTime,
    endTime,
    bookingType,
    zone,
    filterType,
  }: {
    startTime: string;
    endTime: string;
    bookingType?: 'PERSONAL' | 'BUSINESS' | null;
    zone?: string | null;
    filterType?:
      | 'LAST_1_HOUR'
      | 'LAST_6_HOURS'
      | 'TODAY'
      | 'YESTERDAY'
      | 'LAST_4_WEEKS'
      | 'LAST_3_MONTHS'
      | 'LAST_12_MONTHS'
      | 'ALL_TIME'
      | null;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      DashboardService.dashBoardControllerGetReportBookingChart(
        startTime,
        endTime,
        bookingType,
        zone,
        filterType,
      ) as TData,
    queryKey: [
      useDashboardServiceDashBoardControllerGetReportBookingChartKey,
      ...(queryKey ?? [{ bookingType, endTime, filterType, startTime, zone }]),
    ],
    ...options,
  });

export type DashboardServiceDashBoardControllerGetReportBookingRevenueChartDefaultResponse =
  Awaited<
    ReturnType<
      typeof DashboardService.dashBoardControllerGetReportBookingRevenueChart
    >
  >;

export type DashboardServiceDashBoardControllerGetReportBookingRevenueChartQueryResult<
  TData = DashboardServiceDashBoardControllerGetReportBookingRevenueChartDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useDashboardServiceDashBoardControllerGetReportBookingRevenueChartKey =
  'DashboardServiceDashBoardControllerGetReportBookingRevenueChart';

/**
 * Get the revenue charts
 */
export const useDashboardServiceDashBoardControllerGetReportBookingRevenueChart =
  <
    TData = DashboardServiceDashBoardControllerGetReportBookingRevenueChartDefaultResponse,
    TError = unknown,
    TQueryKey extends unknown[] = unknown[],
  >(
    {
      startTime,
      endTime,
      bookingType,
      zone,
      filterType,
    }: {
      startTime: string;
      endTime: string;
      bookingType?: 'PERSONAL' | 'BUSINESS' | null;
      zone?: string | null;
      filterType?:
        | 'LAST_1_HOUR'
        | 'LAST_6_HOURS'
        | 'TODAY'
        | 'YESTERDAY'
        | 'LAST_4_WEEKS'
        | 'LAST_3_MONTHS'
        | 'LAST_12_MONTHS'
        | 'ALL_TIME'
        | null;
    },
    queryKey?: TQueryKey,
    options?: Omit<
      UseQueryOptions<TData, TError>,
      'queryKey' | 'queryFn' | 'initialData'
    >,
  ) =>
    useQuery<TData, TError>({
      queryFn: () =>
        DashboardService.dashBoardControllerGetReportBookingRevenueChart(
          startTime,
          endTime,
          bookingType,
          zone,
          filterType,
        ) as TData,
      queryKey: [
        useDashboardServiceDashBoardControllerGetReportBookingRevenueChartKey,
        ...(queryKey ?? [
          { bookingType, endTime, filterType, startTime, zone },
        ]),
      ],
      ...options,
    });

export type DashboardServiceDashBoardControllerGetReportBookingSourceChartDefaultResponse =
  Awaited<
    ReturnType<
      typeof DashboardService.dashBoardControllerGetReportBookingSourceChart
    >
  >;

export type DashboardServiceDashBoardControllerGetReportBookingSourceChartQueryResult<
  TData = DashboardServiceDashBoardControllerGetReportBookingSourceChartDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useDashboardServiceDashBoardControllerGetReportBookingSourceChartKey =
  'DashboardServiceDashBoardControllerGetReportBookingSourceChart';

/**
 * Get the booking source charts
 */
export const useDashboardServiceDashBoardControllerGetReportBookingSourceChart =
  <
    TData = DashboardServiceDashBoardControllerGetReportBookingSourceChartDefaultResponse,
    TError = unknown,
    TQueryKey extends unknown[] = unknown[],
  >(
    {
      startTime,
      endTime,
      filterType,
    }: {
      startTime: string;
      endTime: string;
      filterType?:
        | 'LAST_1_HOUR'
        | 'LAST_6_HOURS'
        | 'TODAY'
        | 'YESTERDAY'
        | 'LAST_4_WEEKS'
        | 'LAST_3_MONTHS'
        | 'LAST_12_MONTHS'
        | 'ALL_TIME'
        | null;
    },
    queryKey?: TQueryKey,
    options?: Omit<
      UseQueryOptions<TData, TError>,
      'queryKey' | 'queryFn' | 'initialData'
    >,
  ) =>
    useQuery<TData, TError>({
      queryFn: () =>
        DashboardService.dashBoardControllerGetReportBookingSourceChart(
          startTime,
          endTime,
          filterType,
        ) as TData,
      queryKey: [
        useDashboardServiceDashBoardControllerGetReportBookingSourceChartKey,
        ...(queryKey ?? [{ endTime, filterType, startTime }]),
      ],
      ...options,
    });

export type DashboardServiceDashBoardControllerGetTrackingBookingSourceChartDefaultResponse =
  Awaited<
    ReturnType<
      typeof DashboardService.dashBoardControllerGetTrackingBookingSourceChart
    >
  >;

export type DashboardServiceDashBoardControllerGetTrackingBookingSourceChartQueryResult<
  TData = DashboardServiceDashBoardControllerGetTrackingBookingSourceChartDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useDashboardServiceDashBoardControllerGetTrackingBookingSourceChartKey =
  'DashboardServiceDashBoardControllerGetTrackingBookingSourceChart';

/**
 * Get the tracking booking source charts
 */
export const useDashboardServiceDashBoardControllerGetTrackingBookingSourceChart =
  <
    TData = DashboardServiceDashBoardControllerGetTrackingBookingSourceChartDefaultResponse,
    TError = unknown,
    TQueryKey extends unknown[] = unknown[],
  >(
    {
      startTime,
      endTime,
      filterType,
    }: {
      startTime: string;
      endTime: string;
      filterType?:
        | 'LAST_1_HOUR'
        | 'LAST_6_HOURS'
        | 'TODAY'
        | 'YESTERDAY'
        | 'LAST_4_WEEKS'
        | 'LAST_3_MONTHS'
        | 'LAST_12_MONTHS'
        | 'ALL_TIME'
        | null;
    },
    queryKey?: TQueryKey,
    options?: Omit<
      UseQueryOptions<TData, TError>,
      'queryKey' | 'queryFn' | 'initialData'
    >,
  ) =>
    useQuery<TData, TError>({
      queryFn: () =>
        DashboardService.dashBoardControllerGetTrackingBookingSourceChart(
          startTime,
          endTime,
          filterType,
        ) as TData,
      queryKey: [
        useDashboardServiceDashBoardControllerGetTrackingBookingSourceChartKey,
        ...(queryKey ?? [{ endTime, filterType, startTime }]),
      ],
      ...options,
    });

export type CouponsServiceCouponControllerGetListDefaultResponse = Awaited<
  ReturnType<typeof CouponsService.couponControllerGetList>
>;

export type CouponsServiceCouponControllerGetListQueryResult<
  TData = CouponsServiceCouponControllerGetListDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useCouponsServiceCouponControllerGetListKey =
  'CouponsServiceCouponControllerGetList';

/**
 * Get list of coupon
 */
export const useCouponsServiceCouponControllerGetList = <
  TData = CouponsServiceCouponControllerGetListDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    limit,
    offset,
    q,
    title,
    redeemMethod,
    status,
    createdFrom,
    createdTo,
    startDateValidFrom,
    endDateValidFrom,
    startDateValidTo,
    endDateValidTo,
    order,
  }: {
    limit?: number;
    offset: number;
    q?: string | null;
    title?: string | null;
    redeemMethod?: string | null;
    status?: string | null;
    createdFrom?: string | null;
    createdTo?: string | null;
    startDateValidFrom?: string | null;
    endDateValidFrom?: string | null;
    startDateValidTo?: string | null;
    endDateValidTo?: string | null;
    order?: string | null;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      CouponsService.couponControllerGetList(
        limit,
        offset,
        q,
        title,
        redeemMethod,
        status,
        createdFrom,
        createdTo,
        startDateValidFrom,
        endDateValidFrom,
        startDateValidTo,
        endDateValidTo,
        order,
      ) as TData,
    queryKey: [
      useCouponsServiceCouponControllerGetListKey,
      ...(queryKey ?? [
        {
          createdFrom,
          createdTo,
          endDateValidFrom,
          endDateValidTo,
          limit,
          offset,
          order,
          q,
          redeemMethod,
          startDateValidFrom,
          startDateValidTo,
          status,
          title,
        },
      ]),
    ],
    ...options,
  });

export type CouponsServicecouponControllerCreateOneMutationResult = Awaited<
  ReturnType<typeof CouponsService.couponControllerCreateOne>
>;

/**
 * Create a coupon
 */
export const useCouponsServiceCouponControllerCreateOne = <
  TData = CouponsServicecouponControllerCreateOneMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: CreateCouponDto;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: CreateCouponDto;
    },
    TContext
  >({
    mutationFn: ({ requestBody }) =>
      CouponsService.couponControllerCreateOne(
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type CouponsServiceCouponControllerGetCouponStoreListDefaultResponse =
  Awaited<ReturnType<typeof CouponsService.couponControllerGetCouponStoreList>>;

export type CouponsServiceCouponControllerGetCouponStoreListQueryResult<
  TData = CouponsServiceCouponControllerGetCouponStoreListDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useCouponsServiceCouponControllerGetCouponStoreListKey =
  'CouponsServiceCouponControllerGetCouponStoreList';

/**
 * Get list of coupon store
 */
export const useCouponsServiceCouponControllerGetCouponStoreList = <
  TData = CouponsServiceCouponControllerGetCouponStoreListDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () => CouponsService.couponControllerGetCouponStoreList() as TData,
    queryKey: [
      useCouponsServiceCouponControllerGetCouponStoreListKey,
      ...(queryKey ?? []),
    ],
    ...options,
  });

export type CouponsServiceCouponControllerCheckCouponDefaultResponse = Awaited<
  ReturnType<typeof CouponsService.couponControllerCheckCoupon>
>;

export type CouponsServiceCouponControllerCheckCouponQueryResult<
  TData = CouponsServiceCouponControllerCheckCouponDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useCouponsServiceCouponControllerCheckCouponKey =
  'CouponsServiceCouponControllerCheckCoupon';

/**
 * Check a coupon is existed by code
 */
export const useCouponsServiceCouponControllerCheckCoupon = <
  TData = CouponsServiceCouponControllerCheckCouponDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    code,
  }: {
    code: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () => CouponsService.couponControllerCheckCoupon(code) as TData,
    queryKey: [
      useCouponsServiceCouponControllerCheckCouponKey,
      ...(queryKey ?? [{ code }]),
    ],
    ...options,
  });

export type CouponsServicecouponControllerUpdateCouponMutationResult = Awaited<
  ReturnType<typeof CouponsService.couponControllerUpdateCoupon>
>;

/**
 * Update a coupon
 */
export const useCouponsServiceCouponControllerUpdateCoupon = <
  TData = CouponsServicecouponControllerUpdateCouponMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        id: string;
        requestBody: UpdateCouponDto;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      id: string;
      requestBody: UpdateCouponDto;
    },
    TContext
  >({
    mutationFn: ({ id, requestBody }) =>
      CouponsService.couponControllerUpdateCoupon(
        id,
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type CouponsServicecouponControllerDeleteCouponMutationResult = Awaited<
  ReturnType<typeof CouponsService.couponControllerDeleteCoupon>
>;

/**
 * Delete a coupon
 */
export const useCouponsServiceCouponControllerDeleteCoupon = <
  TData = CouponsServicecouponControllerDeleteCouponMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        id: string;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      id: string;
    },
    TContext
  >({
    mutationFn: ({ id }) =>
      CouponsService.couponControllerDeleteCoupon(
        id,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type CouponsServicecouponControllerUpdateStatusOfCouponMutationResult =
  Awaited<
    ReturnType<typeof CouponsService.couponControllerUpdateStatusOfCoupon>
  >;

/**
 * Update status of coupon
 */
export const useCouponsServiceCouponControllerUpdateStatusOfCoupon = <
  TData = CouponsServicecouponControllerUpdateStatusOfCouponMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        id: string;
        requestBody: UpdateCouponStatusDto;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      id: string;
      requestBody: UpdateCouponStatusDto;
    },
    TContext
  >({
    mutationFn: ({ id, requestBody }) =>
      CouponsService.couponControllerUpdateStatusOfCoupon(
        id,
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type CouponsServiceCouponControllerGetCouponDetailDefaultResponse =
  Awaited<ReturnType<typeof CouponsService.couponControllerGetCouponDetail>>;

export type CouponsServiceCouponControllerGetCouponDetailQueryResult<
  TData = CouponsServiceCouponControllerGetCouponDetailDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useCouponsServiceCouponControllerGetCouponDetailKey =
  'CouponsServiceCouponControllerGetCouponDetail';

/**
 * Get coupon detail
 */
export const useCouponsServiceCouponControllerGetCouponDetail = <
  TData = CouponsServiceCouponControllerGetCouponDetailDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    id,
  }: {
    id: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () => CouponsService.couponControllerGetCouponDetail(id) as TData,
    queryKey: [
      useCouponsServiceCouponControllerGetCouponDetailKey,
      ...(queryKey ?? [{ id }]),
    ],
    ...options,
  });

export type CouponsServiceCouponControllerGetCouponStatisticDefaultResponse =
  Awaited<ReturnType<typeof CouponsService.couponControllerGetCouponStatistic>>;

export type CouponsServiceCouponControllerGetCouponStatisticQueryResult<
  TData = CouponsServiceCouponControllerGetCouponStatisticDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useCouponsServiceCouponControllerGetCouponStatisticKey =
  'CouponsServiceCouponControllerGetCouponStatistic';

/**
 * Get coupon statistics
 */
export const useCouponsServiceCouponControllerGetCouponStatistic = <
  TData = CouponsServiceCouponControllerGetCouponStatisticDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    id,
  }: {
    id: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      CouponsService.couponControllerGetCouponStatistic(id) as TData,
    queryKey: [
      useCouponsServiceCouponControllerGetCouponStatisticKey,
      ...(queryKey ?? [{ id }]),
    ],
    ...options,
  });

export type CouponsServiceCouponControllerGetCouponLogHistoriesDefaultResponse =
  Awaited<
    ReturnType<typeof CouponsService.couponControllerGetCouponLogHistories>
  >;

export type CouponsServiceCouponControllerGetCouponLogHistoriesQueryResult<
  TData = CouponsServiceCouponControllerGetCouponLogHistoriesDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useCouponsServiceCouponControllerGetCouponLogHistoriesKey =
  'CouponsServiceCouponControllerGetCouponLogHistories';

/**
 * Get coupon logs histories
 */
export const useCouponsServiceCouponControllerGetCouponLogHistories = <
  TData = CouponsServiceCouponControllerGetCouponLogHistoriesDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    id,
    limit,
    offset,
    fromDate,
    toDate,
    redeemFrom,
    redeemTo,
    q,
    order,
  }: {
    id: string;
    limit?: number;
    offset: number;
    fromDate?: string;
    toDate?: string;
    redeemFrom?: string;
    redeemTo?: string;
    q?: string | null;
    order?: string | null;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      CouponsService.couponControllerGetCouponLogHistories(
        id,
        limit,
        offset,
        fromDate,
        toDate,
        redeemFrom,
        redeemTo,
        q,
        order,
      ) as TData,
    queryKey: [
      useCouponsServiceCouponControllerGetCouponLogHistoriesKey,
      ...(queryKey ?? [
        { fromDate, id, limit, offset, order, q, redeemFrom, redeemTo, toDate },
      ]),
    ],
    ...options,
  });

export type CouponsServiceCouponControllerGetCouponLogActivitiesDefaultResponse =
  Awaited<
    ReturnType<typeof CouponsService.couponControllerGetCouponLogActivities>
  >;

export type CouponsServiceCouponControllerGetCouponLogActivitiesQueryResult<
  TData = CouponsServiceCouponControllerGetCouponLogActivitiesDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useCouponsServiceCouponControllerGetCouponLogActivitiesKey =
  'CouponsServiceCouponControllerGetCouponLogActivities';

/**
 * Get coupon logs activities
 */
export const useCouponsServiceCouponControllerGetCouponLogActivities = <
  TData = CouponsServiceCouponControllerGetCouponLogActivitiesDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    id,
    limit,
    offset,
    order,
  }: {
    id: string;
    limit?: number;
    offset: number;
    order?: string | null;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      CouponsService.couponControllerGetCouponLogActivities(
        id,
        limit,
        offset,
        order,
      ) as TData,
    queryKey: [
      useCouponsServiceCouponControllerGetCouponLogActivitiesKey,
      ...(queryKey ?? [{ id, limit, offset, order }]),
    ],
    ...options,
  });

export type ConfigsServiceAppConfigControllerGetAppConfigsDefaultResponse =
  Awaited<ReturnType<typeof ConfigsService.appConfigControllerGetAppConfigs>>;

export type ConfigsServiceAppConfigControllerGetAppConfigsQueryResult<
  TData = ConfigsServiceAppConfigControllerGetAppConfigsDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useConfigsServiceAppConfigControllerGetAppConfigsKey =
  'ConfigsServiceAppConfigControllerGetAppConfigs';

/**
 * Get app configs
 */
export const useConfigsServiceAppConfigControllerGetAppConfigs = <
  TData = ConfigsServiceAppConfigControllerGetAppConfigsDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () => ConfigsService.appConfigControllerGetAppConfigs() as TData,
    queryKey: [
      useConfigsServiceAppConfigControllerGetAppConfigsKey,
      ...(queryKey ?? []),
    ],
    ...options,
  });

export type ConfigsServiceAppConfigControllerGetBlogsDefaultResponse = Awaited<
  ReturnType<typeof ConfigsService.appConfigControllerGetBlogs>
>;

export type ConfigsServiceAppConfigControllerGetBlogsQueryResult<
  TData = ConfigsServiceAppConfigControllerGetBlogsDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useConfigsServiceAppConfigControllerGetBlogsKey =
  'ConfigsServiceAppConfigControllerGetBlogs';

/**
 * Get list blogs
 */
export const useConfigsServiceAppConfigControllerGetBlogs = <
  TData = ConfigsServiceAppConfigControllerGetBlogsDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () => ConfigsService.appConfigControllerGetBlogs() as TData,
    queryKey: [
      useConfigsServiceAppConfigControllerGetBlogsKey,
      ...(queryKey ?? []),
    ],
    ...options,
  });

export type ConfigsServiceAppConfigControllerMigrateDataDefaultResponse =
  Awaited<ReturnType<typeof ConfigsService.appConfigControllerMigrateData>>;

export type ConfigsServiceAppConfigControllerMigrateDataQueryResult<
  TData = ConfigsServiceAppConfigControllerMigrateDataDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useConfigsServiceAppConfigControllerMigrateDataKey =
  'ConfigsServiceAppConfigControllerMigrateData';

/**
 * Migrate data
 */
export const useConfigsServiceAppConfigControllerMigrateData = <
  TData = ConfigsServiceAppConfigControllerMigrateDataDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () => ConfigsService.appConfigControllerMigrateData() as TData,
    queryKey: [
      useConfigsServiceAppConfigControllerMigrateDataKey,
      ...(queryKey ?? []),
    ],
    ...options,
  });

export type CodesServiceCodeControllerGetCodesOfBusinessDefaultResponse =
  Awaited<ReturnType<typeof CodesService.codeControllerGetCodesOfBusiness>>;

export type CodesServiceCodeControllerGetCodesOfBusinessQueryResult<
  TData = CodesServiceCodeControllerGetCodesOfBusinessDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useCodesServiceCodeControllerGetCodesOfBusinessKey =
  'CodesServiceCodeControllerGetCodesOfBusiness';

/**
 * Get list of codes of a business
 */
export const useCodesServiceCodeControllerGetCodesOfBusiness = <
  TData = CodesServiceCodeControllerGetCodesOfBusinessDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    businessId,
    limit,
    offset,
    code,
    order,
  }: {
    businessId: string;
    limit?: number;
    offset: number;
    code?: string | null;
    order?: string | null;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      CodesService.codeControllerGetCodesOfBusiness(
        businessId,
        limit,
        offset,
        code,
        order,
      ) as TData,
    queryKey: [
      useCodesServiceCodeControllerGetCodesOfBusinessKey,
      ...(queryKey ?? [{ businessId, code, limit, offset, order }]),
    ],
    ...options,
  });

export type CodesServicecodeControllerCreateCodeMutationResult = Awaited<
  ReturnType<typeof CodesService.codeControllerCreateCode>
>;

/**
 * Create a code
 */
export const useCodesServiceCodeControllerCreateCode = <
  TData = CodesServicecodeControllerCreateCodeMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        businessId: string;
        requestBody: CreateCodeDto;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      businessId: string;
      requestBody: CreateCodeDto;
    },
    TContext
  >({
    mutationFn: ({ businessId, requestBody }) =>
      CodesService.codeControllerCreateCode(
        businessId,
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type CodesServiceCodeControllerGetAvailableCodesOfBusinessDefaultResponse =
  Awaited<
    ReturnType<typeof CodesService.codeControllerGetAvailableCodesOfBusiness>
  >;

export type CodesServiceCodeControllerGetAvailableCodesOfBusinessQueryResult<
  TData = CodesServiceCodeControllerGetAvailableCodesOfBusinessDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useCodesServiceCodeControllerGetAvailableCodesOfBusinessKey =
  'CodesServiceCodeControllerGetAvailableCodesOfBusiness';

/**
 * Get list of available codes of a business
 */
export const useCodesServiceCodeControllerGetAvailableCodesOfBusiness = <
  TData = CodesServiceCodeControllerGetAvailableCodesOfBusinessDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    businessId,
    limit,
    offset,
    code,
  }: {
    businessId: string;
    limit?: number;
    offset: number;
    code?: string | null;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      CodesService.codeControllerGetAvailableCodesOfBusiness(
        businessId,
        limit,
        offset,
        code,
      ) as TData,
    queryKey: [
      useCodesServiceCodeControllerGetAvailableCodesOfBusinessKey,
      ...(queryKey ?? [{ businessId, code, limit, offset }]),
    ],
    ...options,
  });

export type CodesServicecodeControllerUpdateStatusOfCodeMutationResult =
  Awaited<ReturnType<typeof CodesService.codeControllerUpdateStatusOfCode>>;

/**
 * Update status of code
 */
export const useCodesServiceCodeControllerUpdateStatusOfCode = <
  TData = CodesServicecodeControllerUpdateStatusOfCodeMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        businessId: string;
        id: string;
        requestBody: UpdateCodeStatusDto;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      businessId: string;
      id: string;
      requestBody: UpdateCodeStatusDto;
    },
    TContext
  >({
    mutationFn: ({ businessId, id, requestBody }) =>
      CodesService.codeControllerUpdateStatusOfCode(
        businessId,
        id,
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type CodesServiceCodeControllerGetCodeOfBusinessDefaultResponse =
  Awaited<ReturnType<typeof CodesService.codeControllerGetCodeOfBusiness>>;

export type CodesServiceCodeControllerGetCodeOfBusinessQueryResult<
  TData = CodesServiceCodeControllerGetCodeOfBusinessDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useCodesServiceCodeControllerGetCodeOfBusinessKey =
  'CodesServiceCodeControllerGetCodeOfBusiness';

/**
 * Get code detail of business
 */
export const useCodesServiceCodeControllerGetCodeOfBusiness = <
  TData = CodesServiceCodeControllerGetCodeOfBusinessDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    businessId,
    id,
  }: {
    businessId: string;
    id: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      CodesService.codeControllerGetCodeOfBusiness(businessId, id) as TData,
    queryKey: [
      useCodesServiceCodeControllerGetCodeOfBusinessKey,
      ...(queryKey ?? [{ businessId, id }]),
    ],
    ...options,
  });

export type CodesServicecodeControllerDeleteCodeOfBusinessMutationResult =
  Awaited<ReturnType<typeof CodesService.codeControllerDeleteCodeOfBusiness>>;

/**
 * Delete a code of business
 */
export const useCodesServiceCodeControllerDeleteCodeOfBusiness = <
  TData = CodesServicecodeControllerDeleteCodeOfBusinessMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        businessId: string;
        id: string;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      businessId: string;
      id: string;
    },
    TContext
  >({
    mutationFn: ({ businessId, id }) =>
      CodesService.codeControllerDeleteCodeOfBusiness(
        businessId,
        id,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type BusinessesServiceBusinessControllerGetBusinessDefaultResponse =
  Awaited<ReturnType<typeof BusinessesService.businessControllerGetBusiness>>;

export type BusinessesServiceBusinessControllerGetBusinessQueryResult<
  TData = BusinessesServiceBusinessControllerGetBusinessDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useBusinessesServiceBusinessControllerGetBusinessKey =
  'BusinessesServiceBusinessControllerGetBusiness';

/**
 * Get a business by id
 */
export const useBusinessesServiceBusinessControllerGetBusiness = <
  TData = BusinessesServiceBusinessControllerGetBusinessDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    businessId,
  }: {
    businessId: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      BusinessesService.businessControllerGetBusiness(businessId) as TData,
    queryKey: [
      useBusinessesServiceBusinessControllerGetBusinessKey,
      ...(queryKey ?? [{ businessId }]),
    ],
    ...options,
  });

export type BusinessesServicebusinessControllerUpdateBusinessMutationResult =
  Awaited<
    ReturnType<typeof BusinessesService.businessControllerUpdateBusiness>
  >;

/**
 * Update a business
 */
export const useBusinessesServiceBusinessControllerUpdateBusiness = <
  TData = BusinessesServicebusinessControllerUpdateBusinessMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        businessId: string;
        requestBody: UpdateBusinessDto;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      businessId: string;
      requestBody: UpdateBusinessDto;
    },
    TContext
  >({
    mutationFn: ({ businessId, requestBody }) =>
      BusinessesService.businessControllerUpdateBusiness(
        businessId,
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type BusinessesServicebusinessSuperAdminControllerDeleteBusinessMutationResult =
  Awaited<
    ReturnType<
      typeof BusinessesService.businessSuperAdminControllerDeleteBusiness
    >
  >;

/**
 * Delete a business
 */
export const useBusinessesServiceBusinessSuperAdminControllerDeleteBusiness = <
  TData = BusinessesServicebusinessSuperAdminControllerDeleteBusinessMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        businessId: string;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      businessId: string;
    },
    TContext
  >({
    mutationFn: ({ businessId }) =>
      BusinessesService.businessSuperAdminControllerDeleteBusiness(
        businessId,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type BusinessesServiceBusinessControllerGetBillToOfBusinessDefaultResponse =
  Awaited<
    ReturnType<typeof BusinessesService.businessControllerGetBillToOfBusiness>
  >;

export type BusinessesServiceBusinessControllerGetBillToOfBusinessQueryResult<
  TData = BusinessesServiceBusinessControllerGetBillToOfBusinessDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useBusinessesServiceBusinessControllerGetBillToOfBusinessKey =
  'BusinessesServiceBusinessControllerGetBillToOfBusiness';

/**
 * Get bill-tos list of business
 */
export const useBusinessesServiceBusinessControllerGetBillToOfBusiness = <
  TData = BusinessesServiceBusinessControllerGetBillToOfBusinessDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    businessId,
  }: {
    businessId: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      BusinessesService.businessControllerGetBillToOfBusiness(
        businessId,
      ) as TData,
    queryKey: [
      useBusinessesServiceBusinessControllerGetBillToOfBusinessKey,
      ...(queryKey ?? [{ businessId }]),
    ],
    ...options,
  });

export type BusinessesServicebusinessControllerCreateBillToMutationResult =
  Awaited<ReturnType<typeof BusinessesService.businessControllerCreateBillTo>>;

/**
 * Create a bill-to
 */
export const useBusinessesServiceBusinessControllerCreateBillTo = <
  TData = BusinessesServicebusinessControllerCreateBillToMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        businessId: string;
        requestBody: CreateBusinessBillToDto;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      businessId: string;
      requestBody: CreateBusinessBillToDto;
    },
    TContext
  >({
    mutationFn: ({ businessId, requestBody }) =>
      BusinessesService.businessControllerCreateBillTo(
        businessId,
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type BusinessesServiceBusinessSuperAdminControllerGetBusinessesDefaultResponse =
  Awaited<
    ReturnType<
      typeof BusinessesService.businessSuperAdminControllerGetBusinesses
    >
  >;

export type BusinessesServiceBusinessSuperAdminControllerGetBusinessesQueryResult<
  TData = BusinessesServiceBusinessSuperAdminControllerGetBusinessesDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useBusinessesServiceBusinessSuperAdminControllerGetBusinessesKey =
  'BusinessesServiceBusinessSuperAdminControllerGetBusinesses';

/**
 * Get list of business
 */
export const useBusinessesServiceBusinessSuperAdminControllerGetBusinesses = <
  TData = BusinessesServiceBusinessSuperAdminControllerGetBusinessesDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    limit,
    offset,
    q,
    order,
  }: {
    limit?: number;
    offset: number;
    q?: string | null;
    order?: string | null;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      BusinessesService.businessSuperAdminControllerGetBusinesses(
        limit,
        offset,
        q,
        order,
      ) as TData,
    queryKey: [
      useBusinessesServiceBusinessSuperAdminControllerGetBusinessesKey,
      ...(queryKey ?? [{ limit, offset, order, q }]),
    ],
    ...options,
  });

export type BusinessesServicebusinessSuperAdminControllerCreateDepartmentGroupMutationResult =
  Awaited<
    ReturnType<
      typeof BusinessesService.businessSuperAdminControllerCreateDepartmentGroup
    >
  >;

/**
 * Create a business
 */
export const useBusinessesServiceBusinessSuperAdminControllerCreateDepartmentGroup =
  <
    TData = BusinessesServicebusinessSuperAdminControllerCreateDepartmentGroupMutationResult,
    TError = unknown,
    TContext = unknown,
  >(
    options?: Omit<
      UseMutationOptions<
        TData,
        TError,
        {
          requestBody: CreateBusinessDto;
        },
        TContext
      >,
      'mutationFn'
    >,
  ) =>
    useMutation<
      TData,
      TError,
      {
        requestBody: CreateBusinessDto;
      },
      TContext
    >({
      mutationFn: ({ requestBody }) =>
        BusinessesService.businessSuperAdminControllerCreateDepartmentGroup(
          requestBody,
        ) as unknown as Promise<TData>,
      ...options,
    });

export type BusinessesServiceBusinessSuperAdminControllerGetBusinessesFilterDefaultResponse =
  Awaited<
    ReturnType<
      typeof BusinessesService.businessSuperAdminControllerGetBusinessesFilter
    >
  >;

export type BusinessesServiceBusinessSuperAdminControllerGetBusinessesFilterQueryResult<
  TData = BusinessesServiceBusinessSuperAdminControllerGetBusinessesFilterDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useBusinessesServiceBusinessSuperAdminControllerGetBusinessesFilterKey =
  'BusinessesServiceBusinessSuperAdminControllerGetBusinessesFilter';

/**
 * Get list filter of business
 */
export const useBusinessesServiceBusinessSuperAdminControllerGetBusinessesFilter =
  <
    TData = BusinessesServiceBusinessSuperAdminControllerGetBusinessesFilterDefaultResponse,
    TError = unknown,
    TQueryKey extends unknown[] = unknown[],
  >(
    {
      limit,
      offset,
      q,
      order,
    }: {
      limit?: number;
      offset: number;
      q?: string | null;
      order?: string | null;
    },
    queryKey?: TQueryKey,
    options?: Omit<
      UseQueryOptions<TData, TError>,
      'queryKey' | 'queryFn' | 'initialData'
    >,
  ) =>
    useQuery<TData, TError>({
      queryFn: () =>
        BusinessesService.businessSuperAdminControllerGetBusinessesFilter(
          limit,
          offset,
          q,
          order,
        ) as TData,
      queryKey: [
        useBusinessesServiceBusinessSuperAdminControllerGetBusinessesFilterKey,
        ...(queryKey ?? [{ limit, offset, order, q }]),
      ],
      ...options,
    });

export type BusinessesServicebusinessSuperAdminControllerUpdateBusinessStatusMutationResult =
  Awaited<
    ReturnType<
      typeof BusinessesService.businessSuperAdminControllerUpdateBusinessStatus
    >
  >;

/**
 * Update status of a business
 */
export const useBusinessesServiceBusinessSuperAdminControllerUpdateBusinessStatus =
  <
    TData = BusinessesServicebusinessSuperAdminControllerUpdateBusinessStatusMutationResult,
    TError = unknown,
    TContext = unknown,
  >(
    options?: Omit<
      UseMutationOptions<
        TData,
        TError,
        {
          businessId: string;
          requestBody: UpdateBusinessStatusDto;
        },
        TContext
      >,
      'mutationFn'
    >,
  ) =>
    useMutation<
      TData,
      TError,
      {
        businessId: string;
        requestBody: UpdateBusinessStatusDto;
      },
      TContext
    >({
      mutationFn: ({ businessId, requestBody }) =>
        BusinessesService.businessSuperAdminControllerUpdateBusinessStatus(
          businessId,
          requestBody,
        ) as unknown as Promise<TData>,
      ...options,
    });

export type BusinessesServiceBusinessSuperAdminControllerExportBusinessCustomersDefaultResponse =
  Awaited<
    ReturnType<
      typeof BusinessesService.businessSuperAdminControllerExportBusinessCustomers
    >
  >;

export type BusinessesServiceBusinessSuperAdminControllerExportBusinessCustomersQueryResult<
  TData = BusinessesServiceBusinessSuperAdminControllerExportBusinessCustomersDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useBusinessesServiceBusinessSuperAdminControllerExportBusinessCustomersKey =
  'BusinessesServiceBusinessSuperAdminControllerExportBusinessCustomers';

/**
 * Export business customers
 */
export const useBusinessesServiceBusinessSuperAdminControllerExportBusinessCustomers =
  <
    TData = BusinessesServiceBusinessSuperAdminControllerExportBusinessCustomersDefaultResponse,
    TError = unknown,
    TQueryKey extends unknown[] = unknown[],
  >(
    {
      q,
      order,
    }: {
      q?: string | null;
      order?: string | null;
    },
    queryKey?: TQueryKey,
    options?: Omit<
      UseQueryOptions<TData, TError>,
      'queryKey' | 'queryFn' | 'initialData'
    >,
  ) =>
    useQuery<TData, TError>({
      queryFn: () =>
        BusinessesService.businessSuperAdminControllerExportBusinessCustomers(
          q,
          order,
        ) as TData,
      queryKey: [
        useBusinessesServiceBusinessSuperAdminControllerExportBusinessCustomersKey,
        ...(queryKey ?? [{ order, q }]),
      ],
      ...options,
    });

export type BookingsServicebookingControllerCreateEstimationMutationResult =
  Awaited<ReturnType<typeof BookingsService.bookingControllerCreateEstimation>>;

/**
 * Create a estimation for booking
 */
export const useBookingsServiceBookingControllerCreateEstimation = <
  TData = BookingsServicebookingControllerCreateEstimationMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: CreateEstimationDto;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: CreateEstimationDto;
    },
    TContext
  >({
    mutationFn: ({ requestBody }) =>
      BookingsService.bookingControllerCreateEstimation(
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type BookingsServicebookingControllerCreateBookingWithCodeMutationResult =
  Awaited<
    ReturnType<typeof BookingsService.bookingControllerCreateBookingWithCode>
  >;

/**
 * Create a booking with an employee of the business
 */
export const useBookingsServiceBookingControllerCreateBookingWithCode = <
  TData = BookingsServicebookingControllerCreateBookingWithCodeMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: CreateBookingWithCodeDto;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: CreateBookingWithCodeDto;
    },
    TContext
  >({
    mutationFn: ({ requestBody }) =>
      BookingsService.bookingControllerCreateBookingWithCode(
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type BookingsServicebookingControllerCreateBookingWithPaymentMutationResult =
  Awaited<
    ReturnType<typeof BookingsService.bookingControllerCreateBookingWithPayment>
  >;

/**
 * Create a personal booking with payment
 */
export const useBookingsServiceBookingControllerCreateBookingWithPayment = <
  TData = BookingsServicebookingControllerCreateBookingWithPaymentMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: CreateBookingWithPaymentDto;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: CreateBookingWithPaymentDto;
    },
    TContext
  >({
    mutationFn: ({ requestBody }) =>
      BookingsService.bookingControllerCreateBookingWithPayment(
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type BookingsServiceBookingControllerGetNearbyVehiclesDefaultResponse =
  Awaited<
    ReturnType<typeof BookingsService.bookingControllerGetNearbyVehicles>
  >;

export type BookingsServiceBookingControllerGetNearbyVehiclesQueryResult<
  TData = BookingsServiceBookingControllerGetNearbyVehiclesDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useBookingsServiceBookingControllerGetNearbyVehiclesKey =
  'BookingsServiceBookingControllerGetNearbyVehicles';

/**
 * Get nearby vehicles
 */
export const useBookingsServiceBookingControllerGetNearbyVehicles = <
  TData = BookingsServiceBookingControllerGetNearbyVehiclesDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    latitude,
    longitude,
  }: {
    latitude: number;
    longitude: number;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      BookingsService.bookingControllerGetNearbyVehicles(
        latitude,
        longitude,
      ) as TData,
    queryKey: [
      useBookingsServiceBookingControllerGetNearbyVehiclesKey,
      ...(queryKey ?? [{ latitude, longitude }]),
    ],
    ...options,
  });

export type BookingsServiceBookingControllerGetBookingDefaultResponse = Awaited<
  ReturnType<typeof BookingsService.bookingControllerGetBooking>
>;

export type BookingsServiceBookingControllerGetBookingQueryResult<
  TData = BookingsServiceBookingControllerGetBookingDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useBookingsServiceBookingControllerGetBookingKey =
  'BookingsServiceBookingControllerGetBooking';

/**
 * Get a booking
 */
export const useBookingsServiceBookingControllerGetBooking = <
  TData = BookingsServiceBookingControllerGetBookingDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    id,
  }: {
    id: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () => BookingsService.bookingControllerGetBooking(id) as TData,
    queryKey: [
      useBookingsServiceBookingControllerGetBookingKey,
      ...(queryKey ?? [{ id }]),
    ],
    ...options,
  });

export type BookingsServiceBookingControllerGetBookingPublicDefaultResponse =
  Awaited<ReturnType<typeof BookingsService.bookingControllerGetBookingPublic>>;

export type BookingsServiceBookingControllerGetBookingPublicQueryResult<
  TData = BookingsServiceBookingControllerGetBookingPublicDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useBookingsServiceBookingControllerGetBookingPublicKey =
  'BookingsServiceBookingControllerGetBookingPublic';

/**
 * Get a public booking
 */
export const useBookingsServiceBookingControllerGetBookingPublic = <
  TData = BookingsServiceBookingControllerGetBookingPublicDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    id,
  }: {
    id: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      BookingsService.bookingControllerGetBookingPublic(id) as TData,
    queryKey: [
      useBookingsServiceBookingControllerGetBookingPublicKey,
      ...(queryKey ?? [{ id }]),
    ],
    ...options,
  });

export type BookingsServiceBookingControllerGetCurrentLocationDefaultResponse =
  Awaited<
    ReturnType<typeof BookingsService.bookingControllerGetCurrentLocation>
  >;

export type BookingsServiceBookingControllerGetCurrentLocationQueryResult<
  TData = BookingsServiceBookingControllerGetCurrentLocationDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useBookingsServiceBookingControllerGetCurrentLocationKey =
  'BookingsServiceBookingControllerGetCurrentLocation';

/**
 * Get current location of a booking
 */
export const useBookingsServiceBookingControllerGetCurrentLocation = <
  TData = BookingsServiceBookingControllerGetCurrentLocationDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    id,
  }: {
    id: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      BookingsService.bookingControllerGetCurrentLocation(id) as TData,
    queryKey: [
      useBookingsServiceBookingControllerGetCurrentLocationKey,
      ...(queryKey ?? [{ id }]),
    ],
    ...options,
  });

export type BookingsServicebookingControllerReviewBookingMutationResult =
  Awaited<ReturnType<typeof BookingsService.bookingControllerReviewBooking>>;

/**
 * Review a booking
 */
export const useBookingsServiceBookingControllerReviewBooking = <
  TData = BookingsServicebookingControllerReviewBookingMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        id: string;
        requestBody: CreateReviewDto;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      id: string;
      requestBody: CreateReviewDto;
    },
    TContext
  >({
    mutationFn: ({ id, requestBody }) =>
      BookingsService.bookingControllerReviewBooking(
        id,
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type BookingsServicebookingControllerCancelBookingMutationResult =
  Awaited<ReturnType<typeof BookingsService.bookingControllerCancelBooking>>;

/**
 * Cancel a booking
 */
export const useBookingsServiceBookingControllerCancelBooking = <
  TData = BookingsServicebookingControllerCancelBookingMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        id: string;
        requestBody: CancelBookingDto;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      id: string;
      requestBody: CancelBookingDto;
    },
    TContext
  >({
    mutationFn: ({ id, requestBody }) =>
      BookingsService.bookingControllerCancelBooking(
        id,
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type BookingsServiceBookingControllerGetBookingOfMeDefaultResponse =
  Awaited<ReturnType<typeof BookingsService.bookingControllerGetBookingOfMe>>;

export type BookingsServiceBookingControllerGetBookingOfMeQueryResult<
  TData = BookingsServiceBookingControllerGetBookingOfMeDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useBookingsServiceBookingControllerGetBookingOfMeKey =
  'BookingsServiceBookingControllerGetBookingOfMe';

/**
 * Get list of booking of me
 */
export const useBookingsServiceBookingControllerGetBookingOfMe = <
  TData = BookingsServiceBookingControllerGetBookingOfMeDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    limit,
    offset,
    fromDate,
    toDate,
    q,
    status,
    statuses,
    filterType,
  }: {
    limit?: number;
    offset: number;
    fromDate?: string | null;
    toDate?: string | null;
    q?: string | null;
    status?:
      | 'DRAFT'
      | 'SCHEDULED'
      | 'DEMAND_CREATION'
      | 'TO_BE_DISTRIBUTED'
      | 'CONFIRMED_ADDRESS'
      | 'AWAITING_CONFIRMED_VEHICLE'
      | 'VEHICLE_CONFIRMED'
      | 'ARRIVAL_AT_CLIENT'
      | 'CLIENT_ON_BOARD'
      | 'AMOUNT_MONEY_RECEIVED'
      | 'COMPLETED'
      | 'NO_LOAD'
      | 'CANCELLATION_REQUEST'
      | 'CANCELLED'
      | null;
    statuses?: string;
    filterType?: 'CURRENT_BOOKING' | 'COMPLETED' | 'CANCELLED' | null;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      BookingsService.bookingControllerGetBookingOfMe(
        limit,
        offset,
        fromDate,
        toDate,
        q,
        status,
        statuses,
        filterType,
      ) as TData,
    queryKey: [
      useBookingsServiceBookingControllerGetBookingOfMeKey,
      ...(queryKey ?? [
        { filterType, fromDate, limit, offset, q, status, statuses, toDate },
      ]),
    ],
    ...options,
  });

export type BookingsServiceBookingControllerGetCurrentBookingDefaultResponse =
  Awaited<
    ReturnType<typeof BookingsService.bookingControllerGetCurrentBooking>
  >;

export type BookingsServiceBookingControllerGetCurrentBookingQueryResult<
  TData = BookingsServiceBookingControllerGetCurrentBookingDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useBookingsServiceBookingControllerGetCurrentBookingKey =
  'BookingsServiceBookingControllerGetCurrentBooking';

/**
 * Get current booking of a user
 */
export const useBookingsServiceBookingControllerGetCurrentBooking = <
  TData = BookingsServiceBookingControllerGetCurrentBookingDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      BookingsService.bookingControllerGetCurrentBooking() as TData,
    queryKey: [
      useBookingsServiceBookingControllerGetCurrentBookingKey,
      ...(queryKey ?? []),
    ],
    ...options,
  });

export type BookingsServiceBookingControllerGetBookingOfBusinessDefaultResponse =
  Awaited<
    ReturnType<typeof BookingsService.bookingControllerGetBookingOfBusiness>
  >;

export type BookingsServiceBookingControllerGetBookingOfBusinessQueryResult<
  TData = BookingsServiceBookingControllerGetBookingOfBusinessDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useBookingsServiceBookingControllerGetBookingOfBusinessKey =
  'BookingsServiceBookingControllerGetBookingOfBusiness';

/**
 * Get list of booking of a business
 */
export const useBookingsServiceBookingControllerGetBookingOfBusiness = <
  TData = BookingsServiceBookingControllerGetBookingOfBusinessDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    businessId,
    limit,
    offset,
    fromDate,
    toDate,
    q,
    status,
    departmentId,
    order,
  }: {
    businessId: string;
    limit?: number;
    offset: number;
    fromDate?: string | null;
    toDate?: string | null;
    q?: string | null;
    status?: string | null;
    departmentId?: string | null;
    order?: string | null;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      BookingsService.bookingControllerGetBookingOfBusiness(
        businessId,
        limit,
        offset,
        fromDate,
        toDate,
        q,
        status,
        departmentId,
        order,
      ) as TData,
    queryKey: [
      useBookingsServiceBookingControllerGetBookingOfBusinessKey,
      ...(queryKey ?? [
        {
          businessId,
          departmentId,
          fromDate,
          limit,
          offset,
          order,
          q,
          status,
          toDate,
        },
      ]),
    ],
    ...options,
  });

export type BookingsServiceBookingControllerGetBookingsOfCodeDefaultResponse =
  Awaited<
    ReturnType<typeof BookingsService.bookingControllerGetBookingsOfCode>
  >;

export type BookingsServiceBookingControllerGetBookingsOfCodeQueryResult<
  TData = BookingsServiceBookingControllerGetBookingsOfCodeDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useBookingsServiceBookingControllerGetBookingsOfCodeKey =
  'BookingsServiceBookingControllerGetBookingsOfCode';

/**
 * Get list of bookings of a code
 */
export const useBookingsServiceBookingControllerGetBookingsOfCode = <
  TData = BookingsServiceBookingControllerGetBookingsOfCodeDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    id,
    limit,
    offset,
    fromDate,
    toDate,
    q,
    status,
    order,
  }: {
    id: string;
    limit?: number;
    offset: number;
    fromDate?: string | null;
    toDate?: string | null;
    q?: string | null;
    status?: string | null;
    order?: string | null;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      BookingsService.bookingControllerGetBookingsOfCode(
        id,
        limit,
        offset,
        fromDate,
        toDate,
        q,
        status,
        order,
      ) as TData,
    queryKey: [
      useBookingsServiceBookingControllerGetBookingsOfCodeKey,
      ...(queryKey ?? [
        { fromDate, id, limit, offset, order, q, status, toDate },
      ]),
    ],
    ...options,
  });

export type BookingsServicebookingControllerCheckWorkingAreaMutationResult =
  Awaited<ReturnType<typeof BookingsService.bookingControllerCheckWorkingArea>>;

/**
 * Check working areas
 */
export const useBookingsServiceBookingControllerCheckWorkingArea = <
  TData = BookingsServicebookingControllerCheckWorkingAreaMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: CheckWorkingAreaDto;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: CheckWorkingAreaDto;
    },
    TContext
  >({
    mutationFn: ({ requestBody }) =>
      BookingsService.bookingControllerCheckWorkingArea(
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type BookingsServiceBookingControllerGetBookingReceiptDefaultResponse =
  Awaited<
    ReturnType<typeof BookingsService.bookingControllerGetBookingReceipt>
  >;

export type BookingsServiceBookingControllerGetBookingReceiptQueryResult<
  TData = BookingsServiceBookingControllerGetBookingReceiptDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useBookingsServiceBookingControllerGetBookingReceiptKey =
  'BookingsServiceBookingControllerGetBookingReceipt';

/**
 * Get receipt file of a booking
 */
export const useBookingsServiceBookingControllerGetBookingReceipt = <
  TData = BookingsServiceBookingControllerGetBookingReceiptDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    id,
  }: {
    id: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      BookingsService.bookingControllerGetBookingReceipt(id) as TData,
    queryKey: [
      useBookingsServiceBookingControllerGetBookingReceiptKey,
      ...(queryKey ?? [{ id }]),
    ],
    ...options,
  });

export type BookingsServiceBookingControllerGetBookingReceiptAdminDefaultResponse =
  Awaited<
    ReturnType<typeof BookingsService.bookingControllerGetBookingReceiptAdmin>
  >;

export type BookingsServiceBookingControllerGetBookingReceiptAdminQueryResult<
  TData = BookingsServiceBookingControllerGetBookingReceiptAdminDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useBookingsServiceBookingControllerGetBookingReceiptAdminKey =
  'BookingsServiceBookingControllerGetBookingReceiptAdmin';

/**
 * Get admin receipt file of a booking
 */
export const useBookingsServiceBookingControllerGetBookingReceiptAdmin = <
  TData = BookingsServiceBookingControllerGetBookingReceiptAdminDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    id,
  }: {
    id: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      BookingsService.bookingControllerGetBookingReceiptAdmin(id) as TData,
    queryKey: [
      useBookingsServiceBookingControllerGetBookingReceiptAdminKey,
      ...(queryKey ?? [{ id }]),
    ],
    ...options,
  });

export type BookingsServiceBookingSuperAdminControllerGetBookingOfUserDefaultResponse =
  Awaited<
    ReturnType<
      typeof BookingsService.bookingSuperAdminControllerGetBookingOfUser
    >
  >;

export type BookingsServiceBookingSuperAdminControllerGetBookingOfUserQueryResult<
  TData = BookingsServiceBookingSuperAdminControllerGetBookingOfUserDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useBookingsServiceBookingSuperAdminControllerGetBookingOfUserKey =
  'BookingsServiceBookingSuperAdminControllerGetBookingOfUser';

/**
 * Get list of booking of a user
 */
export const useBookingsServiceBookingSuperAdminControllerGetBookingOfUser = <
  TData = BookingsServiceBookingSuperAdminControllerGetBookingOfUserDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    id,
    limit,
    offset,
    fromDate,
    toDate,
    q,
    status,
    statuses,
    filterType,
    order,
  }: {
    id: string;
    limit?: number;
    offset: number;
    fromDate?: string | null;
    toDate?: string | null;
    q?: string | null;
    status?:
      | 'DRAFT'
      | 'SCHEDULED'
      | 'DEMAND_CREATION'
      | 'TO_BE_DISTRIBUTED'
      | 'CONFIRMED_ADDRESS'
      | 'AWAITING_CONFIRMED_VEHICLE'
      | 'VEHICLE_CONFIRMED'
      | 'ARRIVAL_AT_CLIENT'
      | 'CLIENT_ON_BOARD'
      | 'AMOUNT_MONEY_RECEIVED'
      | 'COMPLETED'
      | 'NO_LOAD'
      | 'CANCELLATION_REQUEST'
      | 'CANCELLED'
      | null;
    statuses?: string;
    filterType?: 'CURRENT_BOOKING' | 'COMPLETED' | 'CANCELLED' | null;
    order?: string | null;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      BookingsService.bookingSuperAdminControllerGetBookingOfUser(
        id,
        limit,
        offset,
        fromDate,
        toDate,
        q,
        status,
        statuses,
        filterType,
        order,
      ) as TData,
    queryKey: [
      useBookingsServiceBookingSuperAdminControllerGetBookingOfUserKey,
      ...(queryKey ?? [
        {
          filterType,
          fromDate,
          id,
          limit,
          offset,
          order,
          q,
          status,
          statuses,
          toDate,
        },
      ]),
    ],
    ...options,
  });

export type BookingsServiceBookingSuperAdminControllerGetStatisticsOfUserDefaultResponse =
  Awaited<
    ReturnType<
      typeof BookingsService.bookingSuperAdminControllerGetStatisticsOfUser
    >
  >;

export type BookingsServiceBookingSuperAdminControllerGetStatisticsOfUserQueryResult<
  TData = BookingsServiceBookingSuperAdminControllerGetStatisticsOfUserDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useBookingsServiceBookingSuperAdminControllerGetStatisticsOfUserKey =
  'BookingsServiceBookingSuperAdminControllerGetStatisticsOfUser';

/**
 * Get statistics of a user
 */
export const useBookingsServiceBookingSuperAdminControllerGetStatisticsOfUser =
  <
    TData = BookingsServiceBookingSuperAdminControllerGetStatisticsOfUserDefaultResponse,
    TError = unknown,
    TQueryKey extends unknown[] = unknown[],
  >(
    {
      id,
      fromDate,
      toDate,
    }: {
      id: string;
      fromDate: string;
      toDate: string;
    },
    queryKey?: TQueryKey,
    options?: Omit<
      UseQueryOptions<TData, TError>,
      'queryKey' | 'queryFn' | 'initialData'
    >,
  ) =>
    useQuery<TData, TError>({
      queryFn: () =>
        BookingsService.bookingSuperAdminControllerGetStatisticsOfUser(
          id,
          fromDate,
          toDate,
        ) as TData,
      queryKey: [
        useBookingsServiceBookingSuperAdminControllerGetStatisticsOfUserKey,
        ...(queryKey ?? [{ fromDate, id, toDate }]),
      ],
      ...options,
    });

export type BookingsServiceBookingSuperAdminControllerGetStatisticsOfBusinessDefaultResponse =
  Awaited<
    ReturnType<
      typeof BookingsService.bookingSuperAdminControllerGetStatisticsOfBusiness
    >
  >;

export type BookingsServiceBookingSuperAdminControllerGetStatisticsOfBusinessQueryResult<
  TData = BookingsServiceBookingSuperAdminControllerGetStatisticsOfBusinessDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useBookingsServiceBookingSuperAdminControllerGetStatisticsOfBusinessKey =
  'BookingsServiceBookingSuperAdminControllerGetStatisticsOfBusiness';

/**
 * Get statistics of a business
 */
export const useBookingsServiceBookingSuperAdminControllerGetStatisticsOfBusiness =
  <
    TData = BookingsServiceBookingSuperAdminControllerGetStatisticsOfBusinessDefaultResponse,
    TError = unknown,
    TQueryKey extends unknown[] = unknown[],
  >(
    {
      businessId,
      fromDate,
      toDate,
    }: {
      businessId: string;
      fromDate: string;
      toDate: string;
    },
    queryKey?: TQueryKey,
    options?: Omit<
      UseQueryOptions<TData, TError>,
      'queryKey' | 'queryFn' | 'initialData'
    >,
  ) =>
    useQuery<TData, TError>({
      queryFn: () =>
        BookingsService.bookingSuperAdminControllerGetStatisticsOfBusiness(
          businessId,
          fromDate,
          toDate,
        ) as TData,
      queryKey: [
        useBookingsServiceBookingSuperAdminControllerGetStatisticsOfBusinessKey,
        ...(queryKey ?? [{ businessId, fromDate, toDate }]),
      ],
      ...options,
    });

export type BookingsServiceBookingSuperAdminControllerGetBookingsDefaultResponse =
  Awaited<
    ReturnType<typeof BookingsService.bookingSuperAdminControllerGetBookings>
  >;

export type BookingsServiceBookingSuperAdminControllerGetBookingsQueryResult<
  TData = BookingsServiceBookingSuperAdminControllerGetBookingsDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useBookingsServiceBookingSuperAdminControllerGetBookingsKey =
  'BookingsServiceBookingSuperAdminControllerGetBookings';

/**
 * Get list of bookings
 */
export const useBookingsServiceBookingSuperAdminControllerGetBookings = <
  TData = BookingsServiceBookingSuperAdminControllerGetBookingsDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    limit,
    offset,
    fromDate,
    toDate,
    q,
    type,
    businessIds,
    statuses,
    vehicleTypes,
    paymentMethodType,
    refundTag,
    reasons,
    zones,
    vehicleIds,
    order,
  }: {
    limit?: number;
    offset: number;
    fromDate?: string | null;
    toDate?: string | null;
    q?: string | null;
    type?: string | null;
    businessIds?: string;
    statuses?: string;
    vehicleTypes?: string;
    paymentMethodType?: any;
    refundTag?: any;
    reasons?: string;
    zones?: string;
    vehicleIds?: string;
    order?: string | null;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      BookingsService.bookingSuperAdminControllerGetBookings(
        limit,
        offset,
        fromDate,
        toDate,
        q,
        type,
        businessIds,
        statuses,
        vehicleTypes,
        paymentMethodType,
        refundTag,
        reasons,
        zones,
        vehicleIds,
        order,
      ) as TData,
    queryKey: [
      useBookingsServiceBookingSuperAdminControllerGetBookingsKey,
      ...(queryKey ?? [
        {
          businessIds,
          fromDate,
          limit,
          offset,
          order,
          paymentMethodType,
          q,
          reasons,
          refundTag,
          statuses,
          toDate,
          type,
          vehicleIds,
          vehicleTypes,
          zones,
        },
      ]),
    ],
    ...options,
  });

export type BookingsServiceBookingSuperAdminControllerGetBookingsFilterDefaultResponse =
  Awaited<
    ReturnType<
      typeof BookingsService.bookingSuperAdminControllerGetBookingsFilter
    >
  >;

export type BookingsServiceBookingSuperAdminControllerGetBookingsFilterQueryResult<
  TData = BookingsServiceBookingSuperAdminControllerGetBookingsFilterDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useBookingsServiceBookingSuperAdminControllerGetBookingsFilterKey =
  'BookingsServiceBookingSuperAdminControllerGetBookingsFilter';

/**
 * Get list of bookings
 */
export const useBookingsServiceBookingSuperAdminControllerGetBookingsFilter = <
  TData = BookingsServiceBookingSuperAdminControllerGetBookingsFilterDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    limit,
    offset,
    fromDate,
    toDate,
    q,
    type,
    businessIds,
    statuses,
    vehicleTypes,
    paymentMethodType,
    refundTag,
    reasons,
    zones,
    vehicleIds,
    order,
  }: {
    limit?: number;
    offset: number;
    fromDate?: string | null;
    toDate?: string | null;
    q?: string | null;
    type?: string | null;
    businessIds?: string;
    statuses?: string;
    vehicleTypes?: string;
    paymentMethodType?: any;
    refundTag?: any;
    reasons?: string;
    zones?: string;
    vehicleIds?: string;
    order?: string | null;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      BookingsService.bookingSuperAdminControllerGetBookingsFilter(
        limit,
        offset,
        fromDate,
        toDate,
        q,
        type,
        businessIds,
        statuses,
        vehicleTypes,
        paymentMethodType,
        refundTag,
        reasons,
        zones,
        vehicleIds,
        order,
      ) as TData,
    queryKey: [
      useBookingsServiceBookingSuperAdminControllerGetBookingsFilterKey,
      ...(queryKey ?? [
        {
          businessIds,
          fromDate,
          limit,
          offset,
          order,
          paymentMethodType,
          q,
          reasons,
          refundTag,
          statuses,
          toDate,
          type,
          vehicleIds,
          vehicleTypes,
          zones,
        },
      ]),
    ],
    ...options,
  });

export type BookingsServiceBookingSuperAdminControllerExportBookingsDefaultResponse =
  Awaited<
    ReturnType<typeof BookingsService.bookingSuperAdminControllerExportBookings>
  >;

export type BookingsServiceBookingSuperAdminControllerExportBookingsQueryResult<
  TData = BookingsServiceBookingSuperAdminControllerExportBookingsDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useBookingsServiceBookingSuperAdminControllerExportBookingsKey =
  'BookingsServiceBookingSuperAdminControllerExportBookings';

/**
 * Export booking history
 */
export const useBookingsServiceBookingSuperAdminControllerExportBookings = <
  TData = BookingsServiceBookingSuperAdminControllerExportBookingsDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    fromDate,
    toDate,
    q,
    type,
    businessIds,
    statuses,
    vehicleTypes,
    paymentMethodType,
    refundTag,
    reasons,
    zones,
    vehicleIds,
    order,
  }: {
    fromDate?: string | null;
    toDate?: string | null;
    q?: string | null;
    type?: string | null;
    businessIds?: string;
    statuses?: string;
    vehicleTypes?: string;
    paymentMethodType?: any;
    refundTag?: any;
    reasons?: string;
    zones?: string;
    vehicleIds?: string;
    order?: string | null;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      BookingsService.bookingSuperAdminControllerExportBookings(
        fromDate,
        toDate,
        q,
        type,
        businessIds,
        statuses,
        vehicleTypes,
        paymentMethodType,
        refundTag,
        reasons,
        zones,
        vehicleIds,
        order,
      ) as TData,
    queryKey: [
      useBookingsServiceBookingSuperAdminControllerExportBookingsKey,
      ...(queryKey ?? [
        {
          businessIds,
          fromDate,
          order,
          paymentMethodType,
          q,
          reasons,
          refundTag,
          statuses,
          toDate,
          type,
          vehicleIds,
          vehicleTypes,
          zones,
        },
      ]),
    ],
    ...options,
  });

export type BookingsServiceBookingSuperAdminControllerGetBookingOfEmployeeDefaultResponse =
  Awaited<
    ReturnType<
      typeof BookingsService.bookingSuperAdminControllerGetBookingOfEmployee
    >
  >;

export type BookingsServiceBookingSuperAdminControllerGetBookingOfEmployeeQueryResult<
  TData = BookingsServiceBookingSuperAdminControllerGetBookingOfEmployeeDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useBookingsServiceBookingSuperAdminControllerGetBookingOfEmployeeKey =
  'BookingsServiceBookingSuperAdminControllerGetBookingOfEmployee';

/**
 * Get list of booking of employee
 */
export const useBookingsServiceBookingSuperAdminControllerGetBookingOfEmployee =
  <
    TData = BookingsServiceBookingSuperAdminControllerGetBookingOfEmployeeDefaultResponse,
    TError = unknown,
    TQueryKey extends unknown[] = unknown[],
  >(
    {
      id,
      limit,
      offset,
      q,
      order,
    }: {
      id: string;
      limit?: number;
      offset: number;
      q?: string | null;
      order?: string | null;
    },
    queryKey?: TQueryKey,
    options?: Omit<
      UseQueryOptions<TData, TError>,
      'queryKey' | 'queryFn' | 'initialData'
    >,
  ) =>
    useQuery<TData, TError>({
      queryFn: () =>
        BookingsService.bookingSuperAdminControllerGetBookingOfEmployee(
          id,
          limit,
          offset,
          q,
          order,
        ) as TData,
      queryKey: [
        useBookingsServiceBookingSuperAdminControllerGetBookingOfEmployeeKey,
        ...(queryKey ?? [{ id, limit, offset, order, q }]),
      ],
      ...options,
    });

export type BookingsServiceBookingSuperAdminControllerGetTripsDefaultResponse =
  Awaited<
    ReturnType<typeof BookingsService.bookingSuperAdminControllerGetTrips>
  >;

export type BookingsServiceBookingSuperAdminControllerGetTripsQueryResult<
  TData = BookingsServiceBookingSuperAdminControllerGetTripsDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useBookingsServiceBookingSuperAdminControllerGetTripsKey =
  'BookingsServiceBookingSuperAdminControllerGetTrips';

/**
 * Get trips of a booking
 */
export const useBookingsServiceBookingSuperAdminControllerGetTrips = <
  TData = BookingsServiceBookingSuperAdminControllerGetTripsDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    id,
  }: {
    id: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      BookingsService.bookingSuperAdminControllerGetTrips(id) as TData,
    queryKey: [
      useBookingsServiceBookingSuperAdminControllerGetTripsKey,
      ...(queryKey ?? [{ id }]),
    ],
    ...options,
  });

export type BookingsServiceBookingSuperAdminControllerGetBookingProgressesDefaultResponse =
  Awaited<
    ReturnType<
      typeof BookingsService.bookingSuperAdminControllerGetBookingProgresses
    >
  >;

export type BookingsServiceBookingSuperAdminControllerGetBookingProgressesQueryResult<
  TData = BookingsServiceBookingSuperAdminControllerGetBookingProgressesDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useBookingsServiceBookingSuperAdminControllerGetBookingProgressesKey =
  'BookingsServiceBookingSuperAdminControllerGetBookingProgresses';

/**
 * Get progresses of a booking
 */
export const useBookingsServiceBookingSuperAdminControllerGetBookingProgresses =
  <
    TData = BookingsServiceBookingSuperAdminControllerGetBookingProgressesDefaultResponse,
    TError = unknown,
    TQueryKey extends unknown[] = unknown[],
  >(
    {
      id,
    }: {
      id: string;
    },
    queryKey?: TQueryKey,
    options?: Omit<
      UseQueryOptions<TData, TError>,
      'queryKey' | 'queryFn' | 'initialData'
    >,
  ) =>
    useQuery<TData, TError>({
      queryFn: () =>
        BookingsService.bookingSuperAdminControllerGetBookingProgresses(
          id,
        ) as TData,
      queryKey: [
        useBookingsServiceBookingSuperAdminControllerGetBookingProgressesKey,
        ...(queryKey ?? [{ id }]),
      ],
      ...options,
    });

export type BookingsServiceBookingSharingControllerGetBookingByEncryptedIdDefaultResponse =
  Awaited<
    ReturnType<
      typeof BookingsService.bookingSharingControllerGetBookingByEncryptedId
    >
  >;

export type BookingsServiceBookingSharingControllerGetBookingByEncryptedIdQueryResult<
  TData = BookingsServiceBookingSharingControllerGetBookingByEncryptedIdDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useBookingsServiceBookingSharingControllerGetBookingByEncryptedIdKey =
  'BookingsServiceBookingSharingControllerGetBookingByEncryptedId';

/**
 * Get a booking by encrypted ID
 */
export const useBookingsServiceBookingSharingControllerGetBookingByEncryptedId =
  <
    TData = BookingsServiceBookingSharingControllerGetBookingByEncryptedIdDefaultResponse,
    TError = unknown,
    TQueryKey extends unknown[] = unknown[],
  >(
    {
      encryptedId,
    }: {
      encryptedId: string;
    },
    queryKey?: TQueryKey,
    options?: Omit<
      UseQueryOptions<TData, TError>,
      'queryKey' | 'queryFn' | 'initialData'
    >,
  ) =>
    useQuery<TData, TError>({
      queryFn: () =>
        BookingsService.bookingSharingControllerGetBookingByEncryptedId(
          encryptedId,
        ) as TData,
      queryKey: [
        useBookingsServiceBookingSharingControllerGetBookingByEncryptedIdKey,
        ...(queryKey ?? [{ encryptedId }]),
      ],
      ...options,
    });

export type BookingsServiceBookingSharingControllerGetCurrentLocationByEncryptedIdDefaultResponse =
  Awaited<
    ReturnType<
      typeof BookingsService.bookingSharingControllerGetCurrentLocationByEncryptedId
    >
  >;

export type BookingsServiceBookingSharingControllerGetCurrentLocationByEncryptedIdQueryResult<
  TData = BookingsServiceBookingSharingControllerGetCurrentLocationByEncryptedIdDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useBookingsServiceBookingSharingControllerGetCurrentLocationByEncryptedIdKey =
  'BookingsServiceBookingSharingControllerGetCurrentLocationByEncryptedId';

/**
 * Get current location of a booking by encrypted ID
 */
export const useBookingsServiceBookingSharingControllerGetCurrentLocationByEncryptedId =
  <
    TData = BookingsServiceBookingSharingControllerGetCurrentLocationByEncryptedIdDefaultResponse,
    TError = unknown,
    TQueryKey extends unknown[] = unknown[],
  >(
    {
      encryptedId,
    }: {
      encryptedId: string;
    },
    queryKey?: TQueryKey,
    options?: Omit<
      UseQueryOptions<TData, TError>,
      'queryKey' | 'queryFn' | 'initialData'
    >,
  ) =>
    useQuery<TData, TError>({
      queryFn: () =>
        BookingsService.bookingSharingControllerGetCurrentLocationByEncryptedId(
          encryptedId,
        ) as TData,
      queryKey: [
        useBookingsServiceBookingSharingControllerGetCurrentLocationByEncryptedIdKey,
        ...(queryKey ?? [{ encryptedId }]),
      ],
      ...options,
    });

export type BookingConfigsServiceBookingConfigControllerGetBookingConfigDefaultResponse =
  Awaited<
    ReturnType<
      typeof BookingConfigsService.bookingConfigControllerGetBookingConfig
    >
  >;

export type BookingConfigsServiceBookingConfigControllerGetBookingConfigQueryResult<
  TData = BookingConfigsServiceBookingConfigControllerGetBookingConfigDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useBookingConfigsServiceBookingConfigControllerGetBookingConfigKey =
  'BookingConfigsServiceBookingConfigControllerGetBookingConfig';

/**
 * Get booking config
 */
export const useBookingConfigsServiceBookingConfigControllerGetBookingConfig = <
  TData = BookingConfigsServiceBookingConfigControllerGetBookingConfigDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      BookingConfigsService.bookingConfigControllerGetBookingConfig() as TData,
    queryKey: [
      useBookingConfigsServiceBookingConfigControllerGetBookingConfigKey,
      ...(queryKey ?? []),
    ],
    ...options,
  });

export type BookingConfigsServicebookingConfigControllerUpdateBookingConfigMutationResult =
  Awaited<
    ReturnType<
      typeof BookingConfigsService.bookingConfigControllerUpdateBookingConfig
    >
  >;

/**
 * Update tip config position
 */
export const useBookingConfigsServiceBookingConfigControllerUpdateBookingConfig =
  <
    TData = BookingConfigsServicebookingConfigControllerUpdateBookingConfigMutationResult,
    TError = unknown,
    TContext = unknown,
  >(
    options?: Omit<
      UseMutationOptions<
        TData,
        TError,
        {
          requestBody: ConfigTipPositionDto;
        },
        TContext
      >,
      'mutationFn'
    >,
  ) =>
    useMutation<
      TData,
      TError,
      {
        requestBody: ConfigTipPositionDto;
      },
      TContext
    >({
      mutationFn: ({ requestBody }) =>
        BookingConfigsService.bookingConfigControllerUpdateBookingConfig(
          requestBody,
        ) as unknown as Promise<TData>,
      ...options,
    });

export type BookingConfigsServiceBookingConfigControllerGetTipPositionLogsDefaultResponse =
  Awaited<
    ReturnType<
      typeof BookingConfigsService.bookingConfigControllerGetTipPositionLogs
    >
  >;

export type BookingConfigsServiceBookingConfigControllerGetTipPositionLogsQueryResult<
  TData = BookingConfigsServiceBookingConfigControllerGetTipPositionLogsDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useBookingConfigsServiceBookingConfigControllerGetTipPositionLogsKey =
  'BookingConfigsServiceBookingConfigControllerGetTipPositionLogs';

/**
 * Get tip position logs
 */
export const useBookingConfigsServiceBookingConfigControllerGetTipPositionLogs =
  <
    TData = BookingConfigsServiceBookingConfigControllerGetTipPositionLogsDefaultResponse,
    TError = unknown,
    TQueryKey extends unknown[] = unknown[],
  >(
    {
      limit,
      offset,
      order,
    }: {
      limit?: number;
      offset: number;
      order?: string | null;
    },
    queryKey?: TQueryKey,
    options?: Omit<
      UseQueryOptions<TData, TError>,
      'queryKey' | 'queryFn' | 'initialData'
    >,
  ) =>
    useQuery<TData, TError>({
      queryFn: () =>
        BookingConfigsService.bookingConfigControllerGetTipPositionLogs(
          limit,
          offset,
          order,
        ) as TData,
      queryKey: [
        useBookingConfigsServiceBookingConfigControllerGetTipPositionLogsKey,
        ...(queryKey ?? [{ limit, offset, order }]),
      ],
      ...options,
    });

export type BookingConfigsServicebookingConfigControllerUpdateAnyCarConfigMutationResult =
  Awaited<
    ReturnType<
      typeof BookingConfigsService.bookingConfigControllerUpdateAnyCarConfig
    >
  >;

/**
 * Update any car config
 */
export const useBookingConfigsServiceBookingConfigControllerUpdateAnyCarConfig =
  <
    TData = BookingConfigsServicebookingConfigControllerUpdateAnyCarConfigMutationResult,
    TError = unknown,
    TContext = unknown,
  >(
    options?: Omit<
      UseMutationOptions<
        TData,
        TError,
        {
          requestBody: ConfigAnyCarDto;
        },
        TContext
      >,
      'mutationFn'
    >,
  ) =>
    useMutation<
      TData,
      TError,
      {
        requestBody: ConfigAnyCarDto;
      },
      TContext
    >({
      mutationFn: ({ requestBody }) =>
        BookingConfigsService.bookingConfigControllerUpdateAnyCarConfig(
          requestBody,
        ) as unknown as Promise<TData>,
      ...options,
    });

export type BookingConfigsServicebookingConfigControllerUpdateAnyCarStatusConfigMutationResult =
  Awaited<
    ReturnType<
      typeof BookingConfigsService.bookingConfigControllerUpdateAnyCarStatusConfig
    >
  >;

/**
 * Update any car status config
 */
export const useBookingConfigsServiceBookingConfigControllerUpdateAnyCarStatusConfig =
  <
    TData = BookingConfigsServicebookingConfigControllerUpdateAnyCarStatusConfigMutationResult,
    TError = unknown,
    TContext = unknown,
  >(
    options?: Omit<
      UseMutationOptions<
        TData,
        TError,
        {
          requestBody: ConfigAnyCarStatusDto;
        },
        TContext
      >,
      'mutationFn'
    >,
  ) =>
    useMutation<
      TData,
      TError,
      {
        requestBody: ConfigAnyCarStatusDto;
      },
      TContext
    >({
      mutationFn: ({ requestBody }) =>
        BookingConfigsService.bookingConfigControllerUpdateAnyCarStatusConfig(
          requestBody,
        ) as unknown as Promise<TData>,
      ...options,
    });

export type BannersServiceBannerAdminControllerGetListInAppNavigationValueDefaultResponse =
  Awaited<
    ReturnType<
      typeof BannersService.bannerAdminControllerGetListInAppNavigationValue
    >
  >;

export type BannersServiceBannerAdminControllerGetListInAppNavigationValueQueryResult<
  TData = BannersServiceBannerAdminControllerGetListInAppNavigationValueDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useBannersServiceBannerAdminControllerGetListInAppNavigationValueKey =
  'BannersServiceBannerAdminControllerGetListInAppNavigationValue';

/**
 * Get list value of in app navigation
 */
export const useBannersServiceBannerAdminControllerGetListInAppNavigationValue =
  <
    TData = BannersServiceBannerAdminControllerGetListInAppNavigationValueDefaultResponse,
    TError = unknown,
    TQueryKey extends unknown[] = unknown[],
  >(
    queryKey?: TQueryKey,
    options?: Omit<
      UseQueryOptions<TData, TError>,
      'queryKey' | 'queryFn' | 'initialData'
    >,
  ) =>
    useQuery<TData, TError>({
      queryFn: () =>
        BannersService.bannerAdminControllerGetListInAppNavigationValue() as TData,
      queryKey: [
        useBannersServiceBannerAdminControllerGetListInAppNavigationValueKey,
        ...(queryKey ?? []),
      ],
      ...options,
    });

export type BannersServicebannerAdminControllerCreateBannerMutationResult =
  Awaited<ReturnType<typeof BannersService.bannerAdminControllerCreateBanner>>;

/**
 * Create a new banner
 */
export const useBannersServiceBannerAdminControllerCreateBanner = <
  TData = BannersServicebannerAdminControllerCreateBannerMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: BannerParamDto;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: BannerParamDto;
    },
    TContext
  >({
    mutationFn: ({ requestBody }) =>
      BannersService.bannerAdminControllerCreateBanner(
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type BannersServiceBannerAdminControllerGetListBannerDefaultResponse =
  Awaited<ReturnType<typeof BannersService.bannerAdminControllerGetListBanner>>;

export type BannersServiceBannerAdminControllerGetListBannerQueryResult<
  TData = BannersServiceBannerAdminControllerGetListBannerDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useBannersServiceBannerAdminControllerGetListBannerKey =
  'BannersServiceBannerAdminControllerGetListBanner';

/**
 * Get list banner
 */
export const useBannersServiceBannerAdminControllerGetListBanner = <
  TData = BannersServiceBannerAdminControllerGetListBannerDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    limit,
    offset,
    q,
    isActive,
    isDisplay,
    navigationType,
    createdById,
    fromDate,
    toDate,
    order,
  }: {
    limit?: number;
    offset: number;
    q?: string | null;
    isActive?: boolean | null;
    isDisplay?: boolean | null;
    navigationType?: 'URL' | 'IN_APP' | null;
    createdById?: string | null;
    fromDate?: string | null;
    toDate?: string | null;
    order?: string | null;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      BannersService.bannerAdminControllerGetListBanner(
        limit,
        offset,
        q,
        isActive,
        isDisplay,
        navigationType,
        createdById,
        fromDate,
        toDate,
        order,
      ) as TData,
    queryKey: [
      useBannersServiceBannerAdminControllerGetListBannerKey,
      ...(queryKey ?? [
        {
          createdById,
          fromDate,
          isActive,
          isDisplay,
          limit,
          navigationType,
          offset,
          order,
          q,
          toDate,
        },
      ]),
    ],
    ...options,
  });

export type BannersServicebannerAdminControllerUpdateDisplayBannerMutationResult =
  Awaited<
    ReturnType<typeof BannersService.bannerAdminControllerUpdateDisplayBanner>
  >;

/**
 * Update banner displayed
 */
export const useBannersServiceBannerAdminControllerUpdateDisplayBanner = <
  TData = BannersServicebannerAdminControllerUpdateDisplayBannerMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: DisplayBannerDto[];
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: DisplayBannerDto[];
    },
    TContext
  >({
    mutationFn: ({ requestBody }) =>
      BannersService.bannerAdminControllerUpdateDisplayBanner(
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type BannersServicebannerAdminControllerUpdateBannerStatusMutationResult =
  Awaited<
    ReturnType<typeof BannersService.bannerAdminControllerUpdateBannerStatus>
  >;

/**
 * Update status banner
 */
export const useBannersServiceBannerAdminControllerUpdateBannerStatus = <
  TData = BannersServicebannerAdminControllerUpdateBannerStatusMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        id: string;
        requestBody: UpdateStatusBannerDto;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      id: string;
      requestBody: UpdateStatusBannerDto;
    },
    TContext
  >({
    mutationFn: ({ id, requestBody }) =>
      BannersService.bannerAdminControllerUpdateBannerStatus(
        id,
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type BannersServicebannerAdminControllerDeleteBannerMutationResult =
  Awaited<ReturnType<typeof BannersService.bannerAdminControllerDeleteBanner>>;

/**
 * Delete a banner
 */
export const useBannersServiceBannerAdminControllerDeleteBanner = <
  TData = BannersServicebannerAdminControllerDeleteBannerMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        id: string;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      id: string;
    },
    TContext
  >({
    mutationFn: ({ id }) =>
      BannersService.bannerAdminControllerDeleteBanner(
        id,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type BannersServicebannerAdminControllerUpdateBannerMutationResult =
  Awaited<ReturnType<typeof BannersService.bannerAdminControllerUpdateBanner>>;

/**
 * Update banner content
 */
export const useBannersServiceBannerAdminControllerUpdateBanner = <
  TData = BannersServicebannerAdminControllerUpdateBannerMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        id: string;
        requestBody: BannerParamDto;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      id: string;
      requestBody: BannerParamDto;
    },
    TContext
  >({
    mutationFn: ({ id, requestBody }) =>
      BannersService.bannerAdminControllerUpdateBanner(
        id,
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type BannersServiceBannerAdminControllerGetBannerDetailDefaultResponse =
  Awaited<
    ReturnType<typeof BannersService.bannerAdminControllerGetBannerDetail>
  >;

export type BannersServiceBannerAdminControllerGetBannerDetailQueryResult<
  TData = BannersServiceBannerAdminControllerGetBannerDetailDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useBannersServiceBannerAdminControllerGetBannerDetailKey =
  'BannersServiceBannerAdminControllerGetBannerDetail';

/**
 * Get banner detail
 */
export const useBannersServiceBannerAdminControllerGetBannerDetail = <
  TData = BannersServiceBannerAdminControllerGetBannerDetailDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    id,
  }: {
    id: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      BannersService.bannerAdminControllerGetBannerDetail(id) as TData,
    queryKey: [
      useBannersServiceBannerAdminControllerGetBannerDetailKey,
      ...(queryKey ?? [{ id }]),
    ],
    ...options,
  });

export type BannersServiceBannerControllerGetBannerDisplayedDefaultResponse =
  Awaited<ReturnType<typeof BannersService.bannerControllerGetBannerDisplayed>>;

export type BannersServiceBannerControllerGetBannerDisplayedQueryResult<
  TData = BannersServiceBannerControllerGetBannerDisplayedDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useBannersServiceBannerControllerGetBannerDisplayedKey =
  'BannersServiceBannerControllerGetBannerDisplayed';

/**
 * Get banner displayed of the current user
 */
export const useBannersServiceBannerControllerGetBannerDisplayed = <
  TData = BannersServiceBannerControllerGetBannerDisplayedDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () => BannersService.bannerControllerGetBannerDisplayed() as TData,
    queryKey: [
      useBannersServiceBannerControllerGetBannerDisplayedKey,
      ...(queryKey ?? []),
    ],
    ...options,
  });

export type AvailableCarsServiceAvailableCarControllerGetListAvailableCarDefaultResponse =
  Awaited<
    ReturnType<
      typeof AvailableCarsService.availableCarControllerGetListAvailableCar
    >
  >;

export type AvailableCarsServiceAvailableCarControllerGetListAvailableCarQueryResult<
  TData = AvailableCarsServiceAvailableCarControllerGetListAvailableCarDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useAvailableCarsServiceAvailableCarControllerGetListAvailableCarKey =
  'AvailableCarsServiceAvailableCarControllerGetListAvailableCar';

/**
 * Get list available car
 */
export const useAvailableCarsServiceAvailableCarControllerGetListAvailableCar =
  <
    TData = AvailableCarsServiceAvailableCarControllerGetListAvailableCarDefaultResponse,
    TError = unknown,
    TQueryKey extends unknown[] = unknown[],
  >(
    queryKey?: TQueryKey,
    options?: Omit<
      UseQueryOptions<TData, TError>,
      'queryKey' | 'queryFn' | 'initialData'
    >,
  ) =>
    useQuery<TData, TError>({
      queryFn: () =>
        AvailableCarsService.availableCarControllerGetListAvailableCar() as TData,
      queryKey: [
        useAvailableCarsServiceAvailableCarControllerGetListAvailableCarKey,
        ...(queryKey ?? []),
      ],
      ...options,
    });

export type AvailableCarsServiceAvailableCarControllerGetAvailableCarByIdDefaultResponse =
  Awaited<
    ReturnType<
      typeof AvailableCarsService.availableCarControllerGetAvailableCarById
    >
  >;

export type AvailableCarsServiceAvailableCarControllerGetAvailableCarByIdQueryResult<
  TData = AvailableCarsServiceAvailableCarControllerGetAvailableCarByIdDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useAvailableCarsServiceAvailableCarControllerGetAvailableCarByIdKey =
  'AvailableCarsServiceAvailableCarControllerGetAvailableCarById';

/**
 * Get available car by id
 */
export const useAvailableCarsServiceAvailableCarControllerGetAvailableCarById =
  <
    TData = AvailableCarsServiceAvailableCarControllerGetAvailableCarByIdDefaultResponse,
    TError = unknown,
    TQueryKey extends unknown[] = unknown[],
  >(
    {
      id,
    }: {
      id: string;
    },
    queryKey?: TQueryKey,
    options?: Omit<
      UseQueryOptions<TData, TError>,
      'queryKey' | 'queryFn' | 'initialData'
    >,
  ) =>
    useQuery<TData, TError>({
      queryFn: () =>
        AvailableCarsService.availableCarControllerGetAvailableCarById(
          id,
        ) as TData,
      queryKey: [
        useAvailableCarsServiceAvailableCarControllerGetAvailableCarByIdKey,
        ...(queryKey ?? [{ id }]),
      ],
      ...options,
    });

export type AvailableCarsServiceavailableCarControllerUpdateAvailableCarMutationResult =
  Awaited<
    ReturnType<
      typeof AvailableCarsService.availableCarControllerUpdateAvailableCar
    >
  >;

/**
 * Update available car
 */
export const useAvailableCarsServiceAvailableCarControllerUpdateAvailableCar = <
  TData = AvailableCarsServiceavailableCarControllerUpdateAvailableCarMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        id: string;
        requestBody: UpdateAvailableCarDto;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      id: string;
      requestBody: UpdateAvailableCarDto;
    },
    TContext
  >({
    mutationFn: ({ id, requestBody }) =>
      AvailableCarsService.availableCarControllerUpdateAvailableCar(
        id,
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type AuthServiceauthControllerRegisterMutationResult = Awaited<
  ReturnType<typeof AuthService.authControllerRegister>
>;

/**
 * Register a new customer
 */
export const useAuthServiceAuthControllerRegister = <
  TData = AuthServiceauthControllerRegisterMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: RegisterInputDto;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: RegisterInputDto;
    },
    TContext
  >({
    mutationFn: ({ requestBody }) =>
      AuthService.authControllerRegister(
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type AuthServiceauthControllerConfirmRegisterMutationResult = Awaited<
  ReturnType<typeof AuthService.authControllerConfirmRegister>
>;

/**
 * Verify new registration with OTP code
 */
export const useAuthServiceAuthControllerConfirmRegister = <
  TData = AuthServiceauthControllerConfirmRegisterMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: ConfirmRegisterDto;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: ConfirmRegisterDto;
    },
    TContext
  >({
    mutationFn: ({ requestBody }) =>
      AuthService.authControllerConfirmRegister(
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type AuthServiceauthControllerLoginMutationResult = Awaited<
  ReturnType<typeof AuthService.authControllerLogin>
>;

/**
 * Login
 */
export const useAuthServiceAuthControllerLogin = <
  TData = AuthServiceauthControllerLoginMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: LoginInputDto;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: LoginInputDto;
    },
    TContext
  >({
    mutationFn: ({ requestBody }) =>
      AuthService.authControllerLogin(requestBody) as unknown as Promise<TData>,
    ...options,
  });

export type AuthServiceauthControllerRefreshTokenMutationResult = Awaited<
  ReturnType<typeof AuthService.authControllerRefreshToken>
>;

/**
 * Refresh access token when expired
 */
export const useAuthServiceAuthControllerRefreshToken = <
  TData = AuthServiceauthControllerRefreshTokenMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: RefreshTokenInputDto;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: RefreshTokenInputDto;
    },
    TContext
  >({
    mutationFn: ({ requestBody }) =>
      AuthService.authControllerRefreshToken(
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type AuthServiceauthControllerForgotPasswordMutationResult = Awaited<
  ReturnType<typeof AuthService.authControllerForgotPassword>
>;

/**
 * Request OTP when forgot password
 */
export const useAuthServiceAuthControllerForgotPassword = <
  TData = AuthServiceauthControllerForgotPasswordMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: ForgotPasswordInputDto;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: ForgotPasswordInputDto;
    },
    TContext
  >({
    mutationFn: ({ requestBody }) =>
      AuthService.authControllerForgotPassword(
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type AuthServiceauthControllerResetPasswordMutationResult = Awaited<
  ReturnType<typeof AuthService.authControllerResetPassword>
>;

/**
 * Set a new password with the verification code correct
 */
export const useAuthServiceAuthControllerResetPassword = <
  TData = AuthServiceauthControllerResetPasswordMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: ResetPasswordInputDto;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: ResetPasswordInputDto;
    },
    TContext
  >({
    mutationFn: ({ requestBody }) =>
      AuthService.authControllerResetPassword(
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type AuthServiceauthControllerLoginByGoogleMutationResult = Awaited<
  ReturnType<typeof AuthService.authControllerLoginByGoogle>
>;

/**
 * Login with google
 */
export const useAuthServiceAuthControllerLoginByGoogle = <
  TData = AuthServiceauthControllerLoginByGoogleMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: LoginGoogleDto;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: LoginGoogleDto;
    },
    TContext
  >({
    mutationFn: ({ requestBody }) =>
      AuthService.authControllerLoginByGoogle(
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type AuthServiceauthControllerLoginByAppleMutationResult = Awaited<
  ReturnType<typeof AuthService.authControllerLoginByApple>
>;

/**
 * Login with apple
 */
export const useAuthServiceAuthControllerLoginByApple = <
  TData = AuthServiceauthControllerLoginByAppleMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: LoginAppleDto;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: LoginAppleDto;
    },
    TContext
  >({
    mutationFn: ({ requestBody }) =>
      AuthService.authControllerLoginByApple(
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type AuthServiceauthControllerRenewOtpMutationResult = Awaited<
  ReturnType<typeof AuthService.authControllerRenewOtp>
>;

/**
 * Resend a new OTP code
 */
export const useAuthServiceAuthControllerRenewOtp = <
  TData = AuthServiceauthControllerRenewOtpMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: ResendCodeInputDto;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: ResendCodeInputDto;
    },
    TContext
  >({
    mutationFn: ({ requestBody }) =>
      AuthService.authControllerRenewOtp(
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type AuthServiceauthControllerVerifyOtpMutationResult = Awaited<
  ReturnType<typeof AuthService.authControllerVerifyOtp>
>;

/**
 * Verify OTP code is correct
 */
export const useAuthServiceAuthControllerVerifyOtp = <
  TData = AuthServiceauthControllerVerifyOtpMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: VerifyCodeInputDto;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: VerifyCodeInputDto;
    },
    TContext
  >({
    mutationFn: ({ requestBody }) =>
      AuthService.authControllerVerifyOtp(
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type AuthServiceauthControllerLogoutMutationResult = Awaited<
  ReturnType<typeof AuthService.authControllerLogout>
>;

/**
 * Logout
 */
export const useAuthServiceAuthControllerLogout = <
  TData = AuthServiceauthControllerLogoutMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<TData, TError, void, TContext>,
    'mutationFn'
  >,
) =>
  useMutation<TData, TError, void, TContext>({
    mutationFn: () =>
      AuthService.authControllerLogout() as unknown as Promise<TData>,
    ...options,
  });

export type ArticlesServicearticleControllerCreateArticleMutationResult =
  Awaited<ReturnType<typeof ArticlesService.articleControllerCreateArticle>>;

/**
 * Create a new article
 */
export const useArticlesServiceArticleControllerCreateArticle = <
  TData = ArticlesServicearticleControllerCreateArticleMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: CreateArticleDto;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: CreateArticleDto;
    },
    TContext
  >({
    mutationFn: ({ requestBody }) =>
      ArticlesService.articleControllerCreateArticle(
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type ArticlesServiceArticleControllerGetListArticleDefaultResponse =
  Awaited<ReturnType<typeof ArticlesService.articleControllerGetListArticle>>;

export type ArticlesServiceArticleControllerGetListArticleQueryResult<
  TData = ArticlesServiceArticleControllerGetListArticleDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useArticlesServiceArticleControllerGetListArticleKey =
  'ArticlesServiceArticleControllerGetListArticle';

/**
 * Get list article
 */
export const useArticlesServiceArticleControllerGetListArticle = <
  TData = ArticlesServiceArticleControllerGetListArticleDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    limit,
    offset,
    titleEn,
    isActive,
    isDisplay,
    createdById,
    fromDate,
    toDate,
    order,
  }: {
    limit?: number;
    offset: number;
    titleEn?: string;
    isActive?: boolean;
    isDisplay?: boolean;
    createdById?: string;
    fromDate?: string;
    toDate?: string;
    order?: string | null;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      ArticlesService.articleControllerGetListArticle(
        limit,
        offset,
        titleEn,
        isActive,
        isDisplay,
        createdById,
        fromDate,
        toDate,
        order,
      ) as TData,
    queryKey: [
      useArticlesServiceArticleControllerGetListArticleKey,
      ...(queryKey ?? [
        {
          createdById,
          fromDate,
          isActive,
          isDisplay,
          limit,
          offset,
          order,
          titleEn,
          toDate,
        },
      ]),
    ],
    ...options,
  });

export type ArticlesServicearticleControllerUpdateDisplayArticleMutationResult =
  Awaited<
    ReturnType<typeof ArticlesService.articleControllerUpdateDisplayArticle>
  >;

/**
 * Update display of an article
 */
export const useArticlesServiceArticleControllerUpdateDisplayArticle = <
  TData = ArticlesServicearticleControllerUpdateDisplayArticleMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: UpdateDisplayArticleDto[];
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: UpdateDisplayArticleDto[];
    },
    TContext
  >({
    mutationFn: ({ requestBody }) =>
      ArticlesService.articleControllerUpdateDisplayArticle(
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type ArticlesServicearticleControllerUpdateArticleMutationResult =
  Awaited<ReturnType<typeof ArticlesService.articleControllerUpdateArticle>>;

/**
 * Update an article
 */
export const useArticlesServiceArticleControllerUpdateArticle = <
  TData = ArticlesServicearticleControllerUpdateArticleMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        id: string;
        requestBody: ArticleParamDto;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      id: string;
      requestBody: ArticleParamDto;
    },
    TContext
  >({
    mutationFn: ({ id, requestBody }) =>
      ArticlesService.articleControllerUpdateArticle(
        id,
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type ArticlesServicearticleControllerDeleteArticleMutationResult =
  Awaited<ReturnType<typeof ArticlesService.articleControllerDeleteArticle>>;

/**
 * Delete an article
 */
export const useArticlesServiceArticleControllerDeleteArticle = <
  TData = ArticlesServicearticleControllerDeleteArticleMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        id: string;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      id: string;
    },
    TContext
  >({
    mutationFn: ({ id }) =>
      ArticlesService.articleControllerDeleteArticle(
        id,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type ArticlesServiceArticleControllerGetArticleDetailDefaultResponse =
  Awaited<ReturnType<typeof ArticlesService.articleControllerGetArticleDetail>>;

export type ArticlesServiceArticleControllerGetArticleDetailQueryResult<
  TData = ArticlesServiceArticleControllerGetArticleDetailDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useArticlesServiceArticleControllerGetArticleDetailKey =
  'ArticlesServiceArticleControllerGetArticleDetail';

export const useArticlesServiceArticleControllerGetArticleDetail = <
  TData = ArticlesServiceArticleControllerGetArticleDetailDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    id,
  }: {
    id: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      ArticlesService.articleControllerGetArticleDetail(id) as TData,
    queryKey: [
      useArticlesServiceArticleControllerGetArticleDetailKey,
      ...(queryKey ?? [{ id }]),
    ],
    ...options,
  });

export type ArticlesServicearticleControllerUpdateArticleStatusMutationResult =
  Awaited<
    ReturnType<typeof ArticlesService.articleControllerUpdateArticleStatus>
  >;

/**
 * Update status of an article
 */
export const useArticlesServiceArticleControllerUpdateArticleStatus = <
  TData = ArticlesServicearticleControllerUpdateArticleStatusMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        id: string;
        requestBody: UpdateStatusArticleDto;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      id: string;
      requestBody: UpdateStatusArticleDto;
    },
    TContext
  >({
    mutationFn: ({ id, requestBody }) =>
      ArticlesService.articleControllerUpdateArticleStatus(
        id,
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type ArticlesServiceArticleControllerGetArticleDisplayedDefaultResponse =
  Awaited<
    ReturnType<typeof ArticlesService.articleControllerGetArticleDisplayed>
  >;

export type ArticlesServiceArticleControllerGetArticleDisplayedQueryResult<
  TData = ArticlesServiceArticleControllerGetArticleDisplayedDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useArticlesServiceArticleControllerGetArticleDisplayedKey =
  'ArticlesServiceArticleControllerGetArticleDisplayed';

/**
 * Get list displayed article
 */
export const useArticlesServiceArticleControllerGetArticleDisplayed = <
  TData = ArticlesServiceArticleControllerGetArticleDisplayedDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      ArticlesService.articleControllerGetArticleDisplayed() as TData,
    queryKey: [
      useArticlesServiceArticleControllerGetArticleDisplayedKey,
      ...(queryKey ?? []),
    ],
    ...options,
  });

export type AppServiceAppControllerCheckDefaultResponse = Awaited<
  ReturnType<typeof AppService.appControllerCheck>
>;

export type AppServiceAppControllerCheckQueryResult<
  TData = AppServiceAppControllerCheckDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useAppServiceAppControllerCheckKey =
  'AppServiceAppControllerCheck';

export const useAppServiceAppControllerCheck = <
  TData = AppServiceAppControllerCheckDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () => AppService.appControllerCheck() as TData,
    queryKey: [useAppServiceAppControllerCheckKey, ...(queryKey ?? [])],
    ...options,
  });

export type AdminServiceAdminControllerGetAdminsDefaultResponse = Awaited<
  ReturnType<typeof AdminService.adminControllerGetAdmins>
>;

export type AdminServiceAdminControllerGetAdminsQueryResult<
  TData = AdminServiceAdminControllerGetAdminsDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useAdminServiceAdminControllerGetAdminsKey =
  'AdminServiceAdminControllerGetAdmins';

/**
 * Get list of admins
 */
export const useAdminServiceAdminControllerGetAdmins = <
  TData = AdminServiceAdminControllerGetAdminsDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    limit,
    offset,
    q,
    roleId,
    isActive,
    order,
  }: {
    limit?: number;
    offset: number;
    q?: string | null;
    roleId?: string | null;
    isActive?: boolean | null;
    order?: string | null;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      AdminService.adminControllerGetAdmins(
        limit,
        offset,
        q,
        roleId,
        isActive,
        order,
      ) as TData,
    queryKey: [
      useAdminServiceAdminControllerGetAdminsKey,
      ...(queryKey ?? [{ isActive, limit, offset, order, q, roleId }]),
    ],
    ...options,
  });

export type AdminServiceadminControllerCreateAdminMutationResult = Awaited<
  ReturnType<typeof AdminService.adminControllerCreateAdmin>
>;

/**
 * Create an admin account
 */
export const useAdminServiceAdminControllerCreateAdmin = <
  TData = AdminServiceadminControllerCreateAdminMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: UpsertUserDto;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: UpsertUserDto;
    },
    TContext
  >({
    mutationFn: ({ requestBody }) =>
      AdminService.adminControllerCreateAdmin(
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type AdminServiceAdminControllerGetUserDefaultResponse = Awaited<
  ReturnType<typeof AdminService.adminControllerGetUser>
>;

export type AdminServiceAdminControllerGetUserQueryResult<
  TData = AdminServiceAdminControllerGetUserDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useAdminServiceAdminControllerGetUserKey =
  'AdminServiceAdminControllerGetUser';

/**
 * Get a admin
 */
export const useAdminServiceAdminControllerGetUser = <
  TData = AdminServiceAdminControllerGetUserDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    id,
  }: {
    id: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () => AdminService.adminControllerGetUser(id) as TData,
    queryKey: [
      useAdminServiceAdminControllerGetUserKey,
      ...(queryKey ?? [{ id }]),
    ],
    ...options,
  });

export type AdminServiceadminControllerUpdateAdminOfBusinessMutationResult =
  Awaited<ReturnType<typeof AdminService.adminControllerUpdateAdminOfBusiness>>;

/**
 * Update an admin account
 */
export const useAdminServiceAdminControllerUpdateAdminOfBusiness = <
  TData = AdminServiceadminControllerUpdateAdminOfBusinessMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        id: string;
        requestBody: UpsertUserDto;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      id: string;
      requestBody: UpsertUserDto;
    },
    TContext
  >({
    mutationFn: ({ id, requestBody }) =>
      AdminService.adminControllerUpdateAdminOfBusiness(
        id,
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type AdminServiceadminControllerDeleteAdminOfBusinessMutationResult =
  Awaited<ReturnType<typeof AdminService.adminControllerDeleteAdminOfBusiness>>;

/**
 * Delete an admin
 */
export const useAdminServiceAdminControllerDeleteAdminOfBusiness = <
  TData = AdminServiceadminControllerDeleteAdminOfBusinessMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        id: string;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      id: string;
    },
    TContext
  >({
    mutationFn: ({ id }) =>
      AdminService.adminControllerDeleteAdminOfBusiness(
        id,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type AdminServiceadminControllerUpdateAdminStatusMutationResult =
  Awaited<ReturnType<typeof AdminService.adminControllerUpdateAdminStatus>>;

/**
 * Update status of a admin
 */
export const useAdminServiceAdminControllerUpdateAdminStatus = <
  TData = AdminServiceadminControllerUpdateAdminStatusMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        id: string;
        requestBody: UpdateUserStatusDto;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      id: string;
      requestBody: UpdateUserStatusDto;
    },
    TContext
  >({
    mutationFn: ({ id, requestBody }) =>
      AdminService.adminControllerUpdateAdminStatus(
        id,
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });
