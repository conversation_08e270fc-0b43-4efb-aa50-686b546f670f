/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { BaseResponse } from '../models/BaseResponse';

import type { CancelablePromise } from '../core/CancelablePromise';
import { OpenAPI } from '../core/OpenAPI';
import { request as __request } from '../core/request';

export class ZoneService {

  /**
   * Crawl zone
   * @returns any
   * @throws ApiError
   */
  public static zonePrivateControllerCrawlZone(): CancelablePromise<BaseResponse> {
    return __request(OpenAPI, {
      method: 'POST',
      url: '/zone/crawl',
    });
  }

  /**
   * Get zone detail
   * @param id Param id is required
   * @returns any
   * @throws ApiError
   */
  public static zonePrivateControllerGetZoneDetail(
    id: string,
  ): CancelablePromise<BaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/zone/{id}',
      path: {
        'id': id,
      },
    });
  }

  /**
   * Get zone list
   * @param name
   * @param zoneIds
   * @param isIgnore
   * @param limit
   * @param offset
   * @param order Format: fieldName:[asc,desc]
   * @returns any
   * @throws ApiError
   */
  public static zonePrivateControllerGetZoneList(
    name?: string,
    zoneIds?: string,
    isIgnore?: boolean,
    limit: number = 10,
    offset: number,
    order?: string | null,
  ): CancelablePromise<BaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/zone',
      query: {
        'name': name,
        'zoneIds': zoneIds,
        'isIgnore': isIgnore,
        'limit': limit,
        'offset': offset,
        'order': order,
      },
    });
  }

  /**
   * Get zone filter list
   * @param name
   * @param limit
   * @param offset
   * @param order Format: fieldName:[asc,desc]
   * @returns any
   * @throws ApiError
   */
  public static zonePrivateControllerGetBaseZoneList(
    name?: string,
    limit: number = 10,
    offset: number,
    order?: string | null,
  ): CancelablePromise<BaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/zone/filter',
      query: {
        'name': name,
        'limit': limit,
        'offset': offset,
        'order': order,
      },
    });
  }

  /**
   * Get list of zones by Ids
   * @param ids
   * @returns any
   * @throws ApiError
   */
  public static zonePrivateControllerGetZoneListByIds(
    ids?: string | null,
  ): CancelablePromise<BaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/zone/batch',
      query: {
        'ids': ids,
      },
    });
  }

}
