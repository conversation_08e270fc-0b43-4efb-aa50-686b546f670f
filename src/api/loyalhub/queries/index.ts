// generated with @7nohe/openapi-react-query-codegen@0.5.2
import type {
  UseMutationOptions,
  UseQueryOptions,
  UseQueryResult,
} from '@tanstack/react-query';
import { useMutation, useQuery } from '@tanstack/react-query';
import type { CreateCommentCommandInput } from '../requests/models/CreateCommentCommandInput';
import type { CreateDepartmentInput } from '../requests/models/CreateDepartmentInput';
import type { CreateNewEmployeeInput } from '../requests/models/CreateNewEmployeeInput';
import type { CreateUploadPresignedUrlInput } from '../requests/models/CreateUploadPresignedUrlInput';
import type { DelateAttachmentInput } from '../requests/models/DelateAttachmentInput';
import type { LoginCommandInput } from '../requests/models/LoginCommandInput';
import type { RefreshTokenCommandInput } from '../requests/models/RefreshTokenCommandInput';
import type { RegisterCommandInput } from '../requests/models/RegisterCommandInput';
import type { RoleParam } from '../requests/models/RoleParam';
import type { UpdateDepartmentInput } from '../requests/models/UpdateDepartmentInput';
import type { UpdateEmployeeInput } from '../requests/models/UpdateEmployeeInput';
import type { UpdateRoleInput } from '../requests/models/UpdateRoleInput';
import type { UpdateStatusEmployeeInput } from '../requests/models/UpdateStatusEmployeeInput';
import type { UpdateTaskInput } from '../requests/models/UpdateTaskInput';
import type { UploadAttachmentInput } from '../requests/models/UploadAttachmentInput';
import type { UpsertOptionInput } from '../requests/models/UpsertOptionInput';
import { AppService } from '../requests/services/AppService';
import { AuthService } from '../requests/services/AuthService';
import { DepartmentsService } from '../requests/services/DepartmentsService';
import { EmployeeAttachmentsService } from '../requests/services/EmployeeAttachmentsService';
import { EmployeesService } from '../requests/services/EmployeesService';
import { FilterOptionsService } from '../requests/services/FilterOptionsService';
import { FleetsService } from '../requests/services/FleetsService';
import { PermissionsRolesService } from '../requests/services/PermissionsRolesService';
import { RatingsService } from '../requests/services/RatingsService';
import { StoragesService } from '../requests/services/StoragesService';
import { TasksService } from '../requests/services/TasksService';
import { ZoneService } from '../requests/services/ZoneService';

export type ZoneServicezonePrivateControllerCrawlZoneMutationResult = Awaited<
  ReturnType<typeof ZoneService.zonePrivateControllerCrawlZone>
>;

/**
 * Crawl zone
 */
export const useZoneServiceZonePrivateControllerCrawlZone = <
  TData = ZoneServicezonePrivateControllerCrawlZoneMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<TData, TError, void, TContext>,
    'mutationFn'
  >,
) =>
  useMutation<TData, TError, void, TContext>({
    mutationFn: () =>
      ZoneService.zonePrivateControllerCrawlZone() as unknown as Promise<TData>,
    ...options,
  });

export type ZoneServiceZonePrivateControllerGetZoneDetailDefaultResponse =
  Awaited<ReturnType<typeof ZoneService.zonePrivateControllerGetZoneDetail>>;

export type ZoneServiceZonePrivateControllerGetZoneDetailQueryResult<
  TData = ZoneServiceZonePrivateControllerGetZoneDetailDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useZoneServiceZonePrivateControllerGetZoneDetailKey =
  'ZoneServiceZonePrivateControllerGetZoneDetail';

/**
 * Get zone detail
 */
export const useZoneServiceZonePrivateControllerGetZoneDetail = <
  TData = ZoneServiceZonePrivateControllerGetZoneDetailDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    id,
  }: {
    id: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () => ZoneService.zonePrivateControllerGetZoneDetail(id) as TData,
    queryKey: [
      useZoneServiceZonePrivateControllerGetZoneDetailKey,
      ...(queryKey ?? [{ id }]),
    ],
    ...options,
  });

export type ZoneServiceZonePrivateControllerGetZoneListDefaultResponse =
  Awaited<ReturnType<typeof ZoneService.zonePrivateControllerGetZoneList>>;

export type ZoneServiceZonePrivateControllerGetZoneListQueryResult<
  TData = ZoneServiceZonePrivateControllerGetZoneListDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useZoneServiceZonePrivateControllerGetZoneListKey =
  'ZoneServiceZonePrivateControllerGetZoneList';

/**
 * Get zone list
 */
export const useZoneServiceZonePrivateControllerGetZoneList = <
  TData = ZoneServiceZonePrivateControllerGetZoneListDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    name,
    zoneIds,
    isIgnore,
    limit,
    offset,
    order,
  }: {
    name?: string;
    zoneIds?: string;
    isIgnore?: boolean;
    limit?: number;
    offset: number;
    order?: string | null;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      ZoneService.zonePrivateControllerGetZoneList(
        name,
        zoneIds,
        isIgnore,
        limit,
        offset,
        order,
      ) as TData,
    queryKey: [
      useZoneServiceZonePrivateControllerGetZoneListKey,
      ...(queryKey ?? [{ isIgnore, limit, name, offset, order, zoneIds }]),
    ],
    ...options,
  });

export type ZoneServiceZonePrivateControllerGetBaseZoneListDefaultResponse =
  Awaited<ReturnType<typeof ZoneService.zonePrivateControllerGetBaseZoneList>>;

export type ZoneServiceZonePrivateControllerGetBaseZoneListQueryResult<
  TData = ZoneServiceZonePrivateControllerGetBaseZoneListDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useZoneServiceZonePrivateControllerGetBaseZoneListKey =
  'ZoneServiceZonePrivateControllerGetBaseZoneList';

/**
 * Get zone filter list
 */
export const useZoneServiceZonePrivateControllerGetBaseZoneList = <
  TData = ZoneServiceZonePrivateControllerGetBaseZoneListDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    name,
    limit,
    offset,
    order,
  }: {
    name?: string;
    limit?: number;
    offset: number;
    order?: string | null;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      ZoneService.zonePrivateControllerGetBaseZoneList(
        name,
        limit,
        offset,
        order,
      ) as TData,
    queryKey: [
      useZoneServiceZonePrivateControllerGetBaseZoneListKey,
      ...(queryKey ?? [{ limit, name, offset, order }]),
    ],
    ...options,
  });

export type ZoneServiceZonePrivateControllerGetZoneListByIdsDefaultResponse =
  Awaited<ReturnType<typeof ZoneService.zonePrivateControllerGetZoneListByIds>>;

export type ZoneServiceZonePrivateControllerGetZoneListByIdsQueryResult<
  TData = ZoneServiceZonePrivateControllerGetZoneListByIdsDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useZoneServiceZonePrivateControllerGetZoneListByIdsKey =
  'ZoneServiceZonePrivateControllerGetZoneListByIds';

/**
 * Get list of zones by Ids
 */
export const useZoneServiceZonePrivateControllerGetZoneListByIds = <
  TData = ZoneServiceZonePrivateControllerGetZoneListByIdsDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    ids,
  }: {
    ids?: string | null;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      ZoneService.zonePrivateControllerGetZoneListByIds(ids) as TData,
    queryKey: [
      useZoneServiceZonePrivateControllerGetZoneListByIdsKey,
      ...(queryKey ?? [{ ids }]),
    ],
    ...options,
  });

export type TasksServicetaskPrivateControllerCreateTaskMutationResult = Awaited<
  ReturnType<typeof TasksService.taskPrivateControllerCreateTask>
>;

/**
 * Create task
 */
export const useTasksServiceTaskPrivateControllerCreateTask = <
  TData = TasksServicetaskPrivateControllerCreateTaskMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<TData, TError, void, TContext>,
    'mutationFn'
  >,
) =>
  useMutation<TData, TError, void, TContext>({
    mutationFn: () =>
      TasksService.taskPrivateControllerCreateTask() as unknown as Promise<TData>,
    ...options,
  });

export type TasksServiceTaskPrivateControllerGetListTaskDefaultResponse =
  Awaited<ReturnType<typeof TasksService.taskPrivateControllerGetListTask>>;

export type TasksServiceTaskPrivateControllerGetListTaskQueryResult<
  TData = TasksServiceTaskPrivateControllerGetListTaskDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useTasksServiceTaskPrivateControllerGetListTaskKey =
  'TasksServiceTaskPrivateControllerGetListTask';

/**
 * Get list task
 */
export const useTasksServiceTaskPrivateControllerGetListTask = <
  TData = TasksServiceTaskPrivateControllerGetListTaskDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    q,
    startDate,
    endDate,
    dueStartDate,
    dueEndDate,
    optionStatusId,
    optionPriorityId,
    picIds,
    createdByIds,
    employeeId,
    fleetId,
    limit,
    offset,
    order,
  }: {
    q?: string | null;
    startDate?: string | null;
    endDate?: string | null;
    dueStartDate?: string | null;
    dueEndDate?: string | null;
    optionStatusId?: string;
    optionPriorityId?: string;
    picIds?: string;
    createdByIds?: string;
    employeeId?: string;
    fleetId?: string;
    limit?: number;
    offset: number;
    order?: string | null;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      TasksService.taskPrivateControllerGetListTask(
        q,
        startDate,
        endDate,
        dueStartDate,
        dueEndDate,
        optionStatusId,
        optionPriorityId,
        picIds,
        createdByIds,
        employeeId,
        fleetId,
        limit,
        offset,
        order,
      ) as TData,
    queryKey: [
      useTasksServiceTaskPrivateControllerGetListTaskKey,
      ...(queryKey ?? [
        {
          createdByIds,
          dueEndDate,
          dueStartDate,
          employeeId,
          endDate,
          fleetId,
          limit,
          offset,
          optionPriorityId,
          optionStatusId,
          order,
          picIds,
          q,
          startDate,
        },
      ]),
    ],
    ...options,
  });

export type TasksServicetaskPrivateControllerUpdateTaskMutationResult = Awaited<
  ReturnType<typeof TasksService.taskPrivateControllerUpdateTask>
>;

/**
 * Update task
 */
export const useTasksServiceTaskPrivateControllerUpdateTask = <
  TData = TasksServicetaskPrivateControllerUpdateTaskMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        id: string;
        requestBody: UpdateTaskInput;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      id: string;
      requestBody: UpdateTaskInput;
    },
    TContext
  >({
    mutationFn: ({ id, requestBody }) =>
      TasksService.taskPrivateControllerUpdateTask(
        id,
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type TasksServiceTaskPrivateControllerGetTaskDetailDefaultResponse =
  Awaited<ReturnType<typeof TasksService.taskPrivateControllerGetTaskDetail>>;

export type TasksServiceTaskPrivateControllerGetTaskDetailQueryResult<
  TData = TasksServiceTaskPrivateControllerGetTaskDetailDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useTasksServiceTaskPrivateControllerGetTaskDetailKey =
  'TasksServiceTaskPrivateControllerGetTaskDetail';

/**
 * Task detail
 */
export const useTasksServiceTaskPrivateControllerGetTaskDetail = <
  TData = TasksServiceTaskPrivateControllerGetTaskDetailDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    id,
  }: {
    id: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () => TasksService.taskPrivateControllerGetTaskDetail(id) as TData,
    queryKey: [
      useTasksServiceTaskPrivateControllerGetTaskDetailKey,
      ...(queryKey ?? [{ id }]),
    ],
    ...options,
  });

export type TasksServicetaskPrivateControllerDeleteTaskMutationResult = Awaited<
  ReturnType<typeof TasksService.taskPrivateControllerDeleteTask>
>;

/**
 * Delete task
 */
export const useTasksServiceTaskPrivateControllerDeleteTask = <
  TData = TasksServicetaskPrivateControllerDeleteTaskMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        id: string;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      id: string;
    },
    TContext
  >({
    mutationFn: ({ id }) =>
      TasksService.taskPrivateControllerDeleteTask(
        id,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type TasksServicetaskPrivateControllerCreateOptionMutationResult =
  Awaited<ReturnType<typeof TasksService.taskPrivateControllerCreateOption>>;

/**
 * Upsert option
 */
export const useTasksServiceTaskPrivateControllerCreateOption = <
  TData = TasksServicetaskPrivateControllerCreateOptionMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: UpsertOptionInput;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: UpsertOptionInput;
    },
    TContext
  >({
    mutationFn: ({ requestBody }) =>
      TasksService.taskPrivateControllerCreateOption(
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type TasksServiceTaskPrivateControllerGetListOptionDefaultResponse =
  Awaited<ReturnType<typeof TasksService.taskPrivateControllerGetListOption>>;

export type TasksServiceTaskPrivateControllerGetListOptionQueryResult<
  TData = TasksServiceTaskPrivateControllerGetListOptionDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useTasksServiceTaskPrivateControllerGetListOptionKey =
  'TasksServiceTaskPrivateControllerGetListOption';

/**
 * Get list option
 */
export const useTasksServiceTaskPrivateControllerGetListOption = <
  TData = TasksServiceTaskPrivateControllerGetListOptionDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    key,
  }: {
    key: 'PRIORITY' | 'STATUS';
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      TasksService.taskPrivateControllerGetListOption(key) as TData,
    queryKey: [
      useTasksServiceTaskPrivateControllerGetListOptionKey,
      ...(queryKey ?? [{ key }]),
    ],
    ...options,
  });

export type TasksServicetaskPrivateControllerDeleteOptionMutationResult =
  Awaited<ReturnType<typeof TasksService.taskPrivateControllerDeleteOption>>;

/**
 * Delete option
 */
export const useTasksServiceTaskPrivateControllerDeleteOption = <
  TData = TasksServicetaskPrivateControllerDeleteOptionMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        id: string;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      id: string;
    },
    TContext
  >({
    mutationFn: ({ id }) =>
      TasksService.taskPrivateControllerDeleteOption(
        id,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type TasksServiceTaskPrivateControllerGetTaskHistoryDefaultResponse =
  Awaited<ReturnType<typeof TasksService.taskPrivateControllerGetTaskHistory>>;

export type TasksServiceTaskPrivateControllerGetTaskHistoryQueryResult<
  TData = TasksServiceTaskPrivateControllerGetTaskHistoryDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useTasksServiceTaskPrivateControllerGetTaskHistoryKey =
  'TasksServiceTaskPrivateControllerGetTaskHistory';

/**
 * Get task history
 */
export const useTasksServiceTaskPrivateControllerGetTaskHistory = <
  TData = TasksServiceTaskPrivateControllerGetTaskHistoryDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    id,
  }: {
    id: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      TasksService.taskPrivateControllerGetTaskHistory(id) as TData,
    queryKey: [
      useTasksServiceTaskPrivateControllerGetTaskHistoryKey,
      ...(queryKey ?? [{ id }]),
    ],
    ...options,
  });

export type TasksServicetaskPrivateControllerCreateCommentMutationResult =
  Awaited<ReturnType<typeof TasksService.taskPrivateControllerCreateComment>>;

/**
 * Comment on task
 */
export const useTasksServiceTaskPrivateControllerCreateComment = <
  TData = TasksServicetaskPrivateControllerCreateCommentMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        id: string;
        requestBody: CreateCommentCommandInput;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      id: string;
      requestBody: CreateCommentCommandInput;
    },
    TContext
  >({
    mutationFn: ({ id, requestBody }) =>
      TasksService.taskPrivateControllerCreateComment(
        id,
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type TasksServicetaskPrivateControllerUpdateCommentMutationResult =
  Awaited<ReturnType<typeof TasksService.taskPrivateControllerUpdateComment>>;

/**
 * Update comment
 */
export const useTasksServiceTaskPrivateControllerUpdateComment = <
  TData = TasksServicetaskPrivateControllerUpdateCommentMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        commentId: string;
        taskId: string;
        requestBody: CreateCommentCommandInput;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      commentId: string;
      taskId: string;
      requestBody: CreateCommentCommandInput;
    },
    TContext
  >({
    mutationFn: ({ commentId, taskId, requestBody }) =>
      TasksService.taskPrivateControllerUpdateComment(
        commentId,
        taskId,
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type TasksServicetaskPrivateControllerDeleteCommentMutationResult =
  Awaited<ReturnType<typeof TasksService.taskPrivateControllerDeleteComment>>;

/**
 * Delete comment
 */
export const useTasksServiceTaskPrivateControllerDeleteComment = <
  TData = TasksServicetaskPrivateControllerDeleteCommentMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        commentId: string;
        taskId: string;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      commentId: string;
      taskId: string;
    },
    TContext
  >({
    mutationFn: ({ commentId, taskId }) =>
      TasksService.taskPrivateControllerDeleteComment(
        commentId,
        taskId,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type TasksServiceTaskPrivateControllerGetListCommentOfTaskDefaultResponse =
  Awaited<
    ReturnType<typeof TasksService.taskPrivateControllerGetListCommentOfTask>
  >;

export type TasksServiceTaskPrivateControllerGetListCommentOfTaskQueryResult<
  TData = TasksServiceTaskPrivateControllerGetListCommentOfTaskDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useTasksServiceTaskPrivateControllerGetListCommentOfTaskKey =
  'TasksServiceTaskPrivateControllerGetListCommentOfTask';

/**
 * Get comment of task
 */
export const useTasksServiceTaskPrivateControllerGetListCommentOfTask = <
  TData = TasksServiceTaskPrivateControllerGetListCommentOfTaskDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    taskId,
  }: {
    taskId: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      TasksService.taskPrivateControllerGetListCommentOfTask(taskId) as TData,
    queryKey: [
      useTasksServiceTaskPrivateControllerGetListCommentOfTaskKey,
      ...(queryKey ?? [{ taskId }]),
    ],
    ...options,
  });

export type TasksServicetaskPrivateControllerFollowingTaskMutationResult =
  Awaited<ReturnType<typeof TasksService.taskPrivateControllerFollowingTask>>;

/**
 * Following task
 */
export const useTasksServiceTaskPrivateControllerFollowingTask = <
  TData = TasksServicetaskPrivateControllerFollowingTaskMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        id: string;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      id: string;
    },
    TContext
  >({
    mutationFn: ({ id }) =>
      TasksService.taskPrivateControllerFollowingTask(
        id,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type StoragesServicestorageControllerGenerateS3PresignedUrlMutationResult =
  Awaited<
    ReturnType<typeof StoragesService.storageControllerGenerateS3PresignedUrl>
  >;

/**
 * Create a Presigned URL to upload file
 */
export const useStoragesServiceStorageControllerGenerateS3PresignedUrl = <
  TData = StoragesServicestorageControllerGenerateS3PresignedUrlMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: CreateUploadPresignedUrlInput;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: CreateUploadPresignedUrlInput;
    },
    TContext
  >({
    mutationFn: ({ requestBody }) =>
      StoragesService.storageControllerGenerateS3PresignedUrl(
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type RatingsServiceRatingPrivateControllerGetDriverDetailDefaultResponse =
  Awaited<
    ReturnType<typeof RatingsService.ratingPrivateControllerGetDriverDetail>
  >;

export type RatingsServiceRatingPrivateControllerGetDriverDetailQueryResult<
  TData = RatingsServiceRatingPrivateControllerGetDriverDetailDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useRatingsServiceRatingPrivateControllerGetDriverDetailKey =
  'RatingsServiceRatingPrivateControllerGetDriverDetail';

/**
 * Driver detail
 */
export const useRatingsServiceRatingPrivateControllerGetDriverDetail = <
  TData = RatingsServiceRatingPrivateControllerGetDriverDetailDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    id,
    q,
    rating,
    startDate,
    endDate,
    limit,
    offset,
    order,
  }: {
    id: string;
    q?: string | null;
    rating?: number | null;
    startDate?: string | null;
    endDate?: string | null;
    limit?: number;
    offset: number;
    order?: string | null;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      RatingsService.ratingPrivateControllerGetDriverDetail(
        id,
        q,
        rating,
        startDate,
        endDate,
        limit,
        offset,
        order,
      ) as TData,
    queryKey: [
      useRatingsServiceRatingPrivateControllerGetDriverDetailKey,
      ...(queryKey ?? [
        { endDate, id, limit, offset, order, q, rating, startDate },
      ]),
    ],
    ...options,
  });

export type RatingsServiceRatingPrivateControllerGetListRatingHistoryDefaultResponse =
  Awaited<
    ReturnType<
      typeof RatingsService.ratingPrivateControllerGetListRatingHistory
    >
  >;

export type RatingsServiceRatingPrivateControllerGetListRatingHistoryQueryResult<
  TData = RatingsServiceRatingPrivateControllerGetListRatingHistoryDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useRatingsServiceRatingPrivateControllerGetListRatingHistoryKey =
  'RatingsServiceRatingPrivateControllerGetListRatingHistory';

/**
 * Get list rating base on current user context
 */
export const useRatingsServiceRatingPrivateControllerGetListRatingHistory = <
  TData = RatingsServiceRatingPrivateControllerGetListRatingHistoryDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    q,
    rating,
    startDate,
    endDate,
    limit,
    offset,
    order,
  }: {
    q?: string | null;
    rating?: number[] | null;
    startDate?: string | null;
    endDate?: string | null;
    limit?: number;
    offset: number;
    order?: string | null;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      RatingsService.ratingPrivateControllerGetListRatingHistory(
        q,
        rating,
        startDate,
        endDate,
        limit,
        offset,
        order,
      ) as TData,
    queryKey: [
      useRatingsServiceRatingPrivateControllerGetListRatingHistoryKey,
      ...(queryKey ?? [
        { endDate, limit, offset, order, q, rating, startDate },
      ]),
    ],
    ...options,
  });

export type RatingsServiceRatingPrivateControllerGetListAverageRatingDefaultResponse =
  Awaited<
    ReturnType<
      typeof RatingsService.ratingPrivateControllerGetListAverageRating
    >
  >;

export type RatingsServiceRatingPrivateControllerGetListAverageRatingQueryResult<
  TData = RatingsServiceRatingPrivateControllerGetListAverageRatingDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useRatingsServiceRatingPrivateControllerGetListAverageRatingKey =
  'RatingsServiceRatingPrivateControllerGetListAverageRating';

/**
 * Get list rating arrange by driver
 */
export const useRatingsServiceRatingPrivateControllerGetListAverageRating = <
  TData = RatingsServiceRatingPrivateControllerGetListAverageRatingDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    q,
    minRating,
    maxRating,
    limit,
    offset,
    order,
  }: {
    q?: string | null;
    minRating?: number | null;
    maxRating?: number | null;
    limit?: number;
    offset: number;
    order?: string | null;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      RatingsService.ratingPrivateControllerGetListAverageRating(
        q,
        minRating,
        maxRating,
        limit,
        offset,
        order,
      ) as TData,
    queryKey: [
      useRatingsServiceRatingPrivateControllerGetListAverageRatingKey,
      ...(queryKey ?? [{ limit, maxRating, minRating, offset, order, q }]),
    ],
    ...options,
  });

export type RatingsServiceRatingPrivateControllerExportsDefaultResponse =
  Awaited<ReturnType<typeof RatingsService.ratingPrivateControllerExports>>;

export type RatingsServiceRatingPrivateControllerExportsQueryResult<
  TData = RatingsServiceRatingPrivateControllerExportsDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useRatingsServiceRatingPrivateControllerExportsKey =
  'RatingsServiceRatingPrivateControllerExports';

/**
 * Export booking history list
 */
export const useRatingsServiceRatingPrivateControllerExports = <
  TData = RatingsServiceRatingPrivateControllerExportsDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    q,
    rating,
    startDate,
    endDate,
    order,
  }: {
    q?: string | null;
    rating?: number[] | null;
    startDate?: string | null;
    endDate?: string | null;
    order?: string | null;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      RatingsService.ratingPrivateControllerExports(
        q,
        rating,
        startDate,
        endDate,
        order,
      ) as TData,
    queryKey: [
      useRatingsServiceRatingPrivateControllerExportsKey,
      ...(queryKey ?? [{ endDate, order, q, rating, startDate }]),
    ],
    ...options,
  });

export type RatingsServiceRatingPrivateControllerExportAvgRatingDefaultResponse =
  Awaited<
    ReturnType<typeof RatingsService.ratingPrivateControllerExportAvgRating>
  >;

export type RatingsServiceRatingPrivateControllerExportAvgRatingQueryResult<
  TData = RatingsServiceRatingPrivateControllerExportAvgRatingDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useRatingsServiceRatingPrivateControllerExportAvgRatingKey =
  'RatingsServiceRatingPrivateControllerExportAvgRating';

/**
 * Export average rating list
 */
export const useRatingsServiceRatingPrivateControllerExportAvgRating = <
  TData = RatingsServiceRatingPrivateControllerExportAvgRatingDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    q,
    minRating,
    maxRating,
    order,
  }: {
    q?: string | null;
    minRating?: number | null;
    maxRating?: number | null;
    order?: string | null;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      RatingsService.ratingPrivateControllerExportAvgRating(
        q,
        minRating,
        maxRating,
        order,
      ) as TData,
    queryKey: [
      useRatingsServiceRatingPrivateControllerExportAvgRatingKey,
      ...(queryKey ?? [{ maxRating, minRating, order, q }]),
    ],
    ...options,
  });

export type RatingsServiceRatingPrivateControllerExportRatingByDriverDefaultResponse =
  Awaited<
    ReturnType<
      typeof RatingsService.ratingPrivateControllerExportRatingByDriver
    >
  >;

export type RatingsServiceRatingPrivateControllerExportRatingByDriverQueryResult<
  TData = RatingsServiceRatingPrivateControllerExportRatingByDriverDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useRatingsServiceRatingPrivateControllerExportRatingByDriverKey =
  'RatingsServiceRatingPrivateControllerExportRatingByDriver';

/**
 * Export rating by driver id
 */
export const useRatingsServiceRatingPrivateControllerExportRatingByDriver = <
  TData = RatingsServiceRatingPrivateControllerExportRatingByDriverDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    id,
    q,
    rating,
    startDate,
    endDate,
    order,
  }: {
    id: string;
    q?: string | null;
    rating?: number | null;
    startDate?: string | null;
    endDate?: string | null;
    order?: string | null;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      RatingsService.ratingPrivateControllerExportRatingByDriver(
        id,
        q,
        rating,
        startDate,
        endDate,
        order,
      ) as TData,
    queryKey: [
      useRatingsServiceRatingPrivateControllerExportRatingByDriverKey,
      ...(queryKey ?? [{ endDate, id, order, q, rating, startDate }]),
    ],
    ...options,
  });

export type PermissionsRolesServiceRolePrivateControllerGetListRoleDefaultResponse =
  Awaited<
    ReturnType<typeof PermissionsRolesService.rolePrivateControllerGetListRole>
  >;

export type PermissionsRolesServiceRolePrivateControllerGetListRoleQueryResult<
  TData = PermissionsRolesServiceRolePrivateControllerGetListRoleDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const usePermissionsRolesServiceRolePrivateControllerGetListRoleKey =
  'PermissionsRolesServiceRolePrivateControllerGetListRole';

/**
 * Get list role
 */
export const usePermissionsRolesServiceRolePrivateControllerGetListRole = <
  TData = PermissionsRolesServiceRolePrivateControllerGetListRoleDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    name,
    isActive,
    fromDate,
    toDate,
    limit,
    offset,
    order,
  }: {
    name?: string;
    isActive?: boolean;
    fromDate?: string | null;
    toDate?: string | null;
    limit?: number;
    offset: number;
    order?: string | null;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      PermissionsRolesService.rolePrivateControllerGetListRole(
        name,
        isActive,
        fromDate,
        toDate,
        limit,
        offset,
        order,
      ) as TData,
    queryKey: [
      usePermissionsRolesServiceRolePrivateControllerGetListRoleKey,
      ...(queryKey ?? [
        { fromDate, isActive, limit, name, offset, order, toDate },
      ]),
    ],
    ...options,
  });

export type PermissionsRolesServicerolePrivateControllerCreateRoleMutationResult =
  Awaited<
    ReturnType<typeof PermissionsRolesService.rolePrivateControllerCreateRole>
  >;

/**
 * Create role
 */
export const usePermissionsRolesServiceRolePrivateControllerCreateRole = <
  TData = PermissionsRolesServicerolePrivateControllerCreateRoleMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: RoleParam;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: RoleParam;
    },
    TContext
  >({
    mutationFn: ({ requestBody }) =>
      PermissionsRolesService.rolePrivateControllerCreateRole(
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type PermissionsRolesServiceRolePrivateControllerGetListRoleFilterDefaultResponse =
  Awaited<
    ReturnType<
      typeof PermissionsRolesService.rolePrivateControllerGetListRoleFilter
    >
  >;

export type PermissionsRolesServiceRolePrivateControllerGetListRoleFilterQueryResult<
  TData = PermissionsRolesServiceRolePrivateControllerGetListRoleFilterDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const usePermissionsRolesServiceRolePrivateControllerGetListRoleFilterKey =
  'PermissionsRolesServiceRolePrivateControllerGetListRoleFilter';

/**
 * Get list role for filter
 */
export const usePermissionsRolesServiceRolePrivateControllerGetListRoleFilter =
  <
    TData = PermissionsRolesServiceRolePrivateControllerGetListRoleFilterDefaultResponse,
    TError = unknown,
    TQueryKey extends unknown[] = unknown[],
  >(
    {
      name,
      isActive,
      fromDate,
      toDate,
      limit,
      offset,
      order,
    }: {
      name?: string;
      isActive?: boolean;
      fromDate?: string | null;
      toDate?: string | null;
      limit?: number;
      offset: number;
      order?: string | null;
    },
    queryKey?: TQueryKey,
    options?: Omit<
      UseQueryOptions<TData, TError>,
      'queryKey' | 'queryFn' | 'initialData'
    >,
  ) =>
    useQuery<TData, TError>({
      queryFn: () =>
        PermissionsRolesService.rolePrivateControllerGetListRoleFilter(
          name,
          isActive,
          fromDate,
          toDate,
          limit,
          offset,
          order,
        ) as TData,
      queryKey: [
        usePermissionsRolesServiceRolePrivateControllerGetListRoleFilterKey,
        ...(queryKey ?? [
          { fromDate, isActive, limit, name, offset, order, toDate },
        ]),
      ],
      ...options,
    });

export type PermissionsRolesServiceRolePrivateControllerGetRoleDetailDefaultResponse =
  Awaited<
    ReturnType<
      typeof PermissionsRolesService.rolePrivateControllerGetRoleDetail
    >
  >;

export type PermissionsRolesServiceRolePrivateControllerGetRoleDetailQueryResult<
  TData = PermissionsRolesServiceRolePrivateControllerGetRoleDetailDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const usePermissionsRolesServiceRolePrivateControllerGetRoleDetailKey =
  'PermissionsRolesServiceRolePrivateControllerGetRoleDetail';

/**
 * Get role detail
 */
export const usePermissionsRolesServiceRolePrivateControllerGetRoleDetail = <
  TData = PermissionsRolesServiceRolePrivateControllerGetRoleDetailDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    id,
  }: {
    id: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      PermissionsRolesService.rolePrivateControllerGetRoleDetail(id) as TData,
    queryKey: [
      usePermissionsRolesServiceRolePrivateControllerGetRoleDetailKey,
      ...(queryKey ?? [{ id }]),
    ],
    ...options,
  });

export type PermissionsRolesServicerolePrivateControllerUpdateRoleMutationResult =
  Awaited<
    ReturnType<typeof PermissionsRolesService.rolePrivateControllerUpdateRole>
  >;

/**
 * Update role
 */
export const usePermissionsRolesServiceRolePrivateControllerUpdateRole = <
  TData = PermissionsRolesServicerolePrivateControllerUpdateRoleMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        id: string;
        requestBody: UpdateRoleInput;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      id: string;
      requestBody: UpdateRoleInput;
    },
    TContext
  >({
    mutationFn: ({ id, requestBody }) =>
      PermissionsRolesService.rolePrivateControllerUpdateRole(
        id,
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type PermissionsRolesServicerolePrivateControllerDeleteRoleMutationResult =
  Awaited<
    ReturnType<typeof PermissionsRolesService.rolePrivateControllerDeleteRole>
  >;

/**
 * Delete role
 */
export const usePermissionsRolesServiceRolePrivateControllerDeleteRole = <
  TData = PermissionsRolesServicerolePrivateControllerDeleteRoleMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        id: string;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      id: string;
    },
    TContext
  >({
    mutationFn: ({ id }) =>
      PermissionsRolesService.rolePrivateControllerDeleteRole(
        id,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type FleetsServiceFleetControllerGetListFleetDefaultResponse = Awaited<
  ReturnType<typeof FleetsService.fleetControllerGetListFleet>
>;

export type FleetsServiceFleetControllerGetListFleetQueryResult<
  TData = FleetsServiceFleetControllerGetListFleetDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useFleetsServiceFleetControllerGetListFleetKey =
  'FleetsServiceFleetControllerGetListFleet';

/**
 * Get list car
 */
export const useFleetsServiceFleetControllerGetListFleet = <
  TData = FleetsServiceFleetControllerGetListFleetDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    q,
    startDate,
    endDate,
    driverId,
    year,
    model,
    color,
    service,
    fuelType,
    limit,
    offset,
    order,
  }: {
    q?: string;
    startDate?: string;
    endDate?: string;
    driverId?: string;
    year?: string;
    model?: string;
    color?: string;
    service?: string;
    fuelType?: string;
    limit?: number;
    offset: number;
    order?: string | null;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      FleetsService.fleetControllerGetListFleet(
        q,
        startDate,
        endDate,
        driverId,
        year,
        model,
        color,
        service,
        fuelType,
        limit,
        offset,
        order,
      ) as TData,
    queryKey: [
      useFleetsServiceFleetControllerGetListFleetKey,
      ...(queryKey ?? [
        {
          color,
          driverId,
          endDate,
          fuelType,
          limit,
          model,
          offset,
          order,
          q,
          service,
          startDate,
          year,
        },
      ]),
    ],
    ...options,
  });

export type FleetsServiceFleetControllerGetListFleetFilterDefaultResponse =
  Awaited<ReturnType<typeof FleetsService.fleetControllerGetListFleetFilter>>;

export type FleetsServiceFleetControllerGetListFleetFilterQueryResult<
  TData = FleetsServiceFleetControllerGetListFleetFilterDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useFleetsServiceFleetControllerGetListFleetFilterKey =
  'FleetsServiceFleetControllerGetListFleetFilter';

/**
 * Get list car
 */
export const useFleetsServiceFleetControllerGetListFleetFilter = <
  TData = FleetsServiceFleetControllerGetListFleetFilterDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    q,
    startDate,
    endDate,
    driverId,
    year,
    model,
    color,
    service,
    fuelType,
    limit,
    offset,
    order,
  }: {
    q?: string;
    startDate?: string;
    endDate?: string;
    driverId?: string;
    year?: string;
    model?: string;
    color?: string;
    service?: string;
    fuelType?: string;
    limit?: number;
    offset: number;
    order?: string | null;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      FleetsService.fleetControllerGetListFleetFilter(
        q,
        startDate,
        endDate,
        driverId,
        year,
        model,
        color,
        service,
        fuelType,
        limit,
        offset,
        order,
      ) as TData,
    queryKey: [
      useFleetsServiceFleetControllerGetListFleetFilterKey,
      ...(queryKey ?? [
        {
          color,
          driverId,
          endDate,
          fuelType,
          limit,
          model,
          offset,
          order,
          q,
          service,
          startDate,
          year,
        },
      ]),
    ],
    ...options,
  });

export type FleetsServiceFleetControllerGetFleetDetailDefaultResponse = Awaited<
  ReturnType<typeof FleetsService.fleetControllerGetFleetDetail>
>;

export type FleetsServiceFleetControllerGetFleetDetailQueryResult<
  TData = FleetsServiceFleetControllerGetFleetDetailDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useFleetsServiceFleetControllerGetFleetDetailKey =
  'FleetsServiceFleetControllerGetFleetDetail';

/**
 * Get fleet detail
 */
export const useFleetsServiceFleetControllerGetFleetDetail = <
  TData = FleetsServiceFleetControllerGetFleetDetailDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    id,
  }: {
    id: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () => FleetsService.fleetControllerGetFleetDetail(id) as TData,
    queryKey: [
      useFleetsServiceFleetControllerGetFleetDetailKey,
      ...(queryKey ?? [{ id }]),
    ],
    ...options,
  });

export type FleetsServiceFleetControllerExportsDefaultResponse = Awaited<
  ReturnType<typeof FleetsService.fleetControllerExports>
>;

export type FleetsServiceFleetControllerExportsQueryResult<
  TData = FleetsServiceFleetControllerExportsDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useFleetsServiceFleetControllerExportsKey =
  'FleetsServiceFleetControllerExports';

/**
 * Export fleet list
 */
export const useFleetsServiceFleetControllerExports = <
  TData = FleetsServiceFleetControllerExportsDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    q,
    startDate,
    endDate,
    driverId,
    year,
    model,
    color,
    service,
    fuelType,
    order,
  }: {
    q?: string;
    startDate?: string;
    endDate?: string;
    driverId?: string;
    year?: string;
    model?: string;
    color?: string;
    service?: string;
    fuelType?: string;
    order?: string | null;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      FleetsService.fleetControllerExports(
        q,
        startDate,
        endDate,
        driverId,
        year,
        model,
        color,
        service,
        fuelType,
        order,
      ) as TData,
    queryKey: [
      useFleetsServiceFleetControllerExportsKey,
      ...(queryKey ?? [
        {
          color,
          driverId,
          endDate,
          fuelType,
          model,
          order,
          q,
          service,
          startDate,
          year,
        },
      ]),
    ],
    ...options,
  });

export type FilterOptionsServiceFilterOptionControllerGetFilterOptionDefaultResponse =
  Awaited<
    ReturnType<
      typeof FilterOptionsService.filterOptionControllerGetFilterOption
    >
  >;

export type FilterOptionsServiceFilterOptionControllerGetFilterOptionQueryResult<
  TData = FilterOptionsServiceFilterOptionControllerGetFilterOptionDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useFilterOptionsServiceFilterOptionControllerGetFilterOptionKey =
  'FilterOptionsServiceFilterOptionControllerGetFilterOption';

/**
 * Get filter options
 */
export const useFilterOptionsServiceFilterOptionControllerGetFilterOption = <
  TData = FilterOptionsServiceFilterOptionControllerGetFilterOptionDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    module,
  }: {
    module?: 'fleet';
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      FilterOptionsService.filterOptionControllerGetFilterOption(
        module,
      ) as TData,
    queryKey: [
      useFilterOptionsServiceFilterOptionControllerGetFilterOptionKey,
      ...(queryKey ?? [{ module }]),
    ],
    ...options,
  });

export type EmployeesServiceemployeePrivateControllerCreateEmployeeMutationResult =
  Awaited<
    ReturnType<typeof EmployeesService.employeePrivateControllerCreateEmployee>
  >;

/**
 * Create New Employee
 */
export const useEmployeesServiceEmployeePrivateControllerCreateEmployee = <
  TData = EmployeesServiceemployeePrivateControllerCreateEmployeeMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: CreateNewEmployeeInput;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: CreateNewEmployeeInput;
    },
    TContext
  >({
    mutationFn: ({ requestBody }) =>
      EmployeesService.employeePrivateControllerCreateEmployee(
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type EmployeesServiceEmployeePrivateControllerGetListEmployeeInternalDefaultResponse =
  Awaited<
    ReturnType<
      typeof EmployeesService.employeePrivateControllerGetListEmployeeInternal
    >
  >;

export type EmployeesServiceEmployeePrivateControllerGetListEmployeeInternalQueryResult<
  TData = EmployeesServiceEmployeePrivateControllerGetListEmployeeInternalDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useEmployeesServiceEmployeePrivateControllerGetListEmployeeInternalKey =
  'EmployeesServiceEmployeePrivateControllerGetListEmployeeInternal';

/**
 * Get list employee
 */
export const useEmployeesServiceEmployeePrivateControllerGetListEmployeeInternal =
  <
    TData = EmployeesServiceEmployeePrivateControllerGetListEmployeeInternalDefaultResponse,
    TError = unknown,
    TQueryKey extends unknown[] = unknown[],
  >(
    {
      type,
      q,
      startDate,
      endDate,
      minDateOfBirth,
      maxDateOfBirth,
      roleId,
      isActive,
      departmentIds,
      assignedStartDate,
      assignedEndDate,
      employeeId,
      isIgnore,
      limit,
      offset,
      order,
    }: {
      type?: 'INTERNAL' | 'DRIVER';
      q?: string | null;
      startDate?: string | null;
      endDate?: string | null;
      minDateOfBirth?: string | null;
      maxDateOfBirth?: string | null;
      roleId?: string | null;
      isActive?: boolean;
      departmentIds?: string | null;
      assignedStartDate?: string | null;
      assignedEndDate?: string | null;
      employeeId?: string;
      isIgnore?: boolean;
      limit?: number;
      offset: number;
      order?: string | null;
    },
    queryKey?: TQueryKey,
    options?: Omit<
      UseQueryOptions<TData, TError>,
      'queryKey' | 'queryFn' | 'initialData'
    >,
  ) =>
    useQuery<TData, TError>({
      queryFn: () =>
        EmployeesService.employeePrivateControllerGetListEmployeeInternal(
          type,
          q,
          startDate,
          endDate,
          minDateOfBirth,
          maxDateOfBirth,
          roleId,
          isActive,
          departmentIds,
          assignedStartDate,
          assignedEndDate,
          employeeId,
          isIgnore,
          limit,
          offset,
          order,
        ) as TData,
      queryKey: [
        useEmployeesServiceEmployeePrivateControllerGetListEmployeeInternalKey,
        ...(queryKey ?? [
          {
            assignedEndDate,
            assignedStartDate,
            departmentIds,
            employeeId,
            endDate,
            isActive,
            isIgnore,
            limit,
            maxDateOfBirth,
            minDateOfBirth,
            offset,
            order,
            q,
            roleId,
            startDate,
            type,
          },
        ]),
      ],
      ...options,
    });

export type EmployeesServiceemployeePrivateControllerUpdateDepartmentMutationResult =
  Awaited<
    ReturnType<
      typeof EmployeesService.employeePrivateControllerUpdateDepartment
    >
  >;

/**
 * Update employee
 */
export const useEmployeesServiceEmployeePrivateControllerUpdateDepartment = <
  TData = EmployeesServiceemployeePrivateControllerUpdateDepartmentMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        id: string;
        requestBody: UpdateEmployeeInput;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      id: string;
      requestBody: UpdateEmployeeInput;
    },
    TContext
  >({
    mutationFn: ({ id, requestBody }) =>
      EmployeesService.employeePrivateControllerUpdateDepartment(
        id,
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type EmployeesServiceemployeePrivateControllerDeleteMutationResult =
  Awaited<ReturnType<typeof EmployeesService.employeePrivateControllerDelete>>;

/**
 * Delete employee
 */
export const useEmployeesServiceEmployeePrivateControllerDelete = <
  TData = EmployeesServiceemployeePrivateControllerDeleteMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        id: string;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      id: string;
    },
    TContext
  >({
    mutationFn: ({ id }) =>
      EmployeesService.employeePrivateControllerDelete(
        id,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type EmployeesServiceEmployeePrivateControllerGetEmployeeDetailDefaultResponse =
  Awaited<
    ReturnType<
      typeof EmployeesService.employeePrivateControllerGetEmployeeDetail
    >
  >;

export type EmployeesServiceEmployeePrivateControllerGetEmployeeDetailQueryResult<
  TData = EmployeesServiceEmployeePrivateControllerGetEmployeeDetailDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useEmployeesServiceEmployeePrivateControllerGetEmployeeDetailKey =
  'EmployeesServiceEmployeePrivateControllerGetEmployeeDetail';

/**
 * Employee detail
 */
export const useEmployeesServiceEmployeePrivateControllerGetEmployeeDetail = <
  TData = EmployeesServiceEmployeePrivateControllerGetEmployeeDetailDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    id,
  }: {
    id: string;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      EmployeesService.employeePrivateControllerGetEmployeeDetail(id) as TData,
    queryKey: [
      useEmployeesServiceEmployeePrivateControllerGetEmployeeDetailKey,
      ...(queryKey ?? [{ id }]),
    ],
    ...options,
  });

export type EmployeesServiceemployeePrivateControllerUpdateStatusMutationResult =
  Awaited<
    ReturnType<typeof EmployeesService.employeePrivateControllerUpdateStatus>
  >;

/**
 * Update status employee
 */
export const useEmployeesServiceEmployeePrivateControllerUpdateStatus = <
  TData = EmployeesServiceemployeePrivateControllerUpdateStatusMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        id: string;
        requestBody: UpdateStatusEmployeeInput;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      id: string;
      requestBody: UpdateStatusEmployeeInput;
    },
    TContext
  >({
    mutationFn: ({ id, requestBody }) =>
      EmployeesService.employeePrivateControllerUpdateStatus(
        id,
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type EmployeesServiceEmployeePrivateControllerGetListEmployeeFilterDefaultResponse =
  Awaited<
    ReturnType<
      typeof EmployeesService.employeePrivateControllerGetListEmployeeFilter
    >
  >;

export type EmployeesServiceEmployeePrivateControllerGetListEmployeeFilterQueryResult<
  TData = EmployeesServiceEmployeePrivateControllerGetListEmployeeFilterDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useEmployeesServiceEmployeePrivateControllerGetListEmployeeFilterKey =
  'EmployeesServiceEmployeePrivateControllerGetListEmployeeFilter';

/**
 * Get list filter employee
 */
export const useEmployeesServiceEmployeePrivateControllerGetListEmployeeFilter =
  <
    TData = EmployeesServiceEmployeePrivateControllerGetListEmployeeFilterDefaultResponse,
    TError = unknown,
    TQueryKey extends unknown[] = unknown[],
  >(
    {
      type,
      q,
      startDate,
      endDate,
      minDateOfBirth,
      maxDateOfBirth,
      roleId,
      isActive,
      departmentIds,
      assignedStartDate,
      assignedEndDate,
      employeeId,
      isIgnore,
      limit,
      offset,
      order,
    }: {
      type?: 'INTERNAL' | 'DRIVER';
      q?: string | null;
      startDate?: string | null;
      endDate?: string | null;
      minDateOfBirth?: string | null;
      maxDateOfBirth?: string | null;
      roleId?: string | null;
      isActive?: boolean;
      departmentIds?: string | null;
      assignedStartDate?: string | null;
      assignedEndDate?: string | null;
      employeeId?: string;
      isIgnore?: boolean;
      limit?: number;
      offset: number;
      order?: string | null;
    },
    queryKey?: TQueryKey,
    options?: Omit<
      UseQueryOptions<TData, TError>,
      'queryKey' | 'queryFn' | 'initialData'
    >,
  ) =>
    useQuery<TData, TError>({
      queryFn: () =>
        EmployeesService.employeePrivateControllerGetListEmployeeFilter(
          type,
          q,
          startDate,
          endDate,
          minDateOfBirth,
          maxDateOfBirth,
          roleId,
          isActive,
          departmentIds,
          assignedStartDate,
          assignedEndDate,
          employeeId,
          isIgnore,
          limit,
          offset,
          order,
        ) as TData,
      queryKey: [
        useEmployeesServiceEmployeePrivateControllerGetListEmployeeFilterKey,
        ...(queryKey ?? [
          {
            assignedEndDate,
            assignedStartDate,
            departmentIds,
            employeeId,
            endDate,
            isActive,
            isIgnore,
            limit,
            maxDateOfBirth,
            minDateOfBirth,
            offset,
            order,
            q,
            roleId,
            startDate,
            type,
          },
        ]),
      ],
      ...options,
    });

export type EmployeesServiceEmployeePrivateControllerExportEmployeeInternalDefaultResponse =
  Awaited<
    ReturnType<
      typeof EmployeesService.employeePrivateControllerExportEmployeeInternal
    >
  >;

export type EmployeesServiceEmployeePrivateControllerExportEmployeeInternalQueryResult<
  TData = EmployeesServiceEmployeePrivateControllerExportEmployeeInternalDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useEmployeesServiceEmployeePrivateControllerExportEmployeeInternalKey =
  'EmployeesServiceEmployeePrivateControllerExportEmployeeInternal';

/**
 * Export employee
 */
export const useEmployeesServiceEmployeePrivateControllerExportEmployeeInternal =
  <
    TData = EmployeesServiceEmployeePrivateControllerExportEmployeeInternalDefaultResponse,
    TError = unknown,
    TQueryKey extends unknown[] = unknown[],
  >(
    {
      type,
      q,
      startDate,
      endDate,
      minDateOfBirth,
      maxDateOfBirth,
      roleId,
      isActive,
      departmentIds,
      assignedStartDate,
      assignedEndDate,
      order,
    }: {
      type?: 'INTERNAL' | 'DRIVER';
      q?: string | null;
      startDate?: string | null;
      endDate?: string | null;
      minDateOfBirth?: string | null;
      maxDateOfBirth?: string | null;
      roleId?: string | null;
      isActive?: boolean;
      departmentIds?: string | null;
      assignedStartDate?: string | null;
      assignedEndDate?: string | null;
      order?: string | null;
    },
    queryKey?: TQueryKey,
    options?: Omit<
      UseQueryOptions<TData, TError>,
      'queryKey' | 'queryFn' | 'initialData'
    >,
  ) =>
    useQuery<TData, TError>({
      queryFn: () =>
        EmployeesService.employeePrivateControllerExportEmployeeInternal(
          type,
          q,
          startDate,
          endDate,
          minDateOfBirth,
          maxDateOfBirth,
          roleId,
          isActive,
          departmentIds,
          assignedStartDate,
          assignedEndDate,
          order,
        ) as TData,
      queryKey: [
        useEmployeesServiceEmployeePrivateControllerExportEmployeeInternalKey,
        ...(queryKey ?? [
          {
            assignedEndDate,
            assignedStartDate,
            departmentIds,
            endDate,
            isActive,
            maxDateOfBirth,
            minDateOfBirth,
            order,
            q,
            roleId,
            startDate,
            type,
          },
        ]),
      ],
      ...options,
    });

export type EmployeesServiceEmployeePrivateControllerExportDefaultResponse =
  Awaited<ReturnType<typeof EmployeesService.employeePrivateControllerExport>>;

export type EmployeesServiceEmployeePrivateControllerExportQueryResult<
  TData = EmployeesServiceEmployeePrivateControllerExportDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useEmployeesServiceEmployeePrivateControllerExportKey =
  'EmployeesServiceEmployeePrivateControllerExport';

/**
 * Export driver
 */
export const useEmployeesServiceEmployeePrivateControllerExport = <
  TData = EmployeesServiceEmployeePrivateControllerExportDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    type,
    q,
    startDate,
    endDate,
    minDateOfBirth,
    maxDateOfBirth,
    roleId,
    isActive,
    departmentIds,
    assignedStartDate,
    assignedEndDate,
    order,
  }: {
    type?: 'INTERNAL' | 'DRIVER';
    q?: string | null;
    startDate?: string | null;
    endDate?: string | null;
    minDateOfBirth?: string | null;
    maxDateOfBirth?: string | null;
    roleId?: string | null;
    isActive?: boolean;
    departmentIds?: string | null;
    assignedStartDate?: string | null;
    assignedEndDate?: string | null;
    order?: string | null;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      EmployeesService.employeePrivateControllerExport(
        type,
        q,
        startDate,
        endDate,
        minDateOfBirth,
        maxDateOfBirth,
        roleId,
        isActive,
        departmentIds,
        assignedStartDate,
        assignedEndDate,
        order,
      ) as TData,
    queryKey: [
      useEmployeesServiceEmployeePrivateControllerExportKey,
      ...(queryKey ?? [
        {
          assignedEndDate,
          assignedStartDate,
          departmentIds,
          endDate,
          isActive,
          maxDateOfBirth,
          minDateOfBirth,
          order,
          q,
          roleId,
          startDate,
          type,
        },
      ]),
    ],
    ...options,
  });

export type EmployeeAttachmentsServiceemployeeAttachmentControllerUploadMutationResult =
  Awaited<
    ReturnType<
      typeof EmployeeAttachmentsService.employeeAttachmentControllerUpload
    >
  >;

/**
 * Upload file by employee
 */
export const useEmployeeAttachmentsServiceEmployeeAttachmentControllerUpload = <
  TData = EmployeeAttachmentsServiceemployeeAttachmentControllerUploadMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: UploadAttachmentInput;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: UploadAttachmentInput;
    },
    TContext
  >({
    mutationFn: ({ requestBody }) =>
      EmployeeAttachmentsService.employeeAttachmentControllerUpload(
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type EmployeeAttachmentsServiceEmployeeAttachmentControllerGetListEmployeeAttachmentDefaultResponse =
  Awaited<
    ReturnType<
      typeof EmployeeAttachmentsService.employeeAttachmentControllerGetListEmployeeAttachment
    >
  >;

export type EmployeeAttachmentsServiceEmployeeAttachmentControllerGetListEmployeeAttachmentQueryResult<
  TData = EmployeeAttachmentsServiceEmployeeAttachmentControllerGetListEmployeeAttachmentDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useEmployeeAttachmentsServiceEmployeeAttachmentControllerGetListEmployeeAttachmentKey =
  'EmployeeAttachmentsServiceEmployeeAttachmentControllerGetListEmployeeAttachment';

/**
 * Get list employee attachment
 */
export const useEmployeeAttachmentsServiceEmployeeAttachmentControllerGetListEmployeeAttachment =
  <
    TData = EmployeeAttachmentsServiceEmployeeAttachmentControllerGetListEmployeeAttachmentDefaultResponse,
    TError = unknown,
    TQueryKey extends unknown[] = unknown[],
  >(
    {
      q,
      startDate,
      endDate,
      employeeId,
      limit,
      offset,
      order,
    }: {
      q?: string | null;
      startDate?: string | null;
      endDate?: string | null;
      employeeId?: string | null;
      limit?: number;
      offset: number;
      order?: string | null;
    },
    queryKey?: TQueryKey,
    options?: Omit<
      UseQueryOptions<TData, TError>,
      'queryKey' | 'queryFn' | 'initialData'
    >,
  ) =>
    useQuery<TData, TError>({
      queryFn: () =>
        EmployeeAttachmentsService.employeeAttachmentControllerGetListEmployeeAttachment(
          q,
          startDate,
          endDate,
          employeeId,
          limit,
          offset,
          order,
        ) as TData,
      queryKey: [
        useEmployeeAttachmentsServiceEmployeeAttachmentControllerGetListEmployeeAttachmentKey,
        ...(queryKey ?? [
          { employeeId, endDate, limit, offset, order, q, startDate },
        ]),
      ],
      ...options,
    });

export type EmployeeAttachmentsServiceemployeeAttachmentControllerDeleteMutationResult =
  Awaited<
    ReturnType<
      typeof EmployeeAttachmentsService.employeeAttachmentControllerDelete
    >
  >;

/**
 * Delete attachment by employee
 */
export const useEmployeeAttachmentsServiceEmployeeAttachmentControllerDelete = <
  TData = EmployeeAttachmentsServiceemployeeAttachmentControllerDeleteMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        id: string;
        requestBody: DelateAttachmentInput;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      id: string;
      requestBody: DelateAttachmentInput;
    },
    TContext
  >({
    mutationFn: ({ id, requestBody }) =>
      EmployeeAttachmentsService.employeeAttachmentControllerDelete(
        id,
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type DepartmentsServicedepartmentsPrivateControllerCreateDepartmentMutationResult =
  Awaited<
    ReturnType<
      typeof DepartmentsService.departmentsPrivateControllerCreateDepartment
    >
  >;

/**
 * Create department
 */
export const useDepartmentsServiceDepartmentsPrivateControllerCreateDepartment =
  <
    TData = DepartmentsServicedepartmentsPrivateControllerCreateDepartmentMutationResult,
    TError = unknown,
    TContext = unknown,
  >(
    options?: Omit<
      UseMutationOptions<
        TData,
        TError,
        {
          requestBody: CreateDepartmentInput;
        },
        TContext
      >,
      'mutationFn'
    >,
  ) =>
    useMutation<
      TData,
      TError,
      {
        requestBody: CreateDepartmentInput;
      },
      TContext
    >({
      mutationFn: ({ requestBody }) =>
        DepartmentsService.departmentsPrivateControllerCreateDepartment(
          requestBody,
        ) as unknown as Promise<TData>,
      ...options,
    });

export type DepartmentsServiceDepartmentsPrivateControllerGetListDepartmentDefaultResponse =
  Awaited<
    ReturnType<
      typeof DepartmentsService.departmentsPrivateControllerGetListDepartment
    >
  >;

export type DepartmentsServiceDepartmentsPrivateControllerGetListDepartmentQueryResult<
  TData = DepartmentsServiceDepartmentsPrivateControllerGetListDepartmentDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useDepartmentsServiceDepartmentsPrivateControllerGetListDepartmentKey =
  'DepartmentsServiceDepartmentsPrivateControllerGetListDepartment';

/**
 * Get list department
 */
export const useDepartmentsServiceDepartmentsPrivateControllerGetListDepartment =
  <
    TData = DepartmentsServiceDepartmentsPrivateControllerGetListDepartmentDefaultResponse,
    TError = unknown,
    TQueryKey extends unknown[] = unknown[],
  >(
    {
      q,
      departmentId,
      ignoredDepartmentId,
      limit,
      offset,
      order,
    }: {
      q?: string | null;
      departmentId?: string;
      ignoredDepartmentId?: string | null;
      limit?: number;
      offset: number;
      order?: string | null;
    },
    queryKey?: TQueryKey,
    options?: Omit<
      UseQueryOptions<TData, TError>,
      'queryKey' | 'queryFn' | 'initialData'
    >,
  ) =>
    useQuery<TData, TError>({
      queryFn: () =>
        DepartmentsService.departmentsPrivateControllerGetListDepartment(
          q,
          departmentId,
          ignoredDepartmentId,
          limit,
          offset,
          order,
        ) as TData,
      queryKey: [
        useDepartmentsServiceDepartmentsPrivateControllerGetListDepartmentKey,
        ...(queryKey ?? [
          { departmentId, ignoredDepartmentId, limit, offset, order, q },
        ]),
      ],
      ...options,
    });

export type DepartmentsServicedepartmentsPrivateControllerUpdateDepartmentMutationResult =
  Awaited<
    ReturnType<
      typeof DepartmentsService.departmentsPrivateControllerUpdateDepartment
    >
  >;

/**
 * Update department
 */
export const useDepartmentsServiceDepartmentsPrivateControllerUpdateDepartment =
  <
    TData = DepartmentsServicedepartmentsPrivateControllerUpdateDepartmentMutationResult,
    TError = unknown,
    TContext = unknown,
  >(
    options?: Omit<
      UseMutationOptions<
        TData,
        TError,
        {
          id: string;
          requestBody: UpdateDepartmentInput;
        },
        TContext
      >,
      'mutationFn'
    >,
  ) =>
    useMutation<
      TData,
      TError,
      {
        id: string;
        requestBody: UpdateDepartmentInput;
      },
      TContext
    >({
      mutationFn: ({ id, requestBody }) =>
        DepartmentsService.departmentsPrivateControllerUpdateDepartment(
          id,
          requestBody,
        ) as unknown as Promise<TData>,
      ...options,
    });

export type DepartmentsServicedepartmentsPrivateControllerDeleteOptionMutationResult =
  Awaited<
    ReturnType<
      typeof DepartmentsService.departmentsPrivateControllerDeleteOption
    >
  >;

/**
 * Delete department
 */
export const useDepartmentsServiceDepartmentsPrivateControllerDeleteOption = <
  TData = DepartmentsServicedepartmentsPrivateControllerDeleteOptionMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        id: string;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      id: string;
    },
    TContext
  >({
    mutationFn: ({ id }) =>
      DepartmentsService.departmentsPrivateControllerDeleteOption(
        id,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type DepartmentsServiceDepartmentsPrivateControllerGetDepartmentDetailDefaultResponse =
  Awaited<
    ReturnType<
      typeof DepartmentsService.departmentsPrivateControllerGetDepartmentDetail
    >
  >;

export type DepartmentsServiceDepartmentsPrivateControllerGetDepartmentDetailQueryResult<
  TData = DepartmentsServiceDepartmentsPrivateControllerGetDepartmentDetailDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useDepartmentsServiceDepartmentsPrivateControllerGetDepartmentDetailKey =
  'DepartmentsServiceDepartmentsPrivateControllerGetDepartmentDetail';

/**
 * Department detail
 */
export const useDepartmentsServiceDepartmentsPrivateControllerGetDepartmentDetail =
  <
    TData = DepartmentsServiceDepartmentsPrivateControllerGetDepartmentDetailDefaultResponse,
    TError = unknown,
    TQueryKey extends unknown[] = unknown[],
  >(
    {
      id,
    }: {
      id: string;
    },
    queryKey?: TQueryKey,
    options?: Omit<
      UseQueryOptions<TData, TError>,
      'queryKey' | 'queryFn' | 'initialData'
    >,
  ) =>
    useQuery<TData, TError>({
      queryFn: () =>
        DepartmentsService.departmentsPrivateControllerGetDepartmentDetail(
          id,
        ) as TData,
      queryKey: [
        useDepartmentsServiceDepartmentsPrivateControllerGetDepartmentDetailKey,
        ...(queryKey ?? [{ id }]),
      ],
      ...options,
    });

export type DepartmentsServiceDepartmentsPrivateControllerGetEmployeesByDepartmentIdDefaultResponse =
  Awaited<
    ReturnType<
      typeof DepartmentsService.departmentsPrivateControllerGetEmployeesByDepartmentId
    >
  >;

export type DepartmentsServiceDepartmentsPrivateControllerGetEmployeesByDepartmentIdQueryResult<
  TData = DepartmentsServiceDepartmentsPrivateControllerGetEmployeesByDepartmentIdDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useDepartmentsServiceDepartmentsPrivateControllerGetEmployeesByDepartmentIdKey =
  'DepartmentsServiceDepartmentsPrivateControllerGetEmployeesByDepartmentId';

/**
 * Get list employees by department id
 */
export const useDepartmentsServiceDepartmentsPrivateControllerGetEmployeesByDepartmentId =
  <
    TData = DepartmentsServiceDepartmentsPrivateControllerGetEmployeesByDepartmentIdDefaultResponse,
    TError = unknown,
    TQueryKey extends unknown[] = unknown[],
  >(
    {
      id,
      q,
      isActive,
      limit,
      offset,
      order,
    }: {
      id: string;
      q?: string | null;
      isActive?: boolean | null;
      limit?: number;
      offset: number;
      order?: string | null;
    },
    queryKey?: TQueryKey,
    options?: Omit<
      UseQueryOptions<TData, TError>,
      'queryKey' | 'queryFn' | 'initialData'
    >,
  ) =>
    useQuery<TData, TError>({
      queryFn: () =>
        DepartmentsService.departmentsPrivateControllerGetEmployeesByDepartmentId(
          id,
          q,
          isActive,
          limit,
          offset,
          order,
        ) as TData,
      queryKey: [
        useDepartmentsServiceDepartmentsPrivateControllerGetEmployeesByDepartmentIdKey,
        ...(queryKey ?? [{ id, isActive, limit, offset, order, q }]),
      ],
      ...options,
    });

export type DepartmentsServiceDepartmentsPrivateControllerExportsDefaultResponse =
  Awaited<
    ReturnType<typeof DepartmentsService.departmentsPrivateControllerExports>
  >;

export type DepartmentsServiceDepartmentsPrivateControllerExportsQueryResult<
  TData = DepartmentsServiceDepartmentsPrivateControllerExportsDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useDepartmentsServiceDepartmentsPrivateControllerExportsKey =
  'DepartmentsServiceDepartmentsPrivateControllerExports';

/**
 * Export department
 */
export const useDepartmentsServiceDepartmentsPrivateControllerExports = <
  TData = DepartmentsServiceDepartmentsPrivateControllerExportsDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  {
    q,
    order,
  }: {
    q?: string | null;
    order?: string | null;
  },
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () =>
      DepartmentsService.departmentsPrivateControllerExports(q, order) as TData,
    queryKey: [
      useDepartmentsServiceDepartmentsPrivateControllerExportsKey,
      ...(queryKey ?? [{ order, q }]),
    ],
    ...options,
  });

export type DepartmentsServiceDepartmentsPrivateControllerGetListFilterDepartmentDefaultResponse =
  Awaited<
    ReturnType<
      typeof DepartmentsService.departmentsPrivateControllerGetListFilterDepartment
    >
  >;

export type DepartmentsServiceDepartmentsPrivateControllerGetListFilterDepartmentQueryResult<
  TData = DepartmentsServiceDepartmentsPrivateControllerGetListFilterDepartmentDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useDepartmentsServiceDepartmentsPrivateControllerGetListFilterDepartmentKey =
  'DepartmentsServiceDepartmentsPrivateControllerGetListFilterDepartment';

/**
 * Get list filter department
 */
export const useDepartmentsServiceDepartmentsPrivateControllerGetListFilterDepartment =
  <
    TData = DepartmentsServiceDepartmentsPrivateControllerGetListFilterDepartmentDefaultResponse,
    TError = unknown,
    TQueryKey extends unknown[] = unknown[],
  >(
    {
      q,
      departmentId,
      ignoredDepartmentId,
      limit,
      offset,
      order,
    }: {
      q?: string | null;
      departmentId?: string;
      ignoredDepartmentId?: string | null;
      limit?: number;
      offset: number;
      order?: string | null;
    },
    queryKey?: TQueryKey,
    options?: Omit<
      UseQueryOptions<TData, TError>,
      'queryKey' | 'queryFn' | 'initialData'
    >,
  ) =>
    useQuery<TData, TError>({
      queryFn: () =>
        DepartmentsService.departmentsPrivateControllerGetListFilterDepartment(
          q,
          departmentId,
          ignoredDepartmentId,
          limit,
          offset,
          order,
        ) as TData,
      queryKey: [
        useDepartmentsServiceDepartmentsPrivateControllerGetListFilterDepartmentKey,
        ...(queryKey ?? [
          { departmentId, ignoredDepartmentId, limit, offset, order, q },
        ]),
      ],
      ...options,
    });

export type AuthServiceauthPublicControllerRegisterMutationResult = Awaited<
  ReturnType<typeof AuthService.authPublicControllerRegister>
>;

/**
 * Register a new account
 */
export const useAuthServiceAuthPublicControllerRegister = <
  TData = AuthServiceauthPublicControllerRegisterMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: RegisterCommandInput;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: RegisterCommandInput;
    },
    TContext
  >({
    mutationFn: ({ requestBody }) =>
      AuthService.authPublicControllerRegister(
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type AuthServiceauthPublicControllerLoginMutationResult = Awaited<
  ReturnType<typeof AuthService.authPublicControllerLogin>
>;

/**
 * Login with email and password
 */
export const useAuthServiceAuthPublicControllerLogin = <
  TData = AuthServiceauthPublicControllerLoginMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: LoginCommandInput;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: LoginCommandInput;
    },
    TContext
  >({
    mutationFn: ({ requestBody }) =>
      AuthService.authPublicControllerLogin(
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type AuthServiceauthPublicControllerRefreshTokenMutationResult = Awaited<
  ReturnType<typeof AuthService.authPublicControllerRefreshToken>
>;

/**
 * Refresh access token when expired
 */
export const useAuthServiceAuthPublicControllerRefreshToken = <
  TData = AuthServiceauthPublicControllerRefreshTokenMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<
      TData,
      TError,
      {
        requestBody: RefreshTokenCommandInput;
      },
      TContext
    >,
    'mutationFn'
  >,
) =>
  useMutation<
    TData,
    TError,
    {
      requestBody: RefreshTokenCommandInput;
    },
    TContext
  >({
    mutationFn: ({ requestBody }) =>
      AuthService.authPublicControllerRefreshToken(
        requestBody,
      ) as unknown as Promise<TData>,
    ...options,
  });

export type AuthServiceAuthPrivateControllerMeDefaultResponse = Awaited<
  ReturnType<typeof AuthService.authPrivateControllerMe>
>;

export type AuthServiceAuthPrivateControllerMeQueryResult<
  TData = AuthServiceAuthPrivateControllerMeDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useAuthServiceAuthPrivateControllerMeKey =
  'AuthServiceAuthPrivateControllerMe';

/**
 * Get info about me
 */
export const useAuthServiceAuthPrivateControllerMe = <
  TData = AuthServiceAuthPrivateControllerMeDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () => AuthService.authPrivateControllerMe() as TData,
    queryKey: [useAuthServiceAuthPrivateControllerMeKey, ...(queryKey ?? [])],
    ...options,
  });

export type AuthServiceauthPrivateControllerLogoutMutationResult = Awaited<
  ReturnType<typeof AuthService.authPrivateControllerLogout>
>;

/**
 * Logout
 */
export const useAuthServiceAuthPrivateControllerLogout = <
  TData = AuthServiceauthPrivateControllerLogoutMutationResult,
  TError = unknown,
  TContext = unknown,
>(
  options?: Omit<
    UseMutationOptions<TData, TError, void, TContext>,
    'mutationFn'
  >,
) =>
  useMutation<TData, TError, void, TContext>({
    mutationFn: () =>
      AuthService.authPrivateControllerLogout() as unknown as Promise<TData>,
    ...options,
  });

export type AppServiceHealthControllerCheckDefaultResponse = Awaited<
  ReturnType<typeof AppService.healthControllerCheck>
>;

export type AppServiceHealthControllerCheckQueryResult<
  TData = AppServiceHealthControllerCheckDefaultResponse,
  TError = unknown,
> = UseQueryResult<TData, TError>;

export const useAppServiceHealthControllerCheckKey =
  'AppServiceHealthControllerCheck';

export const useAppServiceHealthControllerCheck = <
  TData = AppServiceHealthControllerCheckDefaultResponse,
  TError = unknown,
  TQueryKey extends unknown[] = unknown[],
>(
  queryKey?: TQueryKey,
  options?: Omit<
    UseQueryOptions<TData, TError>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) =>
  useQuery<TData, TError>({
    queryFn: () => AppService.healthControllerCheck() as TData,
    queryKey: [useAppServiceHealthControllerCheckKey, ...(queryKey ?? [])],
    ...options,
  });
