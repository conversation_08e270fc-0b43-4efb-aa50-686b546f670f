/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { ConfirmOtpDto } from '../models/ConfirmOtpDto';
import type { IBaseResponse } from '../models/IBaseResponse';
import type { ImportEmailDto } from '../models/ImportEmailDto';
import type { JoinBusinessDto } from '../models/JoinBusinessDto';
import type { RecentLocationDto } from '../models/RecentLocationDto';
import type { RedeemCouponDto } from '../models/RedeemCouponDto';
import type { RequestOtpDto } from '../models/RequestOtpDto';
import type { SavedLocationDto } from '../models/SavedLocationDto';
import type { UpdateLocaleDto } from '../models/UpdateLocaleDto';
import type { UpdateReferredUserDto } from '../models/UpdateReferredUserDto';
import type { UpdateUserDto } from '../models/UpdateUserDto';
import type { UpdateUserPasswordDto } from '../models/UpdateUserPasswordDto';
import type { UpdateUserStatusDto } from '../models/UpdateUserStatusDto';

import type { CancelablePromise } from '../core/CancelablePromise';
import { OpenAPI } from '../core/OpenAPI';
import { request as __request } from '../core/request';

export class UsersService {

  /**
   * Get current user information
   * @returns any
   * @throws ApiError
   */
  public static userControllerGetUser(): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/users/me',
    });
  }

  /**
   * Update me
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static userControllerUpdateMe(
    requestBody: UpdateUserDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'PUT',
      url: '/v1/users/me',
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Delete me
   * @returns any
   * @throws ApiError
   */
  public static userControllerDeleteMe(): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'DELETE',
      url: '/v1/users/me',
    });
  }

  /**
   * Update a new password
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static userControllerUpdatePass(
    requestBody: UpdateUserPasswordDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'PUT',
      url: '/v1/users/me/update-password',
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Update a locale of user
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static userControllerUpdateLocale(
    requestBody: UpdateLocaleDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'PUT',
      url: '/v1/users/me/update-locale',
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * User join a business
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static userControllerJoinBusiness(
    requestBody: JoinBusinessDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'POST',
      url: '/v1/users/me/join-business',
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Add saved location
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static userControllerAddSavedLocation(
    requestBody: SavedLocationDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'POST',
      url: '/v1/users/me/saved-locations',
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Get saved location of user
   * @returns any
   * @throws ApiError
   */
  public static userControllerGetSavedLocation(): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/users/me/saved-locations',
    });
  }

  /**
   * Add saved location
   * @param id
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static userControllerUpdateSavedLocation(
    id: string,
    requestBody: SavedLocationDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'PUT',
      url: '/v1/users/me/saved-locations/{id}',
      path: {
        'id': id,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Delete a saved location
   * @param id
   * @returns any
   * @throws ApiError
   */
  public static userControllerDeleteDepartmentGroup(
    id: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'DELETE',
      url: '/v1/users/me/saved-locations/{id}',
      path: {
        'id': id,
      },
    });
  }

  /**
   * Get list user credit cards
   * @returns any
   * @throws ApiError
   */
  public static userControllerListCards(): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/users/me/cards',
    });
  }

  /**
   * Add user credit card
   * @returns any
   * @throws ApiError
   */
  public static userControllerCreateUserCreditCard(): CancelablePromise<any> {
    return __request(OpenAPI, {
      method: 'POST',
      url: '/v1/users/me/cards',
    });
  }

  /**
   * Set default card
   * @param id
   * @returns any
   * @throws ApiError
   */
  public static userControllerSetDefaultCard(
    id: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'PUT',
      url: '/v1/users/me/cards/{id}/default',
      path: {
        'id': id,
      },
    });
  }

  /**
   * Delete a card
   * @param id
   * @returns any
   * @throws ApiError
   */
  public static userControllerDeleteUserCreditCard(
    id: string,
  ): CancelablePromise<any> {
    return __request(OpenAPI, {
      method: 'DELETE',
      url: '/v1/users/me/card/{id}',
      path: {
        'id': id,
      },
    });
  }

  /**
   * Get list if recent location of a user
   * @returns any
   * @throws ApiError
   */
  public static userControllerGetRecentLocation(): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/users/me/recent-locations',
    });
  }

  /**
   * Add recent location
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static userControllerAddRecentLocation(
    requestBody: RecentLocationDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'POST',
      url: '/v1/users/me/recent-locations',
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Get static summary of referral
   * @returns any
   * @throws ApiError
   */
  public static userControllerGetReferralSummary(): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/users/me/referral-summaries',
    });
  }

  /**
   * Get list of successful referral users
   * @param limit
   * @param offset
   * @returns any
   * @throws ApiError
   */
  public static userControllerGetSuccessfulReferrals(
    limit: number = 10,
    offset: number,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/users/me/successful-referrals',
      query: {
        'limit': limit,
        'offset': offset,
      },
    });
  }

  /**
   * Update referral user
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static userControllerUpdateReferredUser(
    requestBody: UpdateReferredUserDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'PUT',
      url: '/v1/users/me/update-referral',
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Send a verify code
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static userControllerSendVerificationCode(
    requestBody: RequestOtpDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'POST',
      url: '/v1/users/send-code',
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Send a verify code
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static userControllerResendVerificationCode(
    requestBody: RequestOtpDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'POST',
      url: '/v1/users/resend-code',
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Verify code
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static userControllerVerifyCode(
    requestBody: ConfirmOtpDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'POST',
      url: '/v1/users/verify-code',
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Check user phone number is verified
   * @returns any
   * @throws ApiError
   */
  public static userControllerIsVerifyPhoneNumber(): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/users/verify-phone-number',
    });
  }

  /**
   * Import user email from excel
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static userControllerImportEmails(
    requestBody: ImportEmailDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'POST',
      url: '/v1/users/import-email',
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Get list of users
   * @param limit
   * @param offset
   * @param q
   * @param subscriptionId
   * @param createdFrom
   * @param createdTo
   * @param userId
   * @param order Format: fieldName:[asc,desc]
   * @returns any
   * @throws ApiError
   */
  public static userSuperAdminControllerGetIndividuals(
    limit: number = 10,
    offset: number,
    q?: string | null,
    subscriptionId?: string | null,
    createdFrom?: string | null,
    createdTo?: string | null,
    userId?: string,
    order?: string | null,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/users/individuals',
      query: {
        'limit': limit,
        'offset': offset,
        'q': q,
        'subscriptionId': subscriptionId,
        'createdFrom': createdFrom,
        'createdTo': createdTo,
        'userId': userId,
        'order': order,
      },
    });
  }

  /**
   * Get list filter of users
   * @param limit
   * @param offset
   * @param q
   * @param subscriptionId
   * @param createdFrom
   * @param createdTo
   * @param userId
   * @param order Format: fieldName:[asc,desc]
   * @returns any
   * @throws ApiError
   */
  public static userSuperAdminControllerGetIndividualsFilter(
    limit: number = 10,
    offset: number,
    q?: string | null,
    subscriptionId?: string | null,
    createdFrom?: string | null,
    createdTo?: string | null,
    userId?: string,
    order?: string | null,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/users/individuals-filter',
      query: {
        'limit': limit,
        'offset': offset,
        'q': q,
        'subscriptionId': subscriptionId,
        'createdFrom': createdFrom,
        'createdTo': createdTo,
        'userId': userId,
        'order': order,
      },
    });
  }

  /**
   * Get a user
   * @param id
   * @returns any
   * @throws ApiError
   */
  public static userSuperAdminControllerGetUser(
    id: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/users/{id}',
      path: {
        'id': id,
      },
    });
  }

  /**
   * Delete an user
   * @param id
   * @returns any
   * @throws ApiError
   */
  public static userSuperAdminControllerDeleteAdminOfBusiness(
    id: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'DELETE',
      url: '/v1/users/{id}',
      path: {
        'id': id,
      },
    });
  }

  /**
   * Update status of a user
   * @param id
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static userSuperAdminControllerUpdateUserStatus(
    id: string,
    requestBody: UpdateUserStatusDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'PUT',
      url: '/v1/users/{id}/status',
      path: {
        'id': id,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Export individuals history
   * @param q
   * @param subscriptionId
   * @param createdFrom
   * @param createdTo
   * @param userId
   * @param order Format: fieldName:[asc,desc]
   * @returns any
   * @throws ApiError
   */
  public static userSuperAdminControllerExportPersonalCustomers(
    q?: string | null,
    subscriptionId?: string | null,
    createdFrom?: string | null,
    createdTo?: string | null,
    userId?: string,
    order?: string | null,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/users/individuals/exports-excel',
      query: {
        'q': q,
        'subscriptionId': subscriptionId,
        'createdFrom': createdFrom,
        'createdTo': createdTo,
        'userId': userId,
        'order': order,
      },
    });
  }

  /**
   * Get list of code of a user
   * @param limit
   * @param offset
   * @param order Format: fieldName:[asc,desc]
   * @returns any
   * @throws ApiError
   */
  public static employeeCodeControllerGetCodesOfUser(
    limit: number = 10,
    offset: number,
    order?: string | null,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/users/me/codes',
      query: {
        'limit': limit,
        'offset': offset,
        'order': order,
      },
    });
  }

  /**
   * Delete a code by user
   * @param id
   * @returns any
   * @throws ApiError
   */
  public static employeeCodeControllerDeleteEmployeeCode(
    id: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'DELETE',
      url: '/v1/users/me/codes/{id}',
      path: {
        'id': id,
      },
    });
  }

  /**
   * Get list of employee of a business
   * @param id
   * @param limit
   * @param offset
   * @param employeeQ
   * @param order Format: fieldName:[asc,desc]
   * @returns any
   * @throws ApiError
   */
  public static employeeCodeControllerGetEmployeesOfCode(
    id: string,
    limit: number = 10,
    offset: number,
    employeeQ?: string | null,
    order?: string | null,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/codes/{id}/employees',
      path: {
        'id': id,
      },
      query: {
        'limit': limit,
        'offset': offset,
        'employee.q': employeeQ,
        'order': order,
      },
    });
  }

  /**
   * Get list of coupon on setting
   * @returns any
   * @throws ApiError
   */
  public static userCouponControllerGetCouponLists(): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/users/me/coupons/list',
    });
  }

  /**
   * Get list of my coupon
   * @param bookingType
   * @param vehicleType
   * @param amount
   * @param date
   * @param originLatitude
   * @param originLongitude
   * @param destinationLatitude
   * @param destinationLongitude
   * @param paymentMethod
   * @returns any
   * @throws ApiError
   */
  public static userCouponControllerGetMyCoupons(
    bookingType: 'PERSONAL' | 'BUSINESS',
    vehicleType: 'ALL' | 'TYPICAL_CAR' | 'ELECTRIC_CAR' | 'VAN' | 'ACCESSIBLE_VAN' | 'ANY_CAR',
    amount: number,
    date: string,
    originLatitude: number,
    originLongitude: number,
    destinationLatitude: number,
    destinationLongitude: number,
    paymentMethod?: 'ALL' | 'CARD' | 'CASH' | null,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/users/me/my-coupons',
      query: {
        'bookingType': bookingType,
        'vehicleType': vehicleType,
        'paymentMethod': paymentMethod,
        'amount': amount,
        'date': date,
        'originLatitude': originLatitude,
        'originLongitude': originLongitude,
        'destinationLatitude': destinationLatitude,
        'destinationLongitude': destinationLongitude,
      },
    });
  }

  /**
   * Get a coupon by code on coupon list
   * @param code
   * @returns any
   * @throws ApiError
   */
  public static userCouponControllerSearchCouponOnList(
    code: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/coupons/list/search',
      query: {
        'code': code,
      },
    });
  }

  /**
   * Get a coupon by code
   * @param bookingType
   * @param vehicleType
   * @param amount
   * @param date
   * @param originLatitude
   * @param originLongitude
   * @param destinationLatitude
   * @param destinationLongitude
   * @param code
   * @param paymentMethod
   * @returns any
   * @throws ApiError
   */
  public static userCouponControllerSearchManualCoupon(
    bookingType: 'PERSONAL' | 'BUSINESS',
    vehicleType: 'ALL' | 'TYPICAL_CAR' | 'ELECTRIC_CAR' | 'VAN' | 'ACCESSIBLE_VAN' | 'ANY_CAR',
    amount: number,
    date: string,
    originLatitude: number,
    originLongitude: number,
    destinationLatitude: number,
    destinationLongitude: number,
    code: string,
    paymentMethod?: 'ALL' | 'CARD' | 'CASH' | null,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/coupons/search',
      query: {
        'bookingType': bookingType,
        'vehicleType': vehicleType,
        'paymentMethod': paymentMethod,
        'amount': amount,
        'date': date,
        'originLatitude': originLatitude,
        'originLongitude': originLongitude,
        'destinationLatitude': destinationLatitude,
        'destinationLongitude': destinationLongitude,
        'code': code,
      },
    });
  }

  /**
   * Redeem coupon in coupon store
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static userCouponControllerRedeemCouponsStore(
    requestBody: RedeemCouponDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'POST',
      url: '/v1/coupons/redeem',
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Get business admin information
   * @param id
   * @returns any
   * @throws ApiError
   */
  public static businessAdminControllerGetBusinessAdmin(
    id: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/business-admin/{id}',
      path: {
        'id': id,
      },
    });
  }

}
