/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { ConfigAnyCarDto } from '../models/ConfigAnyCarDto';
import type { ConfigAnyCarStatusDto } from '../models/ConfigAnyCarStatusDto';
import type { ConfigTipPositionDto } from '../models/ConfigTipPositionDto';
import type { IBaseResponse } from '../models/IBaseResponse';

import type { CancelablePromise } from '../core/CancelablePromise';
import { OpenAPI } from '../core/OpenAPI';
import { request as __request } from '../core/request';

export class BookingConfigsService {

  /**
   * Get booking config
   * @returns any
   * @throws ApiError
   */
  public static bookingConfigControllerGetBookingConfig(): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/booking-configs',
    });
  }

  /**
   * Update tip config position
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static bookingConfigControllerUpdateBookingConfig(
    requestBody: ConfigTipPositionDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'PUT',
      url: '/v1/booking-configs/tip-position',
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Get tip position logs
   * @param limit
   * @param offset
   * @param order Format: fieldName:[asc,desc]
   * @returns any
   * @throws ApiError
   */
  public static bookingConfigControllerGetTipPositionLogs(
    limit: number = 10,
    offset: number,
    order?: string | null,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/booking-configs/tip-position-logs',
      query: {
        'limit': limit,
        'offset': offset,
        'order': order,
      },
    });
  }

  /**
   * Update any car config
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static bookingConfigControllerUpdateAnyCarConfig(
    requestBody: ConfigAnyCarDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'PUT',
      url: '/v1/booking-configs/any-car',
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Update any car status config
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static bookingConfigControllerUpdateAnyCarStatusConfig(
    requestBody: ConfigAnyCarStatusDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'PUT',
      url: '/v1/booking-configs/any-car-status',
      body: requestBody,
      mediaType: 'application/json',
    });
  }

}
