/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { CreateFixedFareDto } from '../models/CreateFixedFareDto';
import type { CreateRateFeeDto } from '../models/CreateRateFeeDto';
import type { CreateServiceFeeDto } from '../models/CreateServiceFeeDto';
import type { IBaseResponse } from '../models/IBaseResponse';
import type { UpdateFixedFareDto } from '../models/UpdateFixedFareDto';
import type { UpdateFixedFareStatusDto } from '../models/UpdateFixedFareStatusDto';
import type { UpdateServiceFeeStatusDto } from '../models/UpdateServiceFeeStatusDto';

import type { CancelablePromise } from '../core/CancelablePromise';
import { OpenAPI } from '../core/OpenAPI';
import { request as __request } from '../core/request';

export class FeeConfigurationService {

  /**
   * Get list service fee
   * @param limit
   * @param offset
   * @param q
   * @param vehicleType
   * @param status
   * @param createdByIds
   * @param perType
   * @param order Format: fieldName:[asc,desc]
   * @returns any
   * @throws ApiError
   */
  public static feeConfigurationControllerGetServiceFees(
    limit: number = 10,
    offset: number,
    q?: string,
    vehicleType?: string,
    status?: string,
    createdByIds?: string,
    perType?: string,
    order?: string | null,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/fee-configurations/service-fee',
      query: {
        'limit': limit,
        'offset': offset,
        'q': q,
        'vehicleType': vehicleType,
        'status': status,
        'createdByIds': createdByIds,
        'perType': perType,
        'order': order,
      },
    });
  }

  /**
   * Create a service fee
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static feeConfigurationControllerCreateServiceFee(
    requestBody: CreateServiceFeeDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'POST',
      url: '/v1/fee-configurations/service-fee',
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Get service fee detail
   * @param id
   * @returns any
   * @throws ApiError
   */
  public static feeConfigurationControllerGetServiceFee(
    id: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/fee-configurations/service-fee/{id}',
      path: {
        'id': id,
      },
    });
  }

  /**
   * Update a service fee
   * @param id
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static feeConfigurationControllerUpdateServiceFee(
    id: string,
    requestBody: CreateServiceFeeDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'PUT',
      url: '/v1/fee-configurations/service-fee/{id}',
      path: {
        'id': id,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Delete a service fee
   * @param id
   * @returns any
   * @throws ApiError
   */
  public static feeConfigurationControllerDeleteServiceFee(
    id: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'DELETE',
      url: '/v1/fee-configurations/service-fee/{id}',
      path: {
        'id': id,
      },
    });
  }

  /**
   * Update status service fee
   * @param id
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static feeConfigurationControllerUpdateStatusServiceFee(
    id: string,
    requestBody: UpdateServiceFeeStatusDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'PUT',
      url: '/v1/fee-configurations/service-fee/{id}/status',
      path: {
        'id': id,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Get list rate fee
   * @param limit
   * @param offset
   * @param q
   * @param vehicleType
   * @param status
   * @param createdByIds
   * @param perType
   * @param order Format: fieldName:[asc,desc]
   * @returns any
   * @throws ApiError
   */
  public static feeConfigurationControllerGetRateFees(
    limit: number = 10,
    offset: number,
    q?: string,
    vehicleType?: string,
    status?: string,
    createdByIds?: string,
    perType?: string,
    order?: string | null,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/fee-configurations/rate-fee',
      query: {
        'limit': limit,
        'offset': offset,
        'q': q,
        'vehicleType': vehicleType,
        'status': status,
        'createdByIds': createdByIds,
        'perType': perType,
        'order': order,
      },
    });
  }

  /**
   * Create a rate fee
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static feeConfigurationControllerCreateRateFee(
    requestBody: CreateRateFeeDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'POST',
      url: '/v1/fee-configurations/rate-fee',
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Get rate fee detail
   * @param id
   * @returns any
   * @throws ApiError
   */
  public static feeConfigurationControllerGetRateFee(
    id: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/fee-configurations/rate-fee/{id}',
      path: {
        'id': id,
      },
    });
  }

  /**
   * Update a rate fee
   * @param id
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static feeConfigurationControllerUpdateRateFee(
    id: string,
    requestBody: CreateRateFeeDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'PUT',
      url: '/v1/fee-configurations/rate-fee/{id}',
      path: {
        'id': id,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Delete a rate fee
   * @param id
   * @returns any
   * @throws ApiError
   */
  public static feeConfigurationControllerDeleteRateFee(
    id: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'DELETE',
      url: '/v1/fee-configurations/rate-fee/{id}',
      path: {
        'id': id,
      },
    });
  }

  /**
   * Update a status rate fee
   * @param id
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static feeConfigurationControllerUpdateStatusRateFee(
    id: string,
    requestBody: UpdateServiceFeeStatusDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'PUT',
      url: '/v1/fee-configurations/rate-fee/{id}/status',
      path: {
        'id': id,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Get rate fee price
   * @returns any
   * @throws ApiError
   */
  public static feeConfigurationControllerGetRateFeePrice(): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/fee-configurations/rate-fee-price',
    });
  }

  /**
   * Get list fixed fares
   * @param limit
   * @param offset
   * @param q
   * @param vehicleType
   * @param status
   * @param createdByIds
   * @param zones
   * @param order Format: fieldName:[asc,desc]
   * @returns any
   * @throws ApiError
   */
  public static feeConfigurationControllerGetFixedFares(
    limit: number = 10,
    offset: number,
    q?: string,
    vehicleType?: string,
    status?: string,
    createdByIds?: string,
    zones?: string,
    order?: string | null,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/fee-configurations/fixed-fare',
      query: {
        'limit': limit,
        'offset': offset,
        'q': q,
        'vehicleType': vehicleType,
        'status': status,
        'createdByIds': createdByIds,
        'zones': zones,
        'order': order,
      },
    });
  }

  /**
   * Create a fixed fare
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static feeConfigurationControllerCreateFixedFare(
    requestBody: CreateFixedFareDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'POST',
      url: '/v1/fee-configurations/fixed-fare',
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Get fixed fare detail
   * @param id
   * @returns any
   * @throws ApiError
   */
  public static feeConfigurationControllerGetFixedFare(
    id: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/v1/fee-configurations/fixed-fare/{id}',
      path: {
        'id': id,
      },
    });
  }

  /**
   * Update a fixed fare
   * @param id
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static feeConfigurationControllerUpdateFixedFare(
    id: string,
    requestBody: UpdateFixedFareDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'PUT',
      url: '/v1/fee-configurations/fixed-fare/{id}',
      path: {
        'id': id,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

  /**
   * Delete a fixed fare
   * @param id
   * @returns any
   * @throws ApiError
   */
  public static feeConfigurationControllerDeleteFixedFare(
    id: string,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'DELETE',
      url: '/v1/fee-configurations/fixed-fare/{id}',
      path: {
        'id': id,
      },
    });
  }

  /**
   * Update status fixed fare
   * @param id
   * @param requestBody
   * @returns any
   * @throws ApiError
   */
  public static feeConfigurationControllerUpdateStatusFixedFare(
    id: string,
    requestBody: UpdateFixedFareStatusDto,
  ): CancelablePromise<IBaseResponse> {
    return __request(OpenAPI, {
      method: 'PUT',
      url: '/v1/fee-configurations/fixed-fare/{id}/status',
      path: {
        'id': id,
      },
      body: requestBody,
      mediaType: 'application/json',
    });
  }

}
