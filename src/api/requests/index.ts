/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
export { ApiError } from './core/ApiError';
export { CancelablePromise, CancelError } from './core/CancelablePromise';
export { OpenAPI } from './core/OpenAPI';
export type { OpenAPIConfig } from './core/OpenAPI';

export type { AddressDto } from './models/AddressDto';
export type { AdminEntity } from './models/AdminEntity';
export { AdminNotificationEntity } from './models/AdminNotificationEntity';
export type { AppConfigEntity } from './models/AppConfigEntity';
export type { AppLoyalPointConfigEntity } from './models/AppLoyalPointConfigEntity';
export { AppSubscriptionEntity } from './models/AppSubscriptionEntity';
export type { AreaDto } from './models/AreaDto';
export type { ArticleEntity } from './models/ArticleEntity';
export type { ArticleParamDto } from './models/ArticleParamDto';
export type { AvailableCarEntity } from './models/AvailableCarEntity';
export type { Banner } from './models/Banner';
export { BannerEntity } from './models/BannerEntity';
export type { BannerImageType } from './models/BannerImageType';
export { BannerParamDto } from './models/BannerParamDto';
export { BaseBookingEntity } from './models/BaseBookingEntity';
export type { BaseTicketEntity } from './models/BaseTicketEntity';
export type { BillToType } from './models/BillToType';
export type { BlogEntity } from './models/BlogEntity';
export type { BookingAddressType } from './models/BookingAddressType';
export { BookingConfigEntity } from './models/BookingConfigEntity';
export type { BookingDriverEntity } from './models/BookingDriverEntity';
export { BookingEntity } from './models/BookingEntity';
export { BookingProgressEntity } from './models/BookingProgressEntity';
export type { BookingRateFeeEntity } from './models/BookingRateFeeEntity';
export type { BookingReviewType } from './models/BookingReviewType';
export { BookingSharingEntity } from './models/BookingSharingEntity';
export type { BookingSourceStat } from './models/BookingSourceStat';
export type { Boolean } from './models/Boolean';
export type { BusinessAdminEntity } from './models/BusinessAdminEntity';
export type { BusinessBaseEntity } from './models/BusinessBaseEntity';
export type { BusinessBillToEntity } from './models/BusinessBillToEntity';
export type { BusinessEntity } from './models/BusinessEntity';
export { CancelBookingDto } from './models/CancelBookingDto';
export { CancelReason } from './models/CancelReason';
export type { CancelSubscriptionDto } from './models/CancelSubscriptionDto';
export type { CardEntity } from './models/CardEntity';
export type { CheckWorkingAreaDto } from './models/CheckWorkingAreaDto';
export { CodeBaseEntity } from './models/CodeBaseEntity';
export { CodeEntity } from './models/CodeEntity';
export type { ConfigAnyCarDto } from './models/ConfigAnyCarDto';
export type { ConfigAnyCarStatusDto } from './models/ConfigAnyCarStatusDto';
export { ConfigTipPositionDto } from './models/ConfigTipPositionDto';
export { ConfirmOtpDto } from './models/ConfirmOtpDto';
export type { ConfirmRegisterDto } from './models/ConfirmRegisterDto';
export type { ContentType } from './models/ContentType';
export type { CoordinatesDto } from './models/CoordinatesDto';
export { CouponBaseEntity } from './models/CouponBaseEntity';
export { CouponEntity } from './models/CouponEntity';
export { CouponLogActivityEntity } from './models/CouponLogActivityEntity';
export type { CouponLogEntity } from './models/CouponLogEntity';
export type { CouponLogHistoryEntity } from './models/CouponLogHistoryEntity';
export type { CouponStatisticEntity } from './models/CouponStatisticEntity';
export { CouponStoreEntity } from './models/CouponStoreEntity';
export type { CreateAdminOfBusinessDto } from './models/CreateAdminOfBusinessDto';
export type { CreateArticleDto } from './models/CreateArticleDto';
export { CreateBookingWithCodeDto } from './models/CreateBookingWithCodeDto';
export { CreateBookingWithPaymentDto } from './models/CreateBookingWithPaymentDto';
export type { CreateBusinessBillToDto } from './models/CreateBusinessBillToDto';
export type { CreateBusinessDto } from './models/CreateBusinessDto';
export { CreateCodeDto } from './models/CreateCodeDto';
export { CreateCouponDto } from './models/CreateCouponDto';
export type { CreatedByEntity } from './models/CreatedByEntity';
export type { CreatedByUserEntity } from './models/CreatedByUserEntity';
export type { CreateDepartmentDto } from './models/CreateDepartmentDto';
export type { CreateDepartmentGroupDto } from './models/CreateDepartmentGroupDto';
export type { CreateDepartmentRoleDto } from './models/CreateDepartmentRoleDto';
export { CreateDiscountDto } from './models/CreateDiscountDto';
export type { CreateEmployeeDto } from './models/CreateEmployeeDto';
export type { CreateEmployeeOfBusinessDto } from './models/CreateEmployeeOfBusinessDto';
export type { CreateEstimationDto } from './models/CreateEstimationDto';
export { CreateFixedFareDto } from './models/CreateFixedFareDto';
export { CreateLocationDto } from './models/CreateLocationDto';
export { CreateNotificationDto } from './models/CreateNotificationDto';
export type { CreateNotificationTokenDto } from './models/CreateNotificationTokenDto';
export type { CreatePickupSpecificAresDto } from './models/CreatePickupSpecificAresDto';
export { CreateRateFeeDto } from './models/CreateRateFeeDto';
export { CreateRefundPaymentDto } from './models/CreateRefundPaymentDto';
export { CreateReviewDto } from './models/CreateReviewDto';
export type { CreateRoleDto } from './models/CreateRoleDto';
export { CreateServiceFeeDto } from './models/CreateServiceFeeDto';
export type { CreateSubLocationDto } from './models/CreateSubLocationDto';
export { CreateSubscriptionCouponDto } from './models/CreateSubscriptionCouponDto';
export type { CreateSubscriptionDto } from './models/CreateSubscriptionDto';
export { CreateSubscriptionPlanDto } from './models/CreateSubscriptionPlanDto';
export type { CreateSubscriptionV2Dto } from './models/CreateSubscriptionV2Dto';
export { CreateSupportTicketDto } from './models/CreateSupportTicketDto';
export { CreateUploadExcelPresignedUrlDto } from './models/CreateUploadExcelPresignedUrlDto';
export { CreateUploadPresignedUrlDto } from './models/CreateUploadPresignedUrlDto';
export type { CurrentLocationType } from './models/CurrentLocationType';
export type { DepartmentBaseEntity } from './models/DepartmentBaseEntity';
export type { DepartmentEntity } from './models/DepartmentEntity';
export type { DepartmentGroupEntity } from './models/DepartmentGroupEntity';
export type { DepartmentRoleEntity } from './models/DepartmentRoleEntity';
export { DiscountEntity } from './models/DiscountEntity';
export type { DisplayBannerDto } from './models/DisplayBannerDto';
export type { DriverEntity } from './models/DriverEntity';
export type { EmployeeAdminEntity } from './models/EmployeeAdminEntity';
export type { EmployeeBaseEntity } from './models/EmployeeBaseEntity';
export { EmployeeCodeAdminEntity } from './models/EmployeeCodeAdminEntity';
export { EmployeeCodeEntity } from './models/EmployeeCodeEntity';
export type { EmployeeEntity } from './models/EmployeeEntity';
export { EstimateAmountEntity } from './models/EstimateAmountEntity';
export type { EstimateAmountEntityInput } from './models/EstimateAmountEntityInput';
export type { EstimationEntity } from './models/EstimationEntity';
export { FixedFareEntity } from './models/FixedFareEntity';
export { ForgotPasswordInputDto } from './models/ForgotPasswordInputDto';
export type { GeoLocationType } from './models/GeoLocationType';
export type { IBaseResponse } from './models/IBaseResponse';
export { IBookingReport } from './models/IBookingReport';
export type { IBookingSourceReport } from './models/IBookingSourceReport';
export type { IContextUser } from './models/IContextUser';
export type { ILoginResponse } from './models/ILoginResponse';
export type { IMessageResponse } from './models/IMessageResponse';
export type { IMeta } from './models/IMeta';
export type { ImportEmailDto } from './models/ImportEmailDto';
export type { ImportEmailEntity } from './models/ImportEmailEntity';
export type { IOverallDashBoard } from './models/IOverallDashBoard';
export type { IOverallDashBoardBookingRevenue } from './models/IOverallDashBoardBookingRevenue';
export type { IOverallDashBoardDetail } from './models/IOverallDashBoardDetail';
export type { IOverallDashBoardOfBusiness } from './models/IOverallDashBoardOfBusiness';
export type { IPaginatedResponse } from './models/IPaginatedResponse';
export { IPermissionRole } from './models/IPermissionRole';
export { IPermissionRoleParam } from './models/IPermissionRoleParam';
export { IRevenueReport } from './models/IRevenueReport';
export type { IssueReasonType } from './models/IssueReasonType';
export type { ITopBusiness } from './models/ITopBusiness';
export type { ITopUser } from './models/ITopUser';
export type { IUserCreatedBy } from './models/IUserCreatedBy';
export type { JoinBusinessDto } from './models/JoinBusinessDto';
export type { LocationDto } from './models/LocationDto';
export { LocationEntity } from './models/LocationEntity';
export type { LoginAppleDto } from './models/LoginAppleDto';
export type { LoginGoogleDto } from './models/LoginGoogleDto';
export { LoginInputDto } from './models/LoginInputDto';
export type { LoyalPointConfigEntity } from './models/LoyalPointConfigEntity';
export type { LoyalPointRewardEntity } from './models/LoyalPointRewardEntity';
export { MyCouponEntity } from './models/MyCouponEntity';
export { NearbyVehiclesEntity } from './models/NearbyVehiclesEntity';
export { NotificationEntity } from './models/NotificationEntity';
export { NotificationTokenEntity } from './models/NotificationTokenEntity';
export type { NotificationUnseenEntity } from './models/NotificationUnseenEntity';
export { PermissionEntity } from './models/PermissionEntity';
export type { PickUpAreaLocationType } from './models/PickUpAreaLocationType';
export { ProviderStat } from './models/ProviderStat';
export { RateFeeEntity } from './models/RateFeeEntity';
export type { RateFeePriceEntity } from './models/RateFeePriceEntity';
export type { ReasonLanguage } from './models/ReasonLanguage';
export type { RecentLocationDto } from './models/RecentLocationDto';
export type { RecentLocationEntity } from './models/RecentLocationEntity';
export type { RedeemCouponDto } from './models/RedeemCouponDto';
export type { ReferralSummaryEntity } from './models/ReferralSummaryEntity';
export type { RefreshTokenInputDto } from './models/RefreshTokenInputDto';
export type { RefundProgressEntity } from './models/RefundProgressEntity';
export type { RegisterInputDto } from './models/RegisterInputDto';
export { RequestOtpDto } from './models/RequestOtpDto';
export type { RequestProgressEntity } from './models/RequestProgressEntity';
export { ResendCodeInputDto } from './models/ResendCodeInputDto';
export { ResetPasswordInputDto } from './models/ResetPasswordInputDto';
export type { ResolveTicketDto } from './models/ResolveTicketDto';
export type { RoleEntity } from './models/RoleEntity';
export { SavedLocationDto } from './models/SavedLocationDto';
export { SavedLocationEntity } from './models/SavedLocationEntity';
export { SeenAllNotificationsDto } from './models/SeenAllNotificationsDto';
export { ServiceFeeEntity } from './models/ServiceFeeEntity';
export type { ServiceFeeType } from './models/ServiceFeeType';
export type { SimpleZoneEntity } from './models/SimpleZoneEntity';
export type { SpecificTimeType } from './models/SpecificTimeType';
export type { StatChange } from './models/StatChange';
export type { StatisticsEntity } from './models/StatisticsEntity';
export type { String } from './models/String';
export type { StripeSubscriptionPaymentDataType } from './models/StripeSubscriptionPaymentDataType';
export { SubLocationType } from './models/SubLocationType';
export type { SubscribeSubscriptionDto } from './models/SubscribeSubscriptionDto';
export type { SubscriptionBaseEntity } from './models/SubscriptionBaseEntity';
export type { SubscriptionCouponBaseEntity } from './models/SubscriptionCouponBaseEntity';
export { SubscriptionCouponEntity } from './models/SubscriptionCouponEntity';
export { SubscriptionCouponLogActivityEntity } from './models/SubscriptionCouponLogActivityEntity';
export type { SubscriptionCouponMetricEntity } from './models/SubscriptionCouponMetricEntity';
export { SubscriptionDashboardChartEntity } from './models/SubscriptionDashboardChartEntity';
export { SubscriptionEntity } from './models/SubscriptionEntity';
export type { SubscriptionOverallEntity } from './models/SubscriptionOverallEntity';
export { SubscriptionOverallItem } from './models/SubscriptionOverallItem';
export { SubscriptionPlanEntity } from './models/SubscriptionPlanEntity';
export type { SubscriptionTransactionDataType } from './models/SubscriptionTransactionDataType';
export { SubscriptionTransactionEntity } from './models/SubscriptionTransactionEntity';
export type { SubscriptionTransactionMetric } from './models/SubscriptionTransactionMetric';
export type { SuccessfulReferralEntity } from './models/SuccessfulReferralEntity';
export { SupportProgressEntity } from './models/SupportProgressEntity';
export { SupportTicketEntity } from './models/SupportTicketEntity';
export type { TargetSpecificUser } from './models/TargetSpecificUser';
export { TipPositionLogEntity } from './models/TipPositionLogEntity';
export type { TrackingBookingSource } from './models/TrackingBookingSource';
export type { TrackingBookingSourceResponse } from './models/TrackingBookingSourceResponse';
export type { TrackingSourceProvider } from './models/TrackingSourceProvider';
export type { TrackingValue } from './models/TrackingValue';
export type { TripEntity } from './models/TripEntity';
export type { UpdateAdminOfBusinessDto } from './models/UpdateAdminOfBusinessDto';
export type { UpdateAvailableCarDto } from './models/UpdateAvailableCarDto';
export type { UpdateBusinessDto } from './models/UpdateBusinessDto';
export type { UpdateBusinessStatusDto } from './models/UpdateBusinessStatusDto';
export type { UpdateCodeStatusDto } from './models/UpdateCodeStatusDto';
export { UpdateCouponDto } from './models/UpdateCouponDto';
export { UpdateCouponStatusDto } from './models/UpdateCouponStatusDto';
export type { UpdateDepartmentDto } from './models/UpdateDepartmentDto';
export type { UpdateDepartmentGroupDto } from './models/UpdateDepartmentGroupDto';
export type { UpdateDepartmentRoleDto } from './models/UpdateDepartmentRoleDto';
export type { UpdateDepartmentStatusDto } from './models/UpdateDepartmentStatusDto';
export type { UpdateDisplayArticleDto } from './models/UpdateDisplayArticleDto';
export type { UpdateEmployeeDto } from './models/UpdateEmployeeDto';
export type { UpdateEmployeeOfBusinessDto } from './models/UpdateEmployeeOfBusinessDto';
export type { UpdateEmployeeStatusDto } from './models/UpdateEmployeeStatusDto';
export { UpdateFixedFareDto } from './models/UpdateFixedFareDto';
export { UpdateFixedFareStatusDto } from './models/UpdateFixedFareStatusDto';
export { UpdateLocaleDto } from './models/UpdateLocaleDto';
export { UpdateLocationDto } from './models/UpdateLocationDto';
export { UpdateLocationStatusDto } from './models/UpdateLocationStatusDto';
export type { UpdateLoyalPointConfigDto } from './models/UpdateLoyalPointConfigDto';
export type { UpdateNotificationDto } from './models/UpdateNotificationDto';
export type { UpdatePositionsCouponDto } from './models/UpdatePositionsCouponDto';
export type { UpdateReferredUserDto } from './models/UpdateReferredUserDto';
export type { UpdateRoleDto } from './models/UpdateRoleDto';
export { UpdateServiceFeeStatusDto } from './models/UpdateServiceFeeStatusDto';
export type { UpdateStatusArticleDto } from './models/UpdateStatusArticleDto';
export type { UpdateStatusBannerDto } from './models/UpdateStatusBannerDto';
export type { UpdateStatusNotificationDto } from './models/UpdateStatusNotificationDto';
export type { UpdateSubscriptionCardDto } from './models/UpdateSubscriptionCardDto';
export { UpdateSubscriptionCouponDto } from './models/UpdateSubscriptionCouponDto';
export type { UpdateSubscriptionDto } from './models/UpdateSubscriptionDto';
export { UpdateSubscriptionStatusDto } from './models/UpdateSubscriptionStatusDto';
export type { UpdateSubscriptionV2Dto } from './models/UpdateSubscriptionV2Dto';
export { UpdateUserDto } from './models/UpdateUserDto';
export type { UpdateUserPasswordDto } from './models/UpdateUserPasswordDto';
export type { UpdateUserStatusDto } from './models/UpdateUserStatusDto';
export type { UploadPresignedUrlEntity } from './models/UploadPresignedUrlEntity';
export type { UpsertUserDto } from './models/UpsertUserDto';
export type { UserBaseEntity } from './models/UserBaseEntity';
export { UserEntity } from './models/UserEntity';
export { UserPointEntity } from './models/UserPointEntity';
export type { UserResolvedByEntity } from './models/UserResolvedByEntity';
export type { UserSubscriptionCouponEntity } from './models/UserSubscriptionCouponEntity';
export { UserSubscriptionEntity } from './models/UserSubscriptionEntity';
export type { VehicleEntity } from './models/VehicleEntity';
export { VerifyCodeInputDto } from './models/VerifyCodeInputDto';
export type { VersionAppEntity } from './models/VersionAppEntity';

export { AdminService } from './services/AdminService';
export { AppService } from './services/AppService';
export { ArticlesService } from './services/ArticlesService';
export { AuthService } from './services/AuthService';
export { AvailableCarsService } from './services/AvailableCarsService';
export { BannersService } from './services/BannersService';
export { BookingConfigsService } from './services/BookingConfigsService';
export { BookingsService } from './services/BookingsService';
export { BusinessesService } from './services/BusinessesService';
export { CodesService } from './services/CodesService';
export { ConfigsService } from './services/ConfigsService';
export { CouponsService } from './services/CouponsService';
export { DashboardService } from './services/DashboardService';
export { DefaultService } from './services/DefaultService';
export { DepartmentGroupsService } from './services/DepartmentGroupsService';
export { DepartmentRolesService } from './services/DepartmentRolesService';
export { DepartmentsService } from './services/DepartmentsService';
export { DriverService } from './services/DriverService';
export { EmployeesService } from './services/EmployeesService';
export { FeeConfigurationService } from './services/FeeConfigurationService';
export { LocationsService } from './services/LocationsService';
export { LoyalPointService } from './services/LoyalPointService';
export { NotificationsService } from './services/NotificationsService';
export { NotificationTokensService } from './services/NotificationTokensService';
export { PermissionsService } from './services/PermissionsService';
export { RolesService } from './services/RolesService';
export { StoragesService } from './services/StoragesService';
export { SubscriptionsService } from './services/SubscriptionsService';
export { SupportTicketService } from './services/SupportTicketService';
export { UsersService } from './services/UsersService';
export { VersionAppService } from './services/VersionAppService';
