/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

export type UserPointEntity = {
  type: UserPointEntity.type;
  amount: number;
  createdAt: string;
};

export namespace UserPointEntity {

  export enum type {
    FIRST_RIDE = 'FIRST_RIDE',
    COMPLETED_RIDE = 'COMPLETED_RIDE',
    SUCCESSFUL_REFERRAL = 'SUCCESSFUL_REFERRAL',
    REFERRAL_BONUS_UNLOCKED = 'REFERRAL_BONUS_UNLOCKED',
    REDEEM_POINT = 'REDEEM_POINT',
  }


}

