/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { DiscountEntity } from './DiscountEntity';

export type SubscriptionPlanEntity = {
  id: string;
  name: string;
  amount: number;
  recurringType: SubscriptionPlanEntity.recurringType;
  discount: DiscountEntity;
};

export namespace SubscriptionPlanEntity {

  export enum recurringType {
    MONTHLY_1 = 'MONTHLY_1',
    YEARLY_1 = 'YEARLY_1',
  }


}

