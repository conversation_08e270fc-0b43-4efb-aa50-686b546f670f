/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

export type SubscriptionDashboardChartEntity = {
  totalPurchased: number;
  totalRevenue: number;
  date: string;
  subscriptionName: string;
  recurringType: SubscriptionDashboardChartEntity.recurringType;
};

export namespace SubscriptionDashboardChartEntity {

  export enum recurringType {
    MONTHLY_1 = 'MONTHLY_1',
    YEARLY_1 = 'YEARLY_1',
  }


}

