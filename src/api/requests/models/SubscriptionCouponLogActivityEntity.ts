/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { UserBaseEntity } from './UserBaseEntity';

export type SubscriptionCouponLogActivityEntity = {
  actionBy: UserBaseEntity;
  activity: SubscriptionCouponLogActivityEntity.activity;
  createdAt: string;
};

export namespace SubscriptionCouponLogActivityEntity {

  export enum activity {
    CREATE = 'CREATE',
    UPDATE = 'UPDATE',
    ACTIVE = 'ACTIVE',
    DEACTIVATE = 'DEACTIVATE',
  }


}

