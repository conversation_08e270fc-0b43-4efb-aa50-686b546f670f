/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { CreatePickupSpecificAresDto } from './CreatePickupSpecificAresDto';
import type { SpecificTimeType } from './SpecificTimeType';

export type UpdateCouponDto = {
  title: string;
  titleFr: string;
  code: string;
  description?: string;
  descriptionFr?: string;
  validFrom: string;
  validTo: string;
  vehicleTypes: Array<'ALL' | 'TYPICAL_CAR' | 'ELECTRIC_CAR' | 'VAN' | 'ACCESSIBLE_VAN' | 'ANY_CAR'>;
  type: UpdateCouponDto.type;
  value: number;
  maxDiscountLimit?: number;
  redeemMethod: UpdateCouponDto.redeemMethod;
  totalUsageLimit: number;
  limitPerUser: number;
  paymentMethods?: Array<'ALL' | 'CARD' | 'CASH'>;
  minSpendForAdoption?: number;
  redeemPoint?: number;
  targetUserType: UpdateCouponDto.targetUserType;
  targetSpecificUserIds?: Array<string> | null;
  targetUserProfile: UpdateCouponDto.targetUserProfile;
  applyForFromSpecificTime?: SpecificTimeType;
  applyForToSpecificTime?: SpecificTimeType;
  applyForRepeatTime?: Array<number> | null;
  applyForPickUpAreaCoordinates?: Array<number> | null;
  applyForPickUpAreaRadius?: number | null;
  applyForDropOffAreaCoordinates?: Array<number> | null;
  applyForDropOffAreaRadius?: number | null;
  applyForPickUpArea?: Array<CreatePickupSpecificAresDto> | null;
  applyForDropOffArea?: Array<CreatePickupSpecificAresDto> | null;
  topUpPercent?: number;
};

export namespace UpdateCouponDto {

  export enum type {
    PERCENTAGE = 'PERCENTAGE',
    AMOUNT = 'AMOUNT',
    DROP_RATE = 'DROP_RATE',
  }

  export enum redeemMethod {
    FREE = 'FREE',
    MANUAL_INPUT = 'MANUAL_INPUT',
    REDEEM_BY_POINT = 'REDEEM_BY_POINT',
  }

  export enum targetUserType {
    ALL = 'ALL',
    PAID_USER = 'PAID_USER',
    NEW_USER = 'NEW_USER',
    SPECIFIC_USER = 'SPECIFIC_USER',
  }

  export enum targetUserProfile {
    ALL = 'ALL',
    BUSINESS = 'BUSINESS',
    INDIVIDUAL = 'INDIVIDUAL',
  }


}

