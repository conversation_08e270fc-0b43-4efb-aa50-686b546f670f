/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { GeoLocationType } from './GeoLocationType';

export type SavedLocationDto = {
  name: string;
  address: string;
  type: SavedLocationDto.type;
  coordinates: GeoLocationType;
};

export namespace SavedLocationDto {

  export enum type {
    HOME = 'HOME',
    WORK = 'WORK',
    FAVORITE = 'FAVORITE',
    NORMAL = 'NORMAL',
    RECENT = 'RECENT',
  }


}

