/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

export type PaymentMethodEntity = {
  type: PaymentMethodEntity.type;
  cardType: PaymentMethodEntity.cardType;
  paymentMethodId: string;
};

export namespace PaymentMethodEntity {

  export enum type {
    ALL = 'ALL',
    CARD = 'CARD',
    CASH = 'CASH',
  }

  export enum cardType {
    AMEX = 'amex',
    DINERS = 'diners',
    DISCOVER = 'discover',
    EFTPOS_AU = 'eftpos_au',
    JCB = 'jcb',
    MASTERCARD = 'mastercard',
    UNIONPAY = 'unionpay',
    VISA = 'visa',
    UNKNOWN = 'unknown',
    AMEX_EXPRESS_CHECKOUT = 'amex_express_checkout',
    APPLE_PAY = 'apple_pay',
    GOOGLE_PAY = 'google_pay',
    LINK = 'link',
    MASTERPASS = 'masterpass',
    SAMSUNG_PAY = 'samsung_pay',
    VISA_CHECKOUT = 'visa_checkout',
  }


}

