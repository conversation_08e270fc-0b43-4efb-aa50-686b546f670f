/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { BaseBookingEntity } from './BaseBookingEntity';
import type { BaseTicketEntity } from './BaseTicketEntity';
import type { RefundProgressEntity } from './RefundProgressEntity';
import type { RequestProgressEntity } from './RequestProgressEntity';
import type { UserResolvedByEntity } from './UserResolvedByEntity';

export type SupportProgressEntity = {
  bookingId: string;
  booking: BaseBookingEntity;
  ticket: BaseTicketEntity;
  description: string;
  refund?: RefundProgressEntity;
  request?: RequestProgressEntity;
  progressType: SupportProgressEntity.progressType;
  userResolvedBy?: UserResolvedByEntity;
  createdAt: string;
};

export namespace SupportProgressEntity {

  export enum progressType {
    NEW = 'NEW',
    SOLVED = 'SOLVED',
    CANCELED = 'CANCELED',
    PENDING = 'PENDING',
    FAILED = 'FAILED',
    SUCCESSFULLY = 'SUCCESSFULLY',
  }


}

