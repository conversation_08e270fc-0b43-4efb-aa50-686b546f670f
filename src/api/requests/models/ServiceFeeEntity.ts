/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { UserBaseEntity } from './UserBaseEntity';

export type ServiceFeeEntity = {
  id: string;
  name: string;
  nameFr: string;
  amount: number;
  vehicleTypes: Array<'ALL' | 'TYPICAL_CAR' | 'ELECTRIC_CAR' | 'VAN' | 'ACCESSIBLE_VAN' | 'ANY_CAR'>;
  perType: ServiceFeeEntity.perType;
  applyFrom: string;
  status: ServiceFeeEntity.status;
  createdBy: UserBaseEntity;
  createdById: string;
  specificPickUpAreas: Array<string>;
  specificDropOffAreas: Array<string>;
  totalPickUpAreas: number;
  totalDropOffAreas: number;
  isApplySpecificArea: boolean;
};

export namespace ServiceFeeEntity {

  export enum perType {
    RIDE = 'RIDE',
    KILOMETER = 'KILOMETER',
  }

  export enum status {
    ACTIVE = 'ACTIVE',
    INACTIVE = 'INACTIVE',
  }


}

