/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { SubscriptionPlanEntity } from './SubscriptionPlanEntity';

export type SubscriptionEntity = {
  id: string;
  name: string;
  description: string;
  usageInstruction: string;
  termsOfUse: string;
  amount: number;
  status: SubscriptionEntity.status;
  couponCount: number;
  createdAt: string;
  updatedAt: string;
  descriptionFr: string;
  usageInstructionFr: string;
  termsOfUseFr: string;
  pricingPlans: Array<SubscriptionPlanEntity>;
};

export namespace SubscriptionEntity {

  export enum status {
    NOT_STARTED = 'NOT_STARTED',
    IN_PROGRESS = 'IN_PROGRESS',
    CANCELLED = 'CANCELLED',
  }


}

