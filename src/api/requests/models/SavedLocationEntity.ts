/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { GeoLocationType } from './GeoLocationType';

export type SavedLocationEntity = {
  id: string;
  name: string;
  address: string;
  type: SavedLocationEntity.type;
  coordinates: GeoLocationType;
};

export namespace SavedLocationEntity {

  export enum type {
    HOME = 'HOME',
    WORK = 'WORK',
    FAVORITE = 'FAVORITE',
    NORMAL = 'NORMAL',
    RECENT = 'RECENT',
  }


}

