/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { BaseBookingEntity } from './BaseBookingEntity';
import type { UserResolvedByEntity } from './UserResolvedByEntity';

export type SupportTicketEntity = {
  id: string;
  ticketCode: number;
  bookingId: string;
  booking: BaseBookingEntity;
  supportNote: string;
  resolvedNote: string;
  issueType: SupportTicketEntity.issueType;
  resolvedBy: UserResolvedByEntity;
  resolvedAt: string;
  isSolved: boolean;
  status: SupportTicketEntity.status;
  createdAt: string;
};

export namespace SupportTicketEntity {

  export enum issueType {
    PAYMENT_ISSUE = 'PAYMENT_ISSUE',
    LOST_ITEM = 'LOST_ITEM',
    OTHER = 'OTHER',
  }

  export enum status {
    OPEN = 'OPEN',
    CLOSED = 'CLOSED',
  }


}

