/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { SpecificTimeType } from './SpecificTimeType';
import type { UserBaseEntity } from './UserBaseEntity';

export type RateFeeEntity = {
  id: string;
  name: string;
  nameFr: string;
  kmRate: number;
  vehicleTypes: Array<'ALL' | 'TYPICAL_CAR' | 'ELECTRIC_CAR' | 'VAN' | 'ACCESSIBLE_VAN' | 'ANY_CAR'>;
  applyDateType: RateFeeEntity.applyDateType;
  startApplyDate?: string;
  endApplyDate?: string;
  applyRepeatDay?: Array<any[]>;
  status: RateFeeEntity.status;
  startTime?: SpecificTimeType;
  endTime?: SpecificTimeType;
  createdBy: UserBaseEntity;
  specificPickUpAreas: Array<string>;
  specificDropOffAreas: Array<string>;
  totalPickUpAreas: number;
  totalDropOffAreas: number;
  isApplySpecificArea: boolean;
};

export namespace RateFeeEntity {

  export enum applyDateType {
    ALL = 'ALL',
    SPECIFIC_DATE = 'SPECIFIC_DATE',
    DAY = 'DAY',
  }

  export enum status {
    ACTIVE = 'ACTIVE',
    INACTIVE = 'INACTIVE',
  }


}

