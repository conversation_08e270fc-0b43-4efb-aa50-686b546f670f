/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { IUserCreatedBy } from './IUserCreatedBy';

export type ArticleEntity = {
  id: string;
  titleEn: string;
  titleFr: string;
  descriptionEn: string;
  descriptionFr: string;
  thumbnailEn: string;
  thumbnailFr: string;
  urlEn: string;
  urlFr: string;
  createdByUser: IUserCreatedBy;
  isActive: boolean;
  isDisplay: boolean;
  pos: number;
  createdAt: string;
};

