/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

export type PermissionEntity = {
  id: string;
  title?: string | null;
  description?: string | null;
  key: PermissionEntity.key;
};

export namespace PermissionEntity {

  export enum key {
    ALL_FULL_CONTROL = 'ALL_FULL_CONTROL',
    DEPARTMENT_FULL_CONTROL = 'DEPARTMENT_FULL_CONTROL',
    CODE_FULL_CONTROL = 'CODE_FULL_CONTROL',
    CUSTOMER_FULL_CONTROL = 'CUSTOMER_FULL_CONTROL',
    BOOKING_HISTORY_VIEW = 'BOOKING_HISTORY_VIEW',
  }


}

