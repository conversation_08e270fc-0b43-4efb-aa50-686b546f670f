export enum NavbarKey {
  Dashboard = 'dashboard',
  DispatchCenter = 'dispatch-center',
  Driver = 'driver',
  Customer = 'customer',
  Login = 'login',
  Report = 'Report',
  PriceManagement = 'price-management',
  BookingHistory = 'booking-history',
  Coupon = 'coupon',
  Promotion = 'promotion',
  Setting = 'setting',
  ForgotPassword = 'forgot-password',
  Notification = 'notification',
  Department = 'department',
  Employee = 'employee',
  LoyalPoints = 'loyal-points',
  Subscription = 'subscription',
  Configuration = 'configuration',
  Location = 'location',
  Car = 'car',
  Banner = 'banner',
  Support = 'support',
  Task = 'task',
  Fee = 'fee',
  Rate = 'rate',
  Service = 'service',
  DepartmentManagement = 'department-management',
  Zone = 'zone',
  Rating = 'customer-reviews',
  Admin = 'admin',
  EmployeeManagement = 'employee-management',
  BookingSource = 'booking-source',
  Overview = 'overview',
  Heatmap = 'heatmap',
  Permission = 'permission',
  PermissionDenied = 'permission-denied',
  Fleet = 'fleet',
  News = 'news',
  FixedFare = 'fixed-fare',
}
