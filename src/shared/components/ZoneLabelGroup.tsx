import type { SimpleZoneEntity } from '#/api/requests';
import { Tag, Tooltip } from 'antd';

interface Props {
  group: SimpleZoneEntity[];
  className?: string;
}

function ZoneLabelGroup({ group, className }: Props) {
  return (
    <div className="block">
      {group?.slice(0, 3).map((item, index) => (
        <Tag
          className={`bg-neutral-200 px-2 py-1 rounded-md font-medium text-xs border-none ${className}`}
          key={index}
        >
          {item.name}
        </Tag>
      ))}
      {group && group.length > 3 ? (
        <Tooltip
          className="bg-neutral-6"
          overlayInnerStyle={{
            backgroundColor: '#fff',
            fontWeight: 600,
            padding: '4px 0 4px 4px',
          }}
          title={group.slice(3).map((item, index) => (
            <Tag
              className="bg-neutral-200 px-2 py-1 rounded-md font-medium text-xs border-none"
              key={index}
            >
              {item.name}
            </Tag>
          ))}
        >
          <Tag className="bg-neutral-200 px-2 py-1 rounded-md font-medium text-xs border-none">
            +{group.length - 3}
          </Tag>
        </Tooltip>
      ) : null}
    </div>
  );
}

export default ZoneLabelGroup;
