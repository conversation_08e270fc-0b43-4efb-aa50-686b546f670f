/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-unsafe-argument */
import type { CancelablePromise, IBaseResponse } from '#/api/requests';
import { DEFAULT_PAGE_SIZE } from '#/shared/constants';
import { useGetInfinite } from '#/shared/hooks/useGetInfinite';
import { Select } from 'antd';
import type {
  BaseOptionType,
  DefaultOptionType,
  SelectProps,
} from 'antd/es/select';
import uniqBy from 'lodash-es/uniqBy';
import React, { useEffect, useState } from 'react';
import { useDebounce } from 'use-debounce';

export const emptyStatus = {
  label: '(removed/deactivated)',
  value: 'DEACTIVATED',
};

const allCoupon = {
  label: 'All coupon',
  value: null,
};

export type RawValueType = string | number;

export interface FlattenOptionData<OptionType> {
  label?: React.ReactNode;
  data: OptionType;
  key: React.Key;
  value?: RawValueType;
  groupOption?: boolean;
  group?: boolean;
}

interface Props<T> extends SelectProps {
  formatData: (option: T) => DefaultOptionType;
  queryFn: (
    pageParam: number,
    pageSize: number,
    search?: string,
  ) => CancelablePromise<IBaseResponse>;
  enabled?: boolean;
  queryKey: string[];
  pageSize?: number;
  includeValue?: DefaultOptionType[];
  optionRender?: (
    option: FlattenOptionData<BaseOptionType>,
    info: { index: number },
  ) => React.ReactNode;
  isShowEmptyStatus?: boolean;
  isShowAllCoupon?: boolean;
}

export function InfinitySelector<T>({
  formatData,
  queryFn,
  enabled,
  queryKey,
  pageSize = DEFAULT_PAGE_SIZE,
  includeValue,
  optionRender,
  isShowEmptyStatus = false,
  isShowAllCoupon = false,
  ...rest
}: Props<T>) {
  const [search, setSearch] = useState<string>('');
  const [searchValue] = useDebounce<T>(search as T, 350) as unknown as string;

  const { fetchNextPage, hasNextPage, isFetching, infiniteOptions } =
    useGetInfinite({
      enabled,
      pageSize,
      queryFn,
      queryKey,
      search: searchValue,
    });

  const [options, setOptions] = useState(
    infiniteOptions?.map(option => formatData(option)) || [],
  );

  const getOptions = () => {
    if (includeValue) {
      if (isShowEmptyStatus)
        return uniqBy([emptyStatus, ...includeValue, ...options], 'value');
      if (isShowAllCoupon)
        return uniqBy([allCoupon, ...includeValue, ...options], 'value');

      if (!isShowAllCoupon && !isShowEmptyStatus) {
        return uniqBy([...includeValue, ...options], 'value');
      }
    } else {
      if (isShowEmptyStatus) return [emptyStatus, ...options];
      if (isShowAllCoupon) return [allCoupon, ...options];
      return options;
    }
  };

  useEffect(() => {
    setOptions(infiniteOptions?.map(option => formatData(option)) || []);
  }, [infiniteOptions]);

  return (
    <Select
      allowClear
      {...rest}
      // filterOption={false}
      filterOption={(input, option) =>
        String(option?.label)
          .toLowerCase()
          .includes(input.toLowerCase())
      }
      loading={isFetching || rest.loading}
      onPopupScroll={e => {
        const element = e.target as HTMLDivElement;
        const isEndOfScrollY =
          Math.floor(element.scrollHeight - element.scrollTop) ===
          Math.round(element.clientHeight);

        if (isEndOfScrollY && hasNextPage) {
          fetchNextPage();
        }
      }}
      onSearch={setSearch}
      onSelect={() => {
        setSearch('');
      }}
      optionFilterProp="children"
      optionRender={optionRender}
      options={getOptions()}
      searchValue={search}
    />
  );
}
