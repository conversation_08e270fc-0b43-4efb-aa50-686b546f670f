import { type ZoneResponse, ZoneService } from '#/api/loyalhub/requests';
import type { CancelablePromise, IBaseResponse } from '#/api/requests';
import type { SelectProps } from 'antd';
import { type ReactNode } from 'react';
import type {
  FieldValues,
  UseControllerProps,
  UseFormReturn,
} from 'react-hook-form';
import { Controller, useController } from 'react-hook-form';
import { TypographyUI } from '../../TypographyUI';
import { InfinitySelector } from '../InfinitySelector';
import './style.scss';
import type { DefaultOptionType } from 'antd/es/select';

interface ZoneSelectorProps {
  zoneIds?: string;
  includeValue?: DefaultOptionType[];
}

export function ZoneSelector({ ...props }: SelectProps & ZoneSelectorProps) {
  const { zoneIds } = props;

  return (
    <InfinitySelector<ZoneResponse>
      formatData={option => ({
        label: option.name,
        value: option.zoneId,
      })}
      queryFn={(pageParam, pageSize, name) =>
        ZoneService.zonePrivateControllerGetZoneList(
          name || undefined,
          zoneIds,
          undefined,
          pageSize,
          pageParam,
          undefined,
        ) as unknown as CancelablePromise<IBaseResponse>
      }
      queryKey={['infiniteZones']}
      showSearch
      size={props.size || 'middle'}
      {...props}
    />
  );
}

interface Props<T extends FieldValues> extends SelectProps {
  form: UseFormReturn<T>;
  label: ReactNode | string;
  showRequired?: boolean;
  zoneIds?: string;
  includeValue?: DefaultOptionType[];
}

export function FormZoneSelector<T>({
  form,
  label,
  showRequired,
  zoneIds,
  ...rest
}: Props<T extends FieldValues ? T : FieldValues> &
  UseControllerProps<T extends FieldValues ? T : FieldValues>) {
  const { trigger, control } = form;

  const { fieldState } = useController({
    control,
    name: rest.name,
  });

  return (
    <>
      {typeof label === 'string' ? (
        <TypographyUI className="pb-2" font="Semibold" type="BodySm">
          {label}{' '}
          {showRequired ? <span className="text-error-color">*</span> : ''}
        </TypographyUI>
      ) : (
        label
      )}

      <Controller
        control={control}
        name={rest.name}
        render={({ field }) => (
          <ZoneSelector
            {...field}
            {...rest}
            onChange={(value, option) => {
              field.onChange(value);
              trigger(rest.name);
              rest.onChange?.(value, option);
            }}
            zoneIds={zoneIds}
          />
        )}
      />

      {fieldState.error ? (
        <div className="text-xs mt-1 text-error-color">
          {fieldState.error.message}
        </div>
      ) : null}
    </>
  );
}
