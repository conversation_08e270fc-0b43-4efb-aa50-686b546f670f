import { useTranslation } from 'react-i18next';
import { type UseFormReturn } from 'react-hook-form';
import { FormEntitySelector } from '../FormEntitySelector';
import { BookingEntity } from '#/api/requests';

interface Props {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  form: UseFormReturn<any>;
  name: string;
  className?: string;
  label?: string;
  defaultValue?: string[];
  mode?: 'multiple' | 'tags';
  onChange?: (value: string) => void;
  placeholder?: string;
  showRequired?: boolean;
  hasAnyCar?: boolean;
}

export function ServiceSelector({
  form,
  name,
  className,
  label,
  defaultValue,
  mode,
  onChange,
  placeholder,
  showRequired,
  hasAnyCar = false,
}: Props) {
  const { t } = useTranslation();

  const options = [
    {
      label: t('coupon.create.vehicleTypes.typical'),
      value: BookingEntity.vehicleType.TYPICAL_CAR,
    },
    {
      label: t('coupon.create.vehicleTypes.van'),
      value: BookingEntity.vehicleType.VAN,
    },
    {
      label: t('coupon.create.vehicleTypes.accessible'),
      value: BookingEntity.vehicleType.ACCESSIBLE_VAN,
    },
    {
      label: t('coupon.create.vehicleTypes.electric'),
      value: BookingEntity.vehicleType.ELECTRIC_CAR,
    },
    ...(hasAnyCar
      ? [
          {
            label: t('coupon.create.vehicleTypes.anyCar'),
            value: BookingEntity.vehicleType.ANY_CAR,
          },
        ]
      : []),
  ];

  return (
    <FormEntitySelector
      allowClear={true}
      className={className}
      defaultValue={defaultValue}
      form={form}
      label={label}
      maxTagCount={'responsive'}
      mode={mode}
      name={name}
      onChange={onChange}
      options={options}
      placeholder={
        <div className="text-sm">
          {placeholder || t('bookingHistory.table.service')}
        </div>
      }
      showRequired={showRequired}
      size="middle"
    />
  );
}
