import type { SelectProps } from 'antd';
import { TreeSelect } from 'antd';
import { type ReactNode, useEffect, useState } from 'react';
import {
  Controller,
  type FieldValues,
  type UseControllerProps,
  type UseFormReturn,
  useController,
} from 'react-hook-form';
import { BookingEntity } from '#/api/requests';
import { useTranslation } from 'react-i18next';
import { TypographyUI } from '../../TypographyUI';

const { SHOW_PARENT } = TreeSelect;

interface Props<T extends FieldValues> extends SelectProps {
  form: UseFormReturn<T>;
  label: ReactNode | string;
  showRequired?: boolean;
  className?: string;
  placeholder?: string;
  treeIcon?: boolean;
  treeDefaultExpandAll: boolean;
  defaultVehicles?: string[];
}

export function FormServiceSelector<T>({
  className,
  form,
  label,
  showRequired,
  placeholder,
  treeIcon,
  treeDefaultExpandAll,
  defaultVehicles,
  ...rest
}: Props<T extends FieldValues ? T : FieldValues> &
  UseControllerProps<T extends FieldValues ? T : FieldValues>) {
  const { t } = useTranslation();
  const { control } = form;
  const [vehicles, setVehicles] = useState<string[]>(
    defaultVehicles || [BookingEntity.vehicleType.ALL],
  );

  const { fieldState } = useController({
    control,
    name: rest.name,
  });

  const vehicleList = [
    {
      label: t('coupon.create.vehicleTypes.all'),
      value: BookingEntity.vehicleType.ALL,
    },
    {
      disabled: vehicles?.includes(BookingEntity.vehicleType.ALL),
      label: t('coupon.create.vehicleTypes.typical'),
      value: BookingEntity.vehicleType.TYPICAL_CAR,
    },
    {
      disabled: vehicles?.includes(BookingEntity.vehicleType.ALL),
      label: t('coupon.create.vehicleTypes.van'),
      value: BookingEntity.vehicleType.VAN,
    },
    {
      disabled: vehicles?.includes(BookingEntity.vehicleType.ALL),
      label: t('coupon.create.vehicleTypes.accessible'),
      value: BookingEntity.vehicleType.ACCESSIBLE_VAN,
    },
    {
      disabled: vehicles?.includes(BookingEntity.vehicleType.ALL),
      label: t('coupon.create.vehicleTypes.electric'),
      value: BookingEntity.vehicleType.ELECTRIC_CAR,
    },
  ];

  const onChangeVehicle = (option: string[]) => {
    if (option.includes(BookingEntity.vehicleType.ALL)) {
      setVehicles([BookingEntity.vehicleType.ALL]);
    } else {
      setVehicles(option);
    }
  };

  useEffect(() => {
    // eslint-disable-next-line @typescript-eslint/no-unsafe-argument, @typescript-eslint/no-explicit-any
    form.setValue(rest.name, vehicles as any);
  }, [vehicles]);

  return (
    <>
      {typeof label === 'string' ? (
        <TypographyUI className="pb-2" font="Semibold" type="BodySm">
          {label}{' '}
          {showRequired ? <span className="text-error-color">*</span> : ''}
        </TypographyUI>
      ) : (
        label
      )}

      <Controller
        control={control}
        name={rest.name}
        render={({ field }) => (
          <TreeSelect
            {...field}
            {...rest}
            className={className}
            maxTagCount={'responsive'}
            onChange={(value, _) => {
              onChangeVehicle(value);
            }}
            placeholder={placeholder}
            showCheckedStrategy={SHOW_PARENT}
            treeCheckable
            treeData={vehicleList}
            treeDefaultExpandAll={treeDefaultExpandAll}
            treeIcon={treeIcon}
          />
        )}
      />

      {fieldState.error ? (
        <div className="text-error-color text-xs mt-1">
          {' '}
          <p>{fieldState.error.message}</p>{' '}
        </div>
      ) : null}
    </>
  );
}
