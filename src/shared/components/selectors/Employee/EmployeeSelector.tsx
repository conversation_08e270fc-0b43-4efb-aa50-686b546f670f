import type { UserEntity } from '#/api/requests';
import { EmployeesService } from '#/api/loyalhub/requests';
import { Avatar, type SelectProps } from 'antd';
import type { DefaultOptionType } from 'antd/es/select';
import type { ReactNode } from 'react';
import type {
  FieldValues,
  UseControllerProps,
  UseFormReturn,
} from 'react-hook-form';
import { Controller, useController } from 'react-hook-form';
import { TypographyUI } from '../../TypographyUI';
import { EmployeeInfinitySelector } from '../EmployeeInfinitySelector';
import './style.scss';

interface EmployeeSelectorProps {
  isFilter?: boolean;
  userId?: string;
  isActive?: boolean;
  isShowEmptyStatus?: boolean;
}

export function EmployeeSelector({
  ...props
}: SelectProps & EmployeeSelectorProps) {
  const { isActive, isShowEmptyStatus } = props;

  return (
    <EmployeeInfinitySelector<UserEntity>
      formatData={option => ({
        avatarUrl: option.avatarUrl,
        email: option.email,
        fullName: option.fullName,
        label: option.fullName,
        value: option.id,
      })}
      isShowEmptyStatus={isShowEmptyStatus}
      optionRender={option => (
        <div>
          {option?.data?.fullName ? (
            <div className="flex gap-2">
              <Avatar
                className="w-10 h-10 text-base flex items-center"
                src={option.data?.avatarUrl}
              >
                {option.data?.fullName?.slice(0, 1)}
              </Avatar>
              <div className="flex flex-col">
                <div
                  className={`${
                    props?.isFilter ? 'max-w-[180px] truncate' : ''
                  } text-sm font-semibold text-neutral-1`}
                >
                  {option.data.fullName}
                </div>
                <div
                  className={`${
                    props?.isFilter ? 'max-w-[180px] truncate' : ''
                  } text-xs font-normal text-neutral-3`}
                >
                  {option.data.email}
                </div>
              </div>
            </div>
          ) : (
            <>(removed/deactivated)</>
          )}
        </div>
      )}
      queryFn={(pageParam, pageSize, fullName) =>
        EmployeesService.employeePrivateControllerGetListEmployeeFilter(
          undefined,
          fullName || undefined,
          undefined,
          undefined,
          undefined,
          undefined,
          undefined,
          isActive,
          undefined,
          undefined,
          undefined,
          undefined,
          undefined,
          pageSize,
          pageParam,
          undefined,
        ) as unknown as
          | CancelablePromise<IBaseResponse>
          | CancelablePromise<BaseResponse>
      }
      queryKey={['infiniteEmployees']}
      showSearch
      size="middle"
      {...props}
    />
  );
}

interface Props<T extends FieldValues> extends SelectProps {
  form: UseFormReturn<T>;
  label: ReactNode | string;
  showRequired?: boolean;
  includeValue?: DefaultOptionType[];
  isFilter?: boolean;
  userId?: string;
  isActive?: boolean;
  isShowEmptyStatus?: boolean;
}

export function FormEmployeeSelector<T>({
  form,
  label,
  showRequired,
  isFilter,
  userId,
  isActive,
  isShowEmptyStatus = false,
  ...rest
}: Props<T extends FieldValues ? T : FieldValues> &
  UseControllerProps<T extends FieldValues ? T : FieldValues>) {
  const { trigger, control } = form;

  const { fieldState } = useController({
    control,
    name: rest.name,
  });

  return (
    <>
      {typeof label === 'string' ? (
        <TypographyUI className="pb-2" font="Semibold" type="BodySm">
          {label}{' '}
          {showRequired ? <span className="text-error-color">*</span> : ''}
        </TypographyUI>
      ) : (
        label
      )}

      <div>
        <Controller
          control={control}
          name={rest.name}
          render={({ field }) => (
            <EmployeeSelector
              {...field}
              {...rest}
              isActive={isActive}
              isFilter={isFilter}
              isShowEmptyStatus={isShowEmptyStatus}
              onChange={(value, option) => {
                field.onChange(value);
                trigger(rest.name);
                rest.onChange?.(value, option);
              }}
              userId={userId}
            />
          )}
        />
      </div>

      {fieldState.error ? (
        <div className="h-6 text-error-color">{fieldState.error.message}</div>
      ) : null}
    </>
  );
}
