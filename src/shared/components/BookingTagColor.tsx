import { BookingEntity } from '#/api/requests';
import { Tag } from 'antd';

interface BookingStatusDetailProps {
  backgroundColor: string;
  id: string;
  textColor: string;
}

interface BookingStatusProps {
  CANCELLED: BookingStatusDetailProps;
  COMPLETED: BookingStatusDetailProps;
  ONGOING: BookingStatusDetailProps;
  SCHEDULED: BookingStatusDetailProps;
}

interface Props {
  bookingStatus: BookingEntity.status | 'on-going';
  className?: string;
}

export const BOOKING_STATUS: BookingStatusProps = {
  CANCELLED: {
    backgroundColor: 'bg-error-color-soft',
    id: 'CANCELLED',
    textColor: 'text-error-color',
  },
  COMPLETED: {
    backgroundColor: 'bg-success-color-soft',
    id: 'COMPLETED',
    textColor: 'text-success-color',
  },
  ONGOING: {
    backgroundColor: 'bg-info-color-soft',
    id: 'ONGOING',
    textColor: 'text-info-color',
  },
  SCHEDULED: {
    backgroundColor: 'bg-purple-color-soft',
    id: 'SCHEDULED',
    textColor: 'text-purple-color',
  },
};

function BookingTagColor({ bookingStatus, className }: Props) {
  let concatClassName, title;

  const classes = () => {
    switch (bookingStatus) {
      case BookingEntity.status.CANCELLED:
        concatClassName = BOOKING_STATUS.CANCELLED;
        title = 'Cancelled';
        break;
      case BookingEntity.status.COMPLETED:
        concatClassName = BOOKING_STATUS.COMPLETED;
        title = 'Completed';
        break;
      case BookingEntity.status.SCHEDULED:
        concatClassName = BOOKING_STATUS.SCHEDULED;
        title = 'Scheduled';
        break;
      case BookingEntity.status.DEMAND_CREATION ||
        BookingEntity.status.CONFIRMED_ADDRESS ||
        BookingEntity.status.AWAITING_CONFIRMED_VEHICLE ||
        BookingEntity.status.VEHICLE_CONFIRMED ||
        BookingEntity.status.ARRIVAL_AT_CLIENT ||
        BookingEntity.status.CLIENT_ON_BOARD ||
        BookingEntity.status.TO_BE_DISTRIBUTED:
        concatClassName = BOOKING_STATUS.ONGOING;
        title = 'On-going';
        break;
      default:
        concatClassName = BOOKING_STATUS.ONGOING;
        title = 'On-going';
    }

    return `${className || ''} ${concatClassName.backgroundColor} ${
      concatClassName.textColor
    }`;
  };

  return (
    <Tag bordered={false} className={`${classes()} font-semibold`}>
      {title}
    </Tag>
  );
}

export default BookingTagColor;
