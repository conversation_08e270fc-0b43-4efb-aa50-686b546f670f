import type { MenuProps } from 'antd';
import { Dropdown } from 'antd';
import { useTranslation } from 'react-i18next';
import { Icon } from '@iconify/react';
import { IconKey } from '#/shared/constants/iconKey';
import { useEffect } from 'react';

export function LanguageSwitcher() {
  const { i18n } = useTranslation();
  const items: MenuProps['items'] = [
    {
      key: 'eng',
      label: <div className="text-left">English</div>,
      onClick: () => {
        i18n.changeLanguage('en');
      },
    },
    {
      key: 'fr',
      label: <div className="text-left">French</div>,
      onClick: () => {
        i18n.changeLanguage('fr');
      },
    },
  ];

  useEffect(
    () => () => {
      i18n.changeLanguage('en');
    },
    [],
  );

  return (
    <Dropdown
      menu={{
        items,
      }}
      placement="bottomRight"
      trigger={['click']}
    >
      <Icon
        className="text-neutral-3 min-w-[22px] cursor-pointer"
        icon={IconKey.MultipleLanguage}
        width={22}
      />
    </Dropdown>
  );
}
