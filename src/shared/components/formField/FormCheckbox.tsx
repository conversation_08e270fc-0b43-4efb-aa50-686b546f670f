import type { RadioGroupProps } from 'antd';
import { Checkbox } from 'antd';
import type { ReactNode } from 'react';
import type {
  FieldValues,
  UseControllerProps,
  UseFormReturn,
} from 'react-hook-form';
import { Controller, useController } from 'react-hook-form';
import { TypographyUI } from '../TypographyUI';
import { ErrorMessage } from './ErrorMessage';

interface Props<T extends FieldValues> {
  form: UseFormReturn<T>;
  label: ReactNode | string;
  checked?: boolean;
  showRequired?: boolean;
  className?: string;
}

export function FormCheckbox<T>({
  form,
  label,
  checked,
  showRequired,
  disabled,
  className,
  ...rest
}: Props<T extends FieldValues ? T : FieldValues> &
  UseControllerProps<T extends FieldValues ? T : FieldValues> &
  Omit<RadioGroupProps, 'form'>) {
  const { trigger, control } = form;

  const { fieldState } = useController({
    control,
    name: rest.name,
  });

  return (
    <>
      <Controller
        control={control}
        name={rest.name}
        render={({ field }) => (
          <Checkbox
            checked={checked}
            disabled={disabled}
            onChange={e => {
              field.onChange(e);
              rest.onChange?.(e);
              trigger(rest.name);
            }}
            value={field.value}
          >
            {typeof label === 'string' ? (
              <TypographyUI className={className} font="Semibold" type="BodySm">
                {label}{' '}
                {showRequired ? (
                  <span className="text-error-color">*</span>
                ) : (
                  ''
                )}
              </TypographyUI>
            ) : (
              label
            )}
          </Checkbox>
        )}
      />

      {fieldState.error ? (
        <ErrorMessage>
          <p>{fieldState.error.message}</p>
        </ErrorMessage>
      ) : null}
    </>
  );
}
