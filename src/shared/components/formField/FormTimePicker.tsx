import { type ReactNode } from 'react';
import type {
  FieldValues,
  UseControllerProps,
  UseFormReturn,
} from 'react-hook-form';
import { Controller, useController } from 'react-hook-form';
import { TypographyUI } from '../TypographyUI';
import { ErrorMessage } from './ErrorMessage';
import { TimePicker, type TimePickerProps } from 'antd';
import dayjs from 'dayjs';

interface Props<T extends FieldValues> {
  form: UseFormReturn<T>;
  label: ReactNode | string;
  showRequired?: boolean;
  placeholder: string;
  isDisabledTime?: boolean;
  startHour?: number;
  startMinute?: number;
}

export function FormTimePicker<T>({
  form,
  label,
  name,
  showRequired,
  placeholder,
  startHour,
  startMinute,
  isDisabledTime,
  ...rest
}: Props<T extends FieldValues ? T : FieldValues> &
  UseControllerProps<T extends FieldValues ? T : FieldValues> &
  TimePickerProps) {
  const { control, trigger } = form;

  const { fieldState } = useController({
    control,
    name,
  });

  const disabledTime = () => ({
    disabledHours: () => {
      const hours = [];

      for (let i = 0; i < (startHour ?? 0); i++) {
        hours.push(i);
      }

      return hours;
    },
    disabledMinutes: (selectedHour: number) => {
      if (selectedHour === startHour) {
        const minutes = [];

        for (let i = 0; i <= (startMinute ?? 0); i++) {
          minutes.push(i);
        }

        return minutes;
      }

      return [];
    },
  });

  return (
    <>
      {typeof label === 'string' ? (
        <TypographyUI className="pb-2" font="Semibold" type="BodySm">
          {label}{' '}
          {showRequired ? <span className="text-error-color">*</span> : ''}
        </TypographyUI>
      ) : (
        label
      )}

      <Controller
        control={control}
        name={name}
        render={({ field }) => (
          <TimePicker
            {...rest}
            disabledTime={isDisabledTime ? disabledTime : undefined}
            onChange={time => {
              if (time) {
                field.onChange({
                  hour: time.hour(),
                  minute: time.minute(),
                });
              } else {
                field.onChange(null);
              }

              trigger(name);
            }}
            placeholder={placeholder}
            value={
              field.value &&
              typeof field.value === 'object' &&
              field.value.hour !== undefined
                ? dayjs()
                    .hour(field.value.hour)
                    .minute(field.value.minute || 0)
                : null
            }
          />
        )}
      />

      {fieldState.error ? (
        <ErrorMessage>
          {' '}
          <p>{fieldState.error.message}</p>{' '}
        </ErrorMessage>
      ) : null}
    </>
  );
}
