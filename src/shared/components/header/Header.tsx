import { Navbar<PERSON><PERSON>, Storage<PERSON>ey } from '#/shared/constants';
import {
  getLocalStorageItem,
  removeLocalStorage,
  setLocalStorageItem,
} from '#/shared/utils/localStorage';
import useAuthStore from '#/store/useAuthStore';
import type { MenuProps } from 'antd';
/*
 * import { Avatar, Badge, Dropdown } from 'antd';
 * import { LogoutCurve, Notification } from 'iconsax-react';
 */
import { Avatar, Dropdown } from 'antd';
import { LogoutCurve } from 'iconsax-react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

export function Header() {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { user } = useAuthStore();

  const items: MenuProps['items'] = [
    {
      icon: <LogoutCurve size={16} />,
      key: 'onView',
      label: <div className="text-left w-20">{t('common.logout')}</div>,
      onClick: () => {
        const columnsBookingIndividual = getLocalStorageItem(
          StorageKey.BookingIndividual,
        );

        const columnsBookingBusiness = getLocalStorageItem(
          StorageKey.BookingBusiness,
        );

        removeLocalStorage();
        navigate(`/${NavbarKey.Login}`);

        // Save local storage custom columns booking
        setLocalStorageItem(
          StorageKey.BookingIndividual,
          columnsBookingIndividual as string,
        );
        setLocalStorageItem(
          StorageKey.BookingBusiness,
          columnsBookingBusiness as string,
        );
      },
    },
  ];

  /*
   * const goToNotification = () => {
   *   navigate(`/${NavbarKey.Notification}`);
   * };
   */

  return (
    <div className="flex items-center gap-3 justify-end py-3 px-6 lg:px-0">
      {/* <Badge count={1}>
        <Notification
          className="cursor-pointer"
          onClick={goToNotification}
          size={19}
        />
      </Badge> */}

      <Dropdown
        menu={{
          items,
        }}
        placement="bottomRight"
        trigger={['click']}
      >
        <Avatar
          className="cursor-pointer"
          size={32}
          src={
            user.avatarUrl ||
            'https://i.pinimg.com/474x/3f/d2/5f/3fd25fddb699f74decfad452241a82b2.jpg'
          }
        />
      </Dropdown>
    </div>
  );
}
