import {
  Car,
  DollarCircle,
  Element4,
  Image,
  Location,
  Map,
  MedalStar,
  Money2,
  NotificationBing,
  People,
  Profile,
  ReceiptText,
  SecuritySafe,
  Star,
  TaskSquare,
  Ticket,
  TimerPause,
} from 'iconsax-react';
import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation, useNavigate } from 'react-router-dom';
import { getActiveKey } from '../components/sideBar/utils';
import { NavbarKey } from '../constants/navbarKey';
import { IconKey } from '../constants/iconKey';
import { Icon } from '@iconify/react';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { Permission } from '../constants/permission';

export interface SubNavbar {
  children: {
    key: string;
    label: string;
    onClick: () => void;
    permissions?: string[];
  }[];
  key: NavbarKey;
  icon: React.ReactNode;
  label: string;
}

export interface MenuSideBarItem {
  icon?: React.ReactNode;
  activeIcon?: React.ReactNode;
  key: NavbarKey;
  label?: string;
  desc?: string;
  match?: string[];
  url?: string;
  onClick?: () => void;
  havePermission?: boolean;
  permissions?: string[];
  subNavbar?: SubNavbar[];
}

export interface CollapseBarMenu {
  group: string;
  items: MenuSideBarItem[];
}

export const useMenuSideBar = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { t } = useTranslation();

  const collapseBarMenus: CollapseBarMenu[] = useMemo(() => {
    const navbar = [
      {
        group: t('sidebar.mainMenu'),
        items: [
          {
            key: NavbarKey.Dashboard,
            subNavbar: [
              {
                children: [
                  {
                    key: NavbarKey.Overview,
                    label: t('sidebar.overview'),
                    onClick: () => {
                      navigate(`/${NavbarKey.Dashboard}/${NavbarKey.Overview}`);
                    },
                  },
                  {
                    key: NavbarKey.BookingSource,
                    label: t('sidebar.bookingSource'),
                    onClick: () => {
                      navigate(
                        `/${NavbarKey.Dashboard}/${NavbarKey.BookingSource}`,
                      );
                    },
                    permissions: [Permission.VIEW_BOOKING_SOURCE],
                  },

                  /*
                   * {
                   *   key: NavbarKey.Heatmap,
                   *   label: t('sidebar.heatmap'),
                   *   onClick: () => {
                   *     navigate(`/${NavbarKey.Dashboard}/${NavbarKey.Heatmap}`);
                   *   },
                   * },
                   */
                ],
                icon: <Element4 size="16" />,
                key: NavbarKey.Dashboard,
                label: t('sidebar.dashboard'),
              },
            ],
          },
          {
            icon: <TimerPause size="16" />,
            key: NavbarKey.BookingHistory,
            label: t('sidebar.bookingHistory'),
            match: [`/${NavbarKey.BookingHistory}`],
            onClick: () => {
              navigate(`/${NavbarKey.BookingHistory}`);
            },
            permissions: [
              Permission.VIEW_BOOKING_PERSONAL,
              Permission.REFUND_BOOKING_PERSONAL,
              Permission.VIEW_BOOKING_BUSINESS,
            ],
          },
          {
            icon: <QuestionCircleOutlined style={{ fontSize: 14 }} />,
            key: NavbarKey.Support,
            label: t('sidebar.support'),
            match: [`/${NavbarKey.Support}`],
            onClick: () => {
              navigate(`/${NavbarKey.Support}`);
            },
            permissions: [Permission.VIEW_SUPPORT_TICKET],
          },
          {
            icon: <MedalStar size="16" />,
            key: NavbarKey.Customer,
            label: t('sidebar.customer'),
            match: [`/${NavbarKey.Customer}`],
            onClick: () => {
              navigate(`/${NavbarKey.Customer}`);
            },
            permissions: [
              Permission.VIEW_CUSTOMER_PERSONAL,
              Permission.VIEW_CUSTOMER_BUSINESS,
            ],
          },
        ],
      },
      {
        group: t('sidebar.loyalHubManagement'),
        items: [
          {
            icon: <TaskSquare size="16" />,
            key: NavbarKey.Task,
            label: t('sidebar.task', ''),
            match: [`/${NavbarKey.Task}`],
            onClick: () => {
              navigate(`/${NavbarKey.Task}`);
            },
            permissions: [
              Permission.VIEW_TASK,
              Permission.VIEW_DEPARTMENT_TASK,
              Permission.VIEW_ASSIGNED_TASK,
            ],
          },
          {
            icon: <Car size="16" />,
            key: NavbarKey.Fleet,
            label: t('sidebar.fleet'),
            match: [`/${NavbarKey.Fleet}`],
            onClick: () => {
              navigate(`/${NavbarKey.Fleet}`);
            },
            permissions: [Permission.VIEW_FLEET],
          },
          {
            icon: <Star size="16" />,
            key: NavbarKey.Rating,
            label: t('sidebar.customerReviews'),
            match: [`/${NavbarKey.Rating}`],
            onClick: () => {
              navigate(`/${NavbarKey.Rating}`);
            },
            permissions: [
              Permission.VIEW_CUSTOMER_REVIEW,
              Permission.VIEW_AVERAGE_RATING,
            ],
          },
          {
            icon: <People size="16" />,
            key: NavbarKey.DepartmentManagement,
            label: t('sidebar.department'),
            match: [`/${NavbarKey.DepartmentManagement}`],
            onClick: () => {
              navigate(`/${NavbarKey.DepartmentManagement}`);
            },
            permissions: [Permission.VIEW_DEPARTMENT],
          },
          {
            icon: <Map size="16" />,
            key: NavbarKey.Zone,
            label: t('sidebar.zone'),
            match: [`/${NavbarKey.Zone}`],
            onClick: () => {
              navigate(`/${NavbarKey.Zone}`);
            },
            permissions: [Permission.VIEW_ZONE],
          },
          {
            key: NavbarKey.EmployeeManagement,
            subNavbar: [
              {
                children: [
                  {
                    key: NavbarKey.Employee,
                    label: t('sidebar.employeeInternal'),
                    onClick: () => {
                      navigate(
                        `/${NavbarKey.EmployeeManagement}/${NavbarKey.Employee}`,
                      );
                    },
                    permissions: [
                      Permission.VIEW_EMPLOYEE,
                      Permission.VIEW_DEPARTMENT_EMPLOYEE,
                    ],
                  },
                  {
                    key: NavbarKey.Driver,
                    label: t('sidebar.driverExternal'),
                    onClick: () => {
                      navigate(
                        `/${NavbarKey.EmployeeManagement}/${NavbarKey.Driver}`,
                      );
                    },
                    permissions: [Permission.VIEW_DRIVER],
                  },
                ],
                icon: <Profile size={16} />,
                key: NavbarKey.EmployeeManagement,
                label: t('sidebar.employee'),
              },
            ],
          },
          {
            icon: <SecuritySafe size="16" />,
            key: NavbarKey.Permission,
            label: t('sidebar.permission'),
            match: [`/${NavbarKey.Permission}`],
            onClick: () => {
              navigate(`/${NavbarKey.Permission}`);
            },
            permissions: [Permission.VIEW_PERMISSION],
          },
        ],
      },
      {
        group: t('sidebar.configuration'),
        items: [
          {
            icon: <Ticket size="16" />,
            key: NavbarKey.Coupon,
            label: t('sidebar.coupon'),
            match: [`/${NavbarKey.Coupon}`],
            onClick: () => {
              navigate(`/${NavbarKey.Coupon}`);
            },
            permissions: [Permission.VIEW_COUPON],
          },
          {
            icon: <DollarCircle size="16" />,
            key: NavbarKey.Subscription,
            label: t('sidebar.subscription'),
            match: [`/${NavbarKey.Subscription}`],
            onClick: () => {
              navigate(`/${NavbarKey.Subscription}`);
            },
            permissions: [
              Permission.VIEW_LOYAL_ONE,
              Permission.VIEW_REVENUE_LOYAL_ONE,
            ],
          },
          {
            icon: <Location size="16" />,
            key: NavbarKey.Location,
            label: t('configuration.location'),
            match: [`/${NavbarKey.Configuration}/${NavbarKey.Location}`],
            onClick: () => {
              navigate(`/${NavbarKey.Configuration}/${NavbarKey.Location}`);
            },
            permissions: [Permission.VIEW_LOCATION],
          },
          {
            activeIcon: <Icon icon={IconKey.LoyalPointActive} width={16} />,
            icon: <Icon icon={IconKey.LoyalPoint} width={16} />,
            key: NavbarKey.LoyalPoints,
            label: t('sidebar.loyalPoints'),
            match: [`/${NavbarKey.LoyalPoints}`],
            onClick: () => {
              navigate(`/${NavbarKey.LoyalPoints}`);
            },
            permissions: [Permission.VIEW_LOYAL_POINT],
          },
          {
            icon: <Money2 size="16" />,
            key: NavbarKey.Service,
            label: t('sidebar.fee'),
            match: [`/${NavbarKey.Service}`],
            onClick: () => {
              navigate(`/${NavbarKey.Service}/${NavbarKey.Fee}`);
            },
            permissions: [
              Permission.VIEW_CONFIG_FEE,
              Permission.VIEW_CONFIG_RATE,
              Permission.VIEW_FIXED_FARE,
            ],
          },
        ],
      },
      {
        group: t('sidebar.marketing'),
        items: [
          {
            icon: <NotificationBing size="16" />,
            key: NavbarKey.Notification,
            label: t('sidebar.notification', ''),
            match: [`/${NavbarKey.Notification}`],
            onClick: () => {
              navigate(`/${NavbarKey.Notification}`);
            },
            permissions: [Permission.VIEW_NOTIFICATION],
          },
          {
            icon: <Image size="16" />,
            key: NavbarKey.Banner,
            label: t('configuration.banner'),
            match: [`/${NavbarKey.Configuration}/${NavbarKey.Banner}`],
            onClick: () => {
              navigate(`/${NavbarKey.Configuration}/${NavbarKey.Banner}`);
            },
            permissions: [Permission.VIEW_BANNER],
          },
          {
            icon: <ReceiptText size="16" />,
            key: NavbarKey.News,
            label: t('sidebar.news'),
            match: [`/${NavbarKey.News}`],
            onClick: () => {
              navigate(`/${NavbarKey.News}`);
            },
            permissions: [Permission.VIEW_CONFIG_ARTICLE],
          },
        ],
      },
    ];

    return navbar;
  }, [navigate, t]);

  const activeKey = useMemo(
    () =>
      getActiveKey(
        collapseBarMenus.map(item => item.items).flat(),
        location.pathname,
      ),
    [collapseBarMenus, location.pathname],
  ) as NavbarKey;

  return {
    activeKey,
    collapseBarMenus,
  };
};
