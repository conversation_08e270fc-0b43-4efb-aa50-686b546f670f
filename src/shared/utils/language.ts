import i18n from '../../../i18next.config';

/**
 * Get the Accept-Language header value based on current i18next language
 * @returns Accept-Language header value
 */
export const getAcceptLanguageHeader = (): string => {
  const currentLanguage = i18n.language || 'en';
  
  // Map i18next language codes to proper Accept-Language values
  const languageMap: Record<string, string> = {
    'en': 'en-US',
    'fr': 'fr',
  };
  
  return languageMap[currentLanguage] || 'en-US';
};

/**
 * Get the current language code from i18next
 * @returns Current language code ('en' or 'fr')
 */
export const getCurrentLanguage = (): string => {
  return i18n.language || 'en';
};

/**
 * Initialize language change listener for API headers
 * This ensures that API requests use the correct Accept-Language header
 * when the language is changed via i18next
 */
export const initializeLanguageListener = (): void => {
  // Add event listener for language changes
  i18n.on('languageChanged', (lng: string) => {
    console.log(`Language changed to: ${lng}, Accept-Language header will be: ${getAcceptLanguageHeader()}`);
  });
};
