import type { CodeBaseEntity } from '#/api/requests';
import dayjs from 'dayjs';
import {
  DEFAULT_DIVIDE_BY_METER,
  DEFAULT_UINT_DIVIDED_BY_CENT,
} from '../constants';
import { weekdays } from '../components/formField/FormWeekDay';

const STRIPE_PAYMENT_URL: string = import.meta.env.VITE_STRIPE_PAYMENT_URL;

type DateFormat =
  | 'YYYY-MM-DD'
  | 'YYYY-MM-DD HH:mm:ss'
  | 'DD/MM/YYYY, HH:mm'
  | 'HH:mm DD/MM/YYYY'
  | 'DD/MM/YYYY';

/**
 * Formats a given date string according to the specified format.
 *
 * @param {string} date - The date string to be formatted.
 * @param {DateFormat} format - The format to be applied. Defaults to 'YYYY-MM-DD'.
 * @return {string} The formatted date string.
 */

const formatDaytime = (date: string, format: DateFormat = 'YYYY-MM-DD') =>
  dayjs(date).tz().format(format);

const formatDateDefault = (date: string) =>
  dayjs(date).utc().format('DD/MM/YYYY');

/**
 * Formats a given time input into a string representation.
 *
 * @param {number} input - The time input to be formatted.
 * @return {string} The formatted time string.
 */

function formatTime(input: number): string {
  const minutes = Math.floor(input / 60);
  const seconds = input % 60;

  return dayjs().minute(minutes).second(seconds).format('m:ss');
}

interface FormatFromTo {
  from: string;
  to: string;
  fromFormat?: DateFormat;
  toFormat?: DateFormat;
}

/**
 * Formats the 'from' and 'to' values from one format to another and returns the formatted values as a string.
 *
 * @param {FormatFromTo} formatFromTo - An object containing the 'from' and 'to' values and their respective formats.
 * @param {string} formatFromTo.from - The 'from' value to be formatted.
 * @param {string} formatFromTo.to - The 'to' value to be formatted.
 * @param {string} formatFromTo.fromFormat - The format of the 'from' value.
 * @param {string} formatFromTo.toFormat - The format to which the 'to' value should be formatted.
 * @returns {string} - The formatted 'from' and 'to' values concatenated with a hyphen.
 */
const formatFromTo = ({ from, to, fromFormat, toFormat }: FormatFromTo) =>
  `${formatDaytime(from, fromFormat)} - ${formatDaytime(to, toFormat)}`;

const currencyFormat = (
  cost?: number | null,
  isFormatRevenue?: boolean,
  minimumFractionDigits = 2,
  locale = 'en-US',
  currencyUnit = 'USD',
  // eslint-disable-next-line max-params
) => {
  const costValue = cost ?? 0;

  const costDisplay = isFormatRevenue
    ? costValue
    : costValue / DEFAULT_UINT_DIVIDED_BY_CENT;

  return `${costDisplay.toLocaleString(locale, {
    currency: currencyUnit,
    minimumFractionDigits,
    style: 'currency', // decimal digit
  })} CAD`;
};

const distanceFormat = (distance?: number | null, unit = 'km') => {
  const distanceValue = distance ?? 0;

  return `${distanceValue / DEFAULT_DIVIDE_BY_METER} ${unit}`;
};

const formatCodeAndPin = (codes?: CodeBaseEntity[] | null) => {
  if (!codes?.length) return '';
  const codeAndPin: string[] = [];
  codes.forEach(code => {
    codeAndPin.push(`${code.code} | ${code.PIN}`);
  });
  return codeAndPin.join(', ');
};

function formatEmptyProperty<T>(object: T) {
  const tmp = structuredClone(object);

  for (const key in tmp) {
    if (!tmp[key]) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      tmp[key] = null as unknown as any;
    }
  }

  return tmp;
}

/**
 * Normalizes the input string by converting it to lowercase, replacing underscores with spaces, and capitalizing the first letter of each word.
 *
 * @param {string} value - the string to be normalized
 * @return {string} the normalized string
 */
function normalizeEnumString(value?: string) {
  if (!value) return '';
  return value
    .toLocaleLowerCase()
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

function normalizeFirstEnumString(value?: string) {
  if (!value) return '';
  const result: string = value.toLocaleLowerCase().split('_').join(' ');
  return result.charAt(0).toUpperCase() + result.slice(1);
}

function separateVehicleType(value?: string[]) {
  if (!value) return '';
  return value
    .map(type =>
      type
        .toLocaleLowerCase()
        .split('_')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' '),
    )
    .join('/');
}

function getPaymentUrl(paymentId: string) {
  return STRIPE_PAYMENT_URL.concat(`${paymentId}`);
}

function getRepeatDay(repeatDay: number[] | undefined) {
  return weekdays
    .filter(item => repeatDay?.includes(item.value))
    .map(item => item.label.substring(0, 3))
    .join(', ');
}

function formatNumber(value: number) {
  return Number(value).toLocaleString();
}

function separatePricingPlanType(input: string) {
  const parts = input.split('_');
  const capitalized = parts[0].charAt(0) + parts[0].slice(1).toLowerCase();
  return capitalized;
}

export const formatter = {
  currencyFormat,
  distanceFormat,
  formatCodeAndPin,
  formatDateDefault,
  formatDaytime,
  formatEmptyProperty,
  formatFromTo,
  formatNumber,
  formatTime,
  getPaymentUrl,
  getRepeatDay,
  normalizeEnumString,
  normalizeFirstEnumString,
  separatePricingPlanType,
  separateVehicleType,
};
